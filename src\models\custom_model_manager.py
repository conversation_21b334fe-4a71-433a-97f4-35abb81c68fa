#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
自定义模型管理器
自动管理MODEL_NAMES中的自定义模型，包括英文名称生成、添加和删除
"""

import re
import json
import os
from datetime import datetime
from typing import Dict, List, Tuple
import hashlib

class CustomModelManager:
    def __init__(self):
        self.config_file = 'configs/app_config.yaml'
        self.custom_models_file = 'src/core/custom_models.json'
        
    def generate_english_name(self, chinese_name: str) -> str:
        """
        根据中文名称自动生成英文名称，确保唯一性
        """
        # 扩展的中文到英文映射
        translation_map = {
            # 基础词汇
            '检测': 'detection',
            '识别': 'recognition',
            '监控': 'monitoring',
            '告警': 'alert',
            '分析': 'analysis',
            '统计': 'counting',
            '追踪': 'tracking',

            # 安全防护类
            '安全帽': 'helmet',
            '头盔': 'helmet',
            '工作服': 'uniform',
            '反光衣': 'reflective',
            '口罩': 'mask',
            '面罩': 'mask',
            '手套': 'gloves',
            '护目镜': 'goggles',
            '安全鞋': 'safety_shoes',
            '绝缘鞋': 'insulation_shoes',
            '防护鞋': 'protective_shoes',

            # 行为类
            '吸烟': 'smoking',
            '抽烟': 'smoking',
            '打电话': 'phone_call',
            '电话': 'phone',
            '打架': 'fighting',
            '斗殴': 'fighting',
            '睡觉': 'sleeping',
            '睡岗': 'sleeping_on_duty',
            '离岗': 'leaving_post',
            '脱岗': 'leaving_post',
            '摔倒': 'fall',
            '跌倒': 'fall',

            # 物体类
            '车辆': 'vehicle',
            '汽车': 'car',
            '摩托车': 'motorcycle',
            '自行车': 'bicycle',
            '人员': 'person',
            '动物': 'animal',
            '宠物': 'pet',
            '工牌': 'work_badge',
            '证件': 'certificate',

            # 环境类
            '火焰': 'fire',
            '烟雾': 'smoke',
            '水': 'water',
            '温度': 'temperature',
            '湿度': 'humidity',
            '噪音': 'noise',

            # 特殊场景
            '厨房': 'kitchen',
            '工厂': 'factory',
            '办公室': 'office',
            '学校': 'school',
            '医院': 'hospital',
            '商店': 'store',
        }

        # 智能解析中文名称
        english_parts = []
        remaining_name = chinese_name

        # 按长度排序，优先匹配长词汇
        sorted_translations = sorted(translation_map.items(), key=lambda x: len(x[0]), reverse=True)

        for chinese_word, english_word in sorted_translations:
            if chinese_word in remaining_name:
                english_parts.append(english_word)
                remaining_name = remaining_name.replace(chinese_word, '', 1)  # 只替换第一次出现

        # 如果没有匹配到任何词汇，使用基于内容的哈希
        if not english_parts:
            # 为不同的中文名称生成不同的英文名
            hash_obj = hashlib.md5(chinese_name.encode('utf-8'))
            hash_short = hash_obj.hexdigest()[:8]
            english_parts = [f'custom_{hash_short}']

        # 组合英文名称
        english_name = '_'.join(english_parts)

        # 确保名称符合变量命名规范
        english_name = re.sub(r'[^a-zA-Z0-9_]', '_', english_name)
        english_name = english_name.lower().strip('_')

        # 移除连续的下划线
        english_name = re.sub(r'_+', '_', english_name)

        return english_name
    
    def load_custom_models(self) -> Dict:
        """加载已保存的自定义模型"""
        if os.path.exists(self.custom_models_file):
            try:
                with open(self.custom_models_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except:
                pass
        return {}
    
    def save_custom_models(self, models: Dict):
        """保存自定义模型到文件"""
        with open(self.custom_models_file, 'w', encoding='utf-8') as f:
            json.dump(models, f, ensure_ascii=False, indent=2)

    def generate_smart_keywords(self, chinese_name: str) -> List[str]:
        """
        智能生成关键词列表
        """
        keywords = []

        # 添加完整名称
        keywords.append(chinese_name)

        # 常见的检测对象及其同义词
        detection_objects = {
            '安全帽': ['安全帽', '头盔'],
            '工作服': ['工作服', '制服'],
            '反光衣': ['反光衣', '反光服'],
            '口罩': ['口罩', '面罩', '防护面罩'],
            '手套': ['手套', '防护手套'],
            '绝缘鞋': ['绝缘鞋', '安全鞋', '防护鞋', '运动鞋'],
            '护目镜': ['护目镜', '防护眼镜'],
            '安全带': ['安全带', '保险带'],
            '体温': ['体温', '温度'],
            '湿度': ['湿度', '潮湿'],
            '噪音': ['噪音', '声音', '噪声'],
        }

        # 检查是否包含已知的检测对象
        for obj, synonyms in detection_objects.items():
            if obj in chinese_name:
                keywords.extend(synonyms)
                break

        # 如果没有匹配到已知对象，尝试提取核心词
        if len(keywords) == 1:  # 只有原始名称
            # 移除常见后缀
            core_name = chinese_name
            for suffix in ['检测', '识别', '监控', '告警', '分析']:
                if core_name.endswith(suffix):
                    core_word = core_name[:-len(suffix)]
                    if core_word:
                        keywords.append(core_word)
                    break

        # 去重并返回
        return list(set(keywords))
    
    def add_custom_model(self, chinese_name: str, keywords: List[str] = None) -> Tuple[bool, str, str]:
        """
        添加自定义模型
        参数:
        - chinese_name: 中文模型名称
        - keywords: 可选的关键词列表，如果不提供则自动生成
        返回: (成功标志, 英文名称, 消息)
        """
        try:
            # 生成英文名称
            english_name = self.generate_english_name(chinese_name)

            # 如果没有提供关键词，智能生成关键词
            if keywords is None:
                keywords = self.generate_smart_keywords(chinese_name)

            # 检查英文名称冲突（包括custom_models.json和config.py中的MODEL_NAMES）
            custom_models = self.load_custom_models()

            # 导入当前的MODEL_NAMES检查冲突
            try:
                from src.core.config_adapter import MODEL_NAMES
                all_existing_names = set(MODEL_NAMES.keys()) | set(custom_models.keys())
            except:
                all_existing_names = set(custom_models.keys())

            # 如果英文名称冲突，生成唯一名称
            original_name = english_name
            counter = 1
            while english_name in all_existing_names:
                # 使用递增数字而不是时间戳，确保唯一性
                english_name = f"{original_name}_{counter:04d}"
                counter += 1

                # 防止无限循环
                if counter > 9999:
                    # 使用哈希值作为后备方案
                    hash_obj = hashlib.md5(f"{chinese_name}_{datetime.now().isoformat()}".encode('utf-8'))
                    hash_suffix = hash_obj.hexdigest()[:8]
                    english_name = f"{original_name}_{hash_suffix}"
                    break

            # 更新config.py中的MODEL_NAMES
            success = self._update_model_names(english_name, chinese_name, 'add')
            if not success:
                return False, english_name, "更新config.py失败"

            # 更新关键词映射到多个文件
            if keywords:
                self._update_keyword_mappings(chinese_name, keywords, 'add')

            # 保存到自定义模型记录
            custom_models[english_name] = {
                'chinese_name': chinese_name,
                'keywords': keywords or [],
                'created_at': datetime.now().isoformat(),
                'auto_generated': True
            }
            self.save_custom_models(custom_models)

            return True, english_name, f"成功添加自定义模型: {chinese_name} -> {english_name} (关键词: {', '.join(keywords)})"

        except Exception as e:
            return False, "", f"添加失败: {str(e)}"
    
    def remove_custom_model(self, english_name: str) -> Tuple[bool, str]:
        """
        删除自定义模型
        返回: (成功标志, 消息)
        """
        try:
            # 从自定义模型记录中获取信息
            custom_models = self.load_custom_models()
            if english_name not in custom_models:
                return False, "模型不存在"
            
            model_info = custom_models[english_name]
            chinese_name = model_info['chinese_name']
            keywords = model_info.get('keywords', [])
            
            # 从config.py中移除
            success = self._update_model_names(english_name, chinese_name, 'remove')
            if not success:
                return False, "更新config.py失败"
            
            # 从关键词映射中移除
            if keywords:
                self._update_keyword_mappings(chinese_name, keywords, 'remove')
            
            # 从自定义模型记录中移除
            del custom_models[english_name]
            self.save_custom_models(custom_models)
            
            return True, f"成功删除自定义模型: {chinese_name}"
            
        except Exception as e:
            return False, f"删除失败: {str(e)}"
    
    def _update_model_names(self, english_name: str, chinese_name: str, action: str) -> bool:
        """更新YAML配置文件中的MODEL_NAMES"""
        try:
            import yaml
            with open(self.config_file, 'r', encoding='utf-8') as f:
                config_data = yaml.safe_load(f)

            if action == 'add':
                # 添加到YAML配置的model_names部分
                if 'models' not in config_data:
                    config_data['models'] = {}
                if 'model_names' not in config_data['models']:
                    config_data['models']['model_names'] = {}

                config_data['models']['model_names'][english_name] = chinese_name

            elif action == 'remove':
                # 从YAML配置中移除
                if ('models' in config_data and
                    'model_names' in config_data['models'] and
                    english_name in config_data['models']['model_names']):
                    del config_data['models']['model_names'][english_name]

            # 保存更新后的YAML文件
            with open(self.config_file, 'w', encoding='utf-8') as f:
                yaml.dump(config_data, f, default_flow_style=False, allow_unicode=True, indent=2)

            return True

        except Exception as e:
            print(f"更新MODEL_NAMES失败: {e}")
            return False
    
    def _update_keyword_mappings(self, chinese_name: str, keywords: List[str], action: str):
        """更新关键词映射（在multi_modal_analyzer.py和alert_statistics.py中）"""
        files_to_update = ['multi_modal_analyzer.py', 'alert_statistics.py']
        
        for file_path in files_to_update:
            if not os.path.exists(file_path):
                continue
                
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                if action == 'add':
                    # 在安全防护类后添加关键词
                    for keyword in keywords:
                        if file_path == 'multi_modal_analyzer.py':
                            # 多行格式
                            pattern = r'(# 安全防护类\n[^#]*?)(\n\s*# [^#]*类)'
                            replacement = rf'\1            "{keyword}": "{chinese_name}",\n\2'
                        else:
                            # 单行格式
                            pattern = r'(# 安全防护类\n[^#]*?)(\n\s*# [^#]*类)'
                            replacement = rf'\1        "{keyword}": "{chinese_name}", \2'
                        
                        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
                
                elif action == 'remove':
                    # 移除关键词映射
                    for keyword in keywords:
                        patterns = [
                            rf'\s*"{keyword}":\s*"{chinese_name}",?\s*\n',
                            rf'\s*"{keyword}":\s*"{chinese_name}",?\s*'
                        ]
                        for pattern in patterns:
                            content = re.sub(pattern, '', content)
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                    
            except Exception as e:
                print(f"更新{file_path}失败: {e}")
    
    def list_custom_models(self) -> Dict:
        """列出所有自定义模型"""
        return self.load_custom_models()
    
    def get_model_info(self, english_name: str) -> Dict:
        """获取指定模型的信息"""
        custom_models = self.load_custom_models()
        return custom_models.get(english_name, {})

# 全局实例
model_manager = CustomModelManager()

# 便捷函数
def add_custom_model(chinese_name: str, keywords: List[str] = None) -> Tuple[bool, str, str]:
    """添加自定义模型的便捷函数"""
    return model_manager.add_custom_model(chinese_name, keywords)

def remove_custom_model(english_name: str) -> Tuple[bool, str]:
    """删除自定义模型的便捷函数"""
    return model_manager.remove_custom_model(english_name)

def list_custom_models() -> Dict:
    """列出自定义模型的便捷函数"""
    return model_manager.list_custom_models()
