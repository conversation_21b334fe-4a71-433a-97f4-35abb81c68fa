# AI安全监控系统 - 项目结构（更新版）

## 📁 项目目录结构

```
aiWatchdog0703/
├── 📁 configs/                    # 配置文件
│   ├── app_config.yaml            # 应用配置
│   └── voice_config.yaml          # 语音配置
│
├── 📁 data/                       # 数据文件
│   ├── 📁 archive/                # 归档数据
│   ├── 📁 uploads/                # 上传文件
│   ├── 📁 video_warning/          # 视频警报
│   ├── 📁 voice/                  # 语音数据
│   ├── output_frame.jpg           # 输出帧
│   └── video_histroy_info.txt     # 视频历史信息
│
├── 📁 docs/                       # 文档目录
│   ├── 📄 项目文档/
│   │   ├── AI安全监控软件APP开发方案.md
│   │   ├── PROJECT_STRUCTURE.md
│   │   ├── PROJECT_STRUCTURE_UPDATED.md
│   │   └── README_EN.md
│   │
│   ├── 📄 功能实现文档/
│   │   ├── ANALYSIS_CONTROL_IMPLEMENTATION.md
│   │   ├── CONVERSATION_DISPLAY_IMPLEMENTATION.md
│   │   ├── CONVERSATION_DISPLAY_SOLUTION.md
│   │   └── MIGRATION_GUIDE.md
│   │
│   ├── 📄 语音功能文档/
│   │   ├── FUNASR_FINAL_GUIDE.md
│   │   ├── FUNASR_INTEGRATION_SUMMARY.md
│   │   ├── FUNASR_SETUP_GUIDE.md
│   │   ├── FUNASR_TROUBLESHOOTING.md
│   │   ├── VOICE_ASSISTANT_ENHANCEMENT_SUMMARY.md
│   │   ├── VOICE_ASSISTANT_README.md
│   │   └── VOICE_RECOGNITION_FIX.md
│   │
│   ├── 📄 界面优化文档/
│   │   ├── DUPLICATE_ID_FIX.md
│   │   ├── FIXED_HEIGHT_SUMMARY.md
│   │   ├── HEIGHT_ALIGNMENT_FIX.md
│   │   ├── HISTORY_BUTTON_LAYOUT.md
│   │   ├── LAYOUT_ADJUSTMENTS.md
│   │   ├── VOICE_BUTTON_LAYOUT.md
│   │   ├── VOICE_VIDEO_HEIGHT_MATCH.md
│   │   └── WEB_CODE_SEPARATION.md
│   │
│   └── 📄 维护文档/
│       ├── CLEANUP_SUMMARY.md
│       ├── CONFIG_USAGE.md
│       └── CONFIG_YAML_SYNC_REPORT.md
│
├── 📁 logs/                       # 日志文件
│   ├── app.log                    # 应用日志
│   ├── code.log                   # 代码日志
│   └── voice_assistant.log        # 语音助手日志
│
├── 📁 src/                        # 源代码目录
│   ├── 📁 api/                    # API接口
│   ├── 📁 core/                   # 核心功能
│   ├── 📁 models/                 # 数据模型
│   ├── 📁 video/                  # 视频处理
│   ├── 📁 voice/                  # 语音处理
│   └── __init__.py
│
├── 📁 test/                       # 测试文件目录
│   ├── 📄 FunASR测试/
│   │   ├── debug_funasr.py
│   │   ├── funasr_compatibility_test.py
│   │   ├── test_funasr.py
│   │   ├── test_funasr_correct_protocol.py
│   │   ├── test_funasr_enhanced.py
│   │   ├── test_funasr_final.py
│   │   ├── test_funasr_parameters.py
│   │   └── test_funasr_protocol.py
│   │
│   ├── 📄 语音功能测试/
│   │   ├── test_conversation_fix.py
│   │   ├── test_final_solution.py
│   │   ├── test_voice_api.py
│   │   └── test_voice_recognition.py
│   │
│   ├── 📄 Web测试/
│   │   └── validate_html.py
│   │
│   └── 📄 测试视频/
│       ├── test.mp4
│       ├── test1.mp4
│       ├── test2.mp4
│       └── test3.mp4
│
├── 📁 temp/                       # 临时文件目录
│   └── temp_command_*.wav         # 临时音频文件
│
├── 📁 utils/                      # 工具函数
│   ├── alert_statistics.py       # 警报统计
│   ├── utility.py                # 通用工具
│   └── __init__.py
│
├── 📁 voice/                      # 语音文件
│   ├── 📁 tts_cache/             # TTS缓存
│   └── tts_*.mp3                 # TTS音频文件
│
├── 📁 web/                        # Web前端
│   ├── 📁 static/                # 静态资源
│   │   ├── 📁 assets/            # 资源文件
│   │   ├── 📁 css/               # 样式文件
│   │   │   └── main.css          # 主样式文件
│   │   ├── 📁 js/                # JavaScript文件
│   │   │   ├── advanced-settings.js  # 高级设置
│   │   │   ├── alerts.js         # 警报处理
│   │   │   ├── chat.js           # 聊天功能
│   │   │   ├── particles.js      # 粒子效果
│   │   │   ├── utils.js          # 工具函数
│   │   │   ├── video-config.js   # 视频配置
│   │   │   ├── video.js          # 视频处理
│   │   │   ├── voice-assistant.js # 语音助手
│   │   │   └── voice.js          # 语音功能
│   │   └── alert.mp3             # 警报音频
│   │
│   └── 📁 templates/             # 模板文件
│       ├── 📁 test/              # 测试模板
│       │   └── test.html         # 功能测试页面
│       ├── config.html           # 配置页面
│       ├── index.html            # 主页面
│       └── voice_assistant.html  # 语音助手页面
│
├── 📄 主要文件/
│   ├── main.py                   # 主程序入口
│   ├── requirements.txt          # 依赖包列表
│   ├── simple_test_server.py     # 简单测试服务器
│   └── README.md                 # 项目说明
│
└── 📄 其他文件/
    ├── output_frame.jpg          # 输出帧图片
    └── __pycache__/              # Python缓存
```

## 📋 文件归类说明

### 🗂️ **新增文件归类**

#### **1. 文档文件 → `docs/`**
- `VOICE_RECOGNITION_FIX.md` → `docs/VOICE_RECOGNITION_FIX.md`
- `WEB_CODE_SEPARATION.md` → `docs/WEB_CODE_SEPARATION.md`

#### **2. 测试文件 → `test/`**
- `test_conversation_fix.py` → `test/test_conversation_fix.py`
- `test_final_solution.py` → `test/test_final_solution.py`
- `test_voice_api.py` → `test/test_voice_api.py`
- `test_voice_recognition.py` → `test/test_voice_recognition.py`

#### **3. 临时文件 → `temp/`**
- `temp_command_*.wav` → `temp/temp_command_*.wav`

#### **4. Web测试文件 → `web/templates/test/`**
- `test.html` → `web/templates/test/test.html`

#### **5. Web静态资源 → `web/static/`**
- CSS文件 → `web/static/css/`
- JavaScript文件 → `web/static/js/`

### 🎯 **归类原则**

1. **按功能分类**: 相同功能的文件放在同一目录
2. **按文件类型分类**: 文档、测试、临时文件分别归类
3. **保持层次清晰**: 避免目录层次过深
4. **便于维护**: 文件位置符合开发习惯

### 📊 **目录统计**

| 目录 | 文件数量 | 主要内容 |
|------|----------|----------|
| `docs/` | 25+ | 项目文档、功能说明 |
| `src/` | 50+ | 核心源代码 |
| `test/` | 15+ | 测试脚本 |
| `web/` | 15+ | 前端资源 |
| `temp/` | 15+ | 临时文件 |
| `voice/` | 10+ | 语音文件 |
| `data/` | 多个 | 数据文件 |

### 🔧 **维护建议**

1. **定期清理**: 清理temp目录下的临时文件
2. **文档更新**: 及时更新docs目录下的文档
3. **测试管理**: 定期运行test目录下的测试脚本
4. **版本控制**: 使用.gitignore忽略临时文件和缓存

## 🚀 **下一步优化**

1. **创建子目录**: 在docs下按功能创建子目录
2. **添加索引**: 为每个目录创建README文件
3. **自动化脚本**: 创建文件整理自动化脚本
4. **配置管理**: 统一配置文件管理
