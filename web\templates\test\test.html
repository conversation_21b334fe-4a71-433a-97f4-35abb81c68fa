<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>代码分离测试页面</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <link href="/static/css/main.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white">
  <div class="container mx-auto p-8">
    <h1 class="text-3xl font-bold mb-8 text-center">AI安全监控系统 - 代码分离测试</h1>
    
    <!-- 测试区域 -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      
      <!-- 视频测试区域 -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">视频流测试</h2>
        <div id="video-container" class="mb-4">
          <img id="videoFeed" alt="视频流" class="w-full h-auto">
        </div>
        <div class="flex items-center">
          <span id="videoStatus" class="inline-block w-3 h-3 rounded-full bg-red-500 mr-2"></span>
          <span id="videoSourceDisplay" class="text-sm text-gray-400">未连接</span>
        </div>
      </div>

      <!-- 警报测试区域 -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">警报系统测试</h2>
        <div class="mb-4">
          <span class="text-sm text-gray-400">警报数量: </span>
          <span id="alertCount" class="text-lg font-bold text-red-400">0</span>
        </div>
        <div id="alerts-list" class="space-y-2 max-h-64 overflow-y-auto">
          <!-- 警报将在这里显示 -->
        </div>
      </div>

      <!-- 语音播报测试区域 -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">语音播报测试</h2>
        <div class="mb-4">
          <label class="flex items-center">
            <input type="checkbox" id="voiceControlToggle" checked class="mr-2">
            <span>启用语音播报</span>
          </label>
        </div>
        <button onclick="testVoice()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
          测试语音播报
        </button>
      </div>

      <!-- 通知测试区域 -->
      <div class="bg-gray-800 p-6 rounded-lg">
        <h2 class="text-xl font-semibold mb-4">通知系统测试</h2>
        <div class="space-y-2">
          <button onclick="showNotification('这是一个成功消息', 'success')" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded mr-2">
            成功通知
          </button>
          <button onclick="showNotification('这是一个错误消息', 'error')" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded mr-2">
            错误通知
          </button>
          <button onclick="showNotification('这是一个信息消息', 'info')" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
            信息通知
          </button>
        </div>
      </div>

    </div>

    <!-- 粒子效果背景 -->
    <div id="particles-js"></div>
  </div>

  <!-- 脚本引入 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
  
  <!-- 新的模块化脚本 -->
  <script src="/static/js/particles.js"></script>
  <script src="/static/js/voice.js"></script>
  <script src="/static/js/video.js"></script>
  <script src="/static/js/alerts.js"></script>
  <script src="/static/js/utils.js"></script>

  <script>
    // 测试函数
    function testVoice() {
      voiceQueue.addToQueue('这是一个语音播报测试消息');
    }

    // 模拟警报数据
    function simulateAlert() {
      const alertData = {
        alert: '检测到异常行为',
        alert_type: '入侵检测',
        alert_level: 'warning',
        location: '测试区域',
        timestamp: new Date().toISOString()
      };

      // 模拟WebSocket消息
      const event = {
        data: JSON.stringify(alertData)
      };

      // 手动调用警报处理函数
      if (typeof alertSocket !== 'undefined' && alertSocket.onmessage) {
        alertSocket.onmessage(event);
      }
    }

    // 添加模拟警报按钮
    document.addEventListener('DOMContentLoaded', function() {
      const alertSection = document.querySelector('#alerts-list').parentElement;
      const simulateBtn = document.createElement('button');
      simulateBtn.textContent = '模拟警报';
      simulateBtn.className = 'bg-yellow-600 hover:bg-yellow-700 px-4 py-2 rounded mt-2';
      simulateBtn.onclick = simulateAlert;
      alertSection.appendChild(simulateBtn);
    });
  </script>
</body>
</html>
