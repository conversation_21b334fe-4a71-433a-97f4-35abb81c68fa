
<!-- ai_security_monitor/frontend/login.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>AI安全监控系统 - 登录</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
      min-height: 100vh;
    }
    .login-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }
    .login-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
    }
    #particles-js {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    .input-field {
      background: rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }
    .input-field:focus {
      background: rgba(255, 255, 255, 0.15);
    }
  </style>
</head>
<body class="text-gray-200">
  <div id="particles-js"></div>
  
  <div class="container mx-auto px-4 py-8 flex items-center justify-center min-h-screen">
    <div class="login-card rounded-xl p-8 w-full max-w-md">
      <div class="text-center mb-8">
        <div class="w-20 h-20 mx-auto mb-4 bg-gradient-to-r from-blue-500 to-purple-600 rounded-full flex items-center justify-center">
          <i class="fas fa-shield-alt text-3xl text-white"></i>
        </div>
        <h1 class="text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
          AI安全监控系统
        </h1>
        <p class="text-gray-400">请登录您的账户</p>
      </div>

      <form id="loginForm" class="space-y-6">
        <div>
          <label for="username" class="block text-sm font-medium mb-2">用户名</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-user text-gray-500"></i>
            </div>
            <input type="text" id="username" name="username" required 
                   class="input-field w-full pl-10 pr-3 py-3 rounded-md border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                   placeholder="输入用户名">
          </div>
        </div>

        <div>
          <label for="password" class="block text-sm font-medium mb-2">密码</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <i class="fas fa-lock text-gray-500"></i>
            </div>
            <input type="password" id="password" name="password" required 
                   class="input-field w-full pl-10 pr-3 py-3 rounded-md border border-gray-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent" 
                   placeholder="输入密码">
          </div>
        </div>

        <div class="flex items-center justify-between">
          <div class="flex items-center">
            <input id="remember" name="remember" type="checkbox" 
                   class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-700 rounded">
            <label for="remember" class="ml-2 block text-sm text-gray-400">记住我</label>
          </div>
          <a href="#" class="text-sm text-blue-400 hover:text-blue-300">忘记密码?</a>
        </div>

        <div>
          <button type="submit" 
                  class="w-full px-4 py-3 bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700 rounded-md font-medium transition duration-300">
            登录
          </button>
        </div>

        <div class="text-center text-sm text-gray-400">
          还没有账户? <a href="#" class="text-blue-400 hover:text-blue-300">联系管理员</a>
        </div>
      </form>
    </div>
  </div>

  <script>
    // 粒子效果初始化
    document.addEventListener('DOMContentLoaded', function() {
      particlesJS('particles-js', {
        particles: {
          number: { value: 60, density: { enable: true, value_area: 800 } },
          color: { value: "#3b82f6" },
          shape: { type: "circle" },
          opacity: { value: 0.5, random: true },
          size: { value: 3, random: true },
          line_linked: { enable: true, distance: 150, color: "#3b82f6", opacity: 0.4, width: 1 },
          move: { enable: true, speed: 2, direction: "none", random: true, straight: false, out_mode: "out" }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: { enable: true, mode: "grab" },
            onclick: { enable: true, mode: "push" }
          }
        }
      });

      // 登录表单提交
      document.getElementById('loginForm').addEventListener('submit', function(e) {
        e.preventDefault();
        const username = document.getElementById('username').value;
        const password = document.getElementById('password').value;
        
        // 验证用户名和密码
        if (username === 'admin' && password === '123') {
          window.location.href = 'index.html';
        } else {
          alert('用户名或密码错误');
        }
      });
    });
  </script>
</body>
</html>
