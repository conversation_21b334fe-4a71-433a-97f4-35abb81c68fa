#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR最终测试脚本
功能：使用正确的协议测试FunASR语音识别
"""

import asyncio
import json
import logging
import wave
import base64
import sounddevice as sd
import numpy as np
import os

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FunASR配置
FUNASR_WS_URL = "ws://192.168.3.95:10096/"
FUNASR_TIMEOUT = 10
SAMPLE_RATE = 16000

class FunASRFinalTester:
    """FunASR最终测试器"""
    
    def __init__(self):
        self.ws_url = FUNASR_WS_URL
        self.timeout = FUNASR_TIMEOUT
    
    def print_banner(self):
        """打印测试横幅"""
        print("\n" + "=" * 60)
        print("🎙️ FunASR语音识别最终测试")
        print("=" * 60)
        print(f"服务器地址: {self.ws_url}")
        print(f"使用正确的流式协议")
        print("=" * 60)
    
    def record_audio(self, duration=3):
        """录制音频"""
        print(f"\n🎤 开始录制音频 ({duration}秒)...")
        print("请对着麦克风说话...")
        
        try:
            # 录制音频
            audio_data = sd.rec(
                int(duration * SAMPLE_RATE),
                samplerate=SAMPLE_RATE,
                channels=1,
                dtype='int16'
            )
            sd.wait()  # 等待录制完成
            
            print("✅ 录制完成")
            return audio_data
            
        except Exception as e:
            print(f"❌ 录制失败: {e}")
            return None
    
    def save_audio_to_wav(self, audio_data, filename="test_audio_final.wav"):
        """保存音频为WAV文件"""
        try:
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)  # 单声道
                wf.setsampwidth(2)  # 16位
                wf.setframerate(SAMPLE_RATE)
                wf.writeframes(audio_data.tobytes())
            
            print(f"✅ 音频已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存音频失败: {e}")
            return None
    
    async def funasr_recognize(self, audio_file):
        """使用正确的协议进行FunASR识别"""
        print(f"\n🧠 使用FunASR识别: {audio_file}")
        
        try:
            # 读取音频文件
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
            
            # 获取文件名（不含路径和扩展名）
            wav_name = os.path.splitext(os.path.basename(audio_file))[0]
            
            # 连接FunASR WebSocket服务
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:
                
                # 1. 发送开始信号和配置
                start_msg = json.dumps({
                    "mode": "offline",
                    "chunk_size": [5, 10, 5],
                    "chunk_interval": 10,
                    "wav_name": wav_name
                })
                await websocket.send(start_msg)
                print(f"📤 发送开始信号: {start_msg}")
                
                # 2. 转换为PCM格式并发送音频数据
                pcm_data = self._convert_to_pcm(audio_data)
                audio_msg = json.dumps({
                    "audio": base64.b64encode(pcm_data).decode(),
                    "is_speaking": True,
                    "wav_format": "pcm"
                })
                await websocket.send(audio_msg)
                print("📤 发送音频数据")
                
                # 3. 发送结束信号
                end_msg = json.dumps({
                    "is_speaking": False
                })
                await websocket.send(end_msg)
                print("📤 发送结束信号")
                
                # 4. 接收识别结果
                responses = []
                final_result = None
                
                try:
                    while True:
                        response = await asyncio.wait_for(
                            websocket.recv(),
                            timeout=5
                        )
                        responses.append(response)
                        print(f"📥 收到响应: {response}")
                        
                        # 解析响应
                        if isinstance(response, str):
                            try:
                                result_data = json.loads(response)
                                text = result_data.get('text', '').strip()
                                is_final = result_data.get('is_final', False)
                                
                                if text and is_final:
                                    final_result = text
                                    print(f"✅ 最终识别结果: {text}")
                                    break
                                elif text:
                                    print(f"🔄 中间结果: {text}")
                                    
                            except json.JSONDecodeError:
                                print(f"⚠️ 非JSON响应: {response}")
                        
                except asyncio.TimeoutError:
                    print(f"⏰ 接收完成，共收到 {len(responses)} 个响应")
                
                # 如果没有找到最终结果，检查所有响应
                if not final_result:
                    for response in responses:
                        if isinstance(response, str):
                            try:
                                result_data = json.loads(response)
                                text = result_data.get('text', '').strip()
                                if text:
                                    final_result = text
                                    print(f"✅ 从响应中提取结果: {text}")
                                    break
                            except json.JSONDecodeError:
                                continue
                
                return final_result
                
        except Exception as e:
            print(f"❌ FunASR识别失败: {e}")
            return None

    def _convert_to_pcm(self, audio_data):
        """将音频数据转换为PCM格式"""
        try:
            import wave
            import io

            # 如果是WAV文件，提取PCM数据
            if audio_data.startswith(b'RIFF'):
                # 这是WAV文件，提取PCM数据
                with io.BytesIO(audio_data) as wav_io:
                    with wave.open(wav_io, 'rb') as wav_file:
                        # 读取PCM数据
                        pcm_data = wav_file.readframes(wav_file.getnframes())
                        print(f"✅ 转换为PCM格式，数据长度: {len(pcm_data)} 字节")
                        return pcm_data
            else:
                # 假设已经是PCM数据
                print("⚠️ 数据不是WAV格式，直接使用")
                return audio_data

        except Exception as e:
            print(f"❌ PCM转换失败，使用原始数据: {e}")
            return audio_data
    
    async def run_full_test(self):
        """运行完整测试"""
        self.print_banner()
        
        if not WEBSOCKETS_AVAILABLE:
            print("❌ websockets库不可用，无法进行测试")
            return False
        
        # 1. 录制音频
        audio_data = self.record_audio(duration=3)
        if audio_data is None:
            print("❌ 录制失败，无法继续测试")
            return False
        
        # 2. 保存音频文件
        audio_file = self.save_audio_to_wav(audio_data)
        if audio_file is None:
            print("❌ 保存音频失败，无法继续测试")
            return False
        
        # 3. 使用FunASR识别
        result = await self.funasr_recognize(audio_file)
        
        if result:
            print(f"\n🎉 识别成功: {result}")
        else:
            print("\n❌ 识别失败或结果为空")
        
        print("\n" + "=" * 60)
        print("🎉 测试完成")
        print("=" * 60)
        
        return result is not None

async def main():
    """主函数"""
    tester = FunASRFinalTester()
    
    try:
        success = await tester.run_full_test()
        if success:
            print("\n✅ FunASR集成测试成功！")
            print("现在可以在语音助手中使用FunASR了。")
        else:
            print("\n❌ FunASR集成测试失败")
            print("请检查FunASR服务器状态和网络连接。")
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        # Python 3.6兼容性
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
