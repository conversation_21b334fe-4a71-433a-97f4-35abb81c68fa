try:
    from pymilvus import connections, Collection
    PYMILVUS_AVAILABLE = True
except ImportError:
    PYMILVUS_AVAILABLE = False
    print("Warning: pymilvus not available, falling back to file-based statistics")

from datetime import datetime, timedelta
from src.core.config_adapter import RAGConfig, MODEL_NAMES

def infer_detection_type_from_text(text_content):
    """
    从文本内容中推断具体的异常检测类型
    """
    if not text_content:
        return "通用异常检测"

    # 关键词映射，根据异常描述内容推断检测类型
    keyword_mapping = {
        # 安全防护类
        "安全帽": "安全帽检测", "头盔": "安全帽检测",
        "工作服": "工作服检测", "反光衣": "反光衣识别",
        "厨师帽": "厨师帽识别",
        "口罩": "口罩监测", "面罩": "口罩监测", "防护面罩": "口罩监测",
        "绝缘鞋": "绝缘鞋检测", "安全鞋": "绝缘鞋检测", "防护鞋": "绝缘鞋检测", "运动鞋": "绝缘鞋检测",# 行为检测类
        "吸烟": "吸烟检测", "抽烟": "吸烟检测",
        "打电话": "打电话检测", "电话": "打电话检测",
        "打架": "打架斗殴检测", "斗殴": "打架斗殴检测", "冲突": "打架斗殴检测",
        "武器": "持械识别", "刀具": "持械识别",
        "睡觉": "睡岗检测", "睡岗": "睡岗检测",
        "离岗": "离岗检测", "脱岗": "离岗检测",

        # 区域监控类
        "入侵": "入侵检测", "闯入": "入侵检测",
        "周界": "周界入侵检测", "围栏": "电子围栏", "徘徊": "徘徊检测",

        # 计数统计类
        "人数": "计数检测", "计数": "计数检测", "越线": "越线计数",
        "超限": "人员超限检测", "拥挤": "人员超限检测",

        # 识别检测类
        "人脸": "人脸识别", "面部": "人脸识别", "车牌": "车牌识别",
        "陌生人": "陌生人告警",

        # 环境安全类
        "火焰": "火焰烟火检测", "烟火": "火焰烟火检测",
        "明火": "明火检测", "火": "明火检测", "烟尘": "烟尘检测",
        "消防通道": "消防通道阻塞识别", "通道阻塞": "消防通道阻塞识别",

        # 意外事件类
        "跌倒": "跌倒检测", "摔倒": "跌倒检测", "倒地": "跌倒检测",
    }

    # 遍历关键词映射，找到匹配的检测类型
    for keyword, detection_type in keyword_mapping.items():
        if keyword in text_content:
            return detection_type

    # 如果没有匹配到特定类型，返回通用检测
    return "通用异常检测"

def query_recent_alerts():
    if not PYMILVUS_AVAILABLE:
        print("Pymilvus not available, returning empty results")
        return [], []

    try:
        # 连接Milvus
        connections.connect(
            alias="default",
            host=RAGConfig.MILVUS_HOST,
            port=RAGConfig.MILVUS_PORT,
            user=RAGConfig.MILVUS_USER,
            password=RAGConfig.MILVUS_PASSWORD
        )

        # 获取collection对象
        collection = Collection("table_video_table")
        collection.load()

        # 生成YYYY/MMDD格式时间字符串
        end_date = datetime.now().strftime('%Y/%m/%d')
        start_date = (datetime.now() - timedelta(days=7)).strftime('%Y/%m/%d')

        # 构建字符串比较表达式
        query_expr = f"timestamp >= '{start_date}' && timestamp <= '{end_date}' && alert_value == 1"

        results = collection.query(
            expr=query_expr,
            output_fields=["alert_type", "text"]
        )
        # print(results)
        # 定义标准分类映射
        category_map = MODEL_NAMES
        # 创建反向映射（中文到英文）
        reverse_map = {v: k for k, v in category_map.items()}

        # 初始化计数器
        type_counter = {v: 0 for v in category_map.values()}

        for item in results:
            alert_type = item.get('alert_type')
            text_content = item.get('text', '')
            # print(f"处理alert_type: {alert_type}")

            # 处理英文格式的alert_type
            if alert_type in category_map:
                mapped_type = category_map[alert_type]
                type_counter[mapped_type] += 1
                # print(f"英文格式匹配: {alert_type} -> {mapped_type}")
            # 处理中文格式的alert_type
            elif alert_type in reverse_map:
                # 如果是中文格式，直接使用
                type_counter[alert_type] += 1
                # print(f"中文格式匹配: {alert_type}")
            # 处理可能的中文格式（在category_map的values中）
            elif alert_type in category_map.values():
                type_counter[alert_type] += 1
                # print(f"中文值匹配: {alert_type}")
            else:
                # 处理特殊情况：多模型检测
                if alert_type == "多模型检测":
                    # 动态推断具体的异常类型
                    inferred_type = infer_detection_type_from_text(text_content)
                    if inferred_type not in type_counter:
                        type_counter[inferred_type] = 0
                    type_counter[inferred_type] += 1
                    # print(f"多模型检测推断为: {inferred_type}")
                else:
                    # print(f"未匹配的alert_type: {alert_type}")
                    pass
                # 可选：添加到"其他"类别
                # else:
                #     if '其他' not in type_counter:
                #         type_counter['其他'] = 0
                #     type_counter['其他'] += 1
        # print(type_counter)
        # 按前端需要的顺序返回数据，只返回有数据的异常类型
        labels = []
        data = []

        # 首先添加标准映射中的类型
        for alert_type, chinese_name in category_map.items():
            count = type_counter.get(chinese_name, 0)
            if count > 0:  # 只添加有数据的异常类型
                labels.append(chinese_name)
                data.append(count)

        # 然后添加特殊类型（如"多模型检测"）
        for alert_type, count in type_counter.items():
            if count > 0 and alert_type not in category_map.values():
                labels.append(alert_type)
                data.append(count)

        return labels, data

    except Exception as e:
        print(f"查询失败：{str(e)}")
        return {'labels': [], 'data': []}

def query_recent_alert_counts():
    if not PYMILVUS_AVAILABLE:
        print("Pymilvus not available, returning empty results")
        return [], []

    connections.connect(
        alias="default",
        host=RAGConfig.MILVUS_HOST,
        port=RAGConfig.MILVUS_PORT,
        user=RAGConfig.MILVUS_USER,
        password=RAGConfig.MILVUS_PASSWORD
    )

    # 获取collection对象
    collection = Collection("table_video_table")
    collection.load()
    
    end_date = datetime.now().strftime('%Y/%m/%d')
    start_date = (datetime.now() - timedelta(days=7)).strftime('%Y/%m/%d')
    query_expr = f"timestamp >= '{start_date}' && timestamp <= '{end_date}' && alert_value == 1"

    timestamp_counter = collection.query(
        expr=query_expr,
        output_fields=["timestamp"]
    )
    results = {}
    for item in timestamp_counter:
        date_str = item.get('timestamp')
        # print(date_str)
        if date_str in results:
            results[date_str] += 1
        else:
            results[date_str] = 1
    # 生成完整日期序列（最近7天）
    date_sequence = [(datetime.now() - timedelta(days=i)).strftime('%Y/%m/%d') for i in range(6, -1, -1)]
    
    # 生成对应日期的告警数量列表
    counts = [results.get(date, 0) for date in date_sequence]
    
    return date_sequence, counts

if __name__ == "__main__":
    print(query_recent_alerts())
    print(query_recent_alert_counts())