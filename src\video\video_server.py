"""智能视频监控系统 (2025.02.26版)
核心功能：
1. 实时视频流采集与缓冲
2. 智能多模态异常检测
3. 视频分段存储与特征归档
4. WebSocket实时警报推送
"""

# 标准库导入
import cv2  # OpenCV视频处理
import asyncio  # 异步IO支持
import json  # JSON数据处理
import argparse  # 解析命令行参数
from datetime import datetime, timedelta  # 时间
from concurrent.futures import ThreadPoolExecutor  # 线程池
from fastapi.websockets import WebSocketState  # WebSocket状态
from collections import deque  # 双端队列
from typing import Optional, Dict, Any  # 数据类型
import numpy as np  # Numpy
import logging  # 记录日志
from src.models.multi_modal_analyzer import MultiModalAnalyzer  # 多模态分析器
import time  # 计时
import uvicorn  # Uvicorn
from multiprocessing import set_start_method  # 进程
from src.core.config_adapter import VideoConfig, ServerConfig, APIConfig, RAGConfig, LOG_CONFIG, ARCHIVE_DIR, update_config  # 配置
from src.core.config_adapter import VIDEO_SOURCE, current_model, MODEL_NAMES  # 全局变量
from src.core.config_loader import config_loader  # YAML配置加载器
import os
import src.core.config_adapter as config  # 导入config适配器以便修改全局变量

print(f"✅ 使用YAML配置: 视频源={VIDEO_SOURCE}, 当前模型={current_model['name']}")
from contextlib import asynccontextmanager
from fastapi.staticfiles import StaticFiles
from fastapi.middleware.cors import CORSMiddleware
from fastapi import FastAPI, WebSocket, WebSocketDisconnect, Request, HTTPException,APIRouter, Response, UploadFile, File # FastAPI
from fastapi.responses import HTMLResponse, FileResponse
from fastapi.responses import JSONResponse
import re
import shutil
from pathlib import Path
from src.api.rag_query import rag_query,init_voice_engine,speech_to_text

# 尝试导入警报统计模块
try:
    from utils.alert_statistics import query_recent_alert_counts,query_recent_alerts
    ALERT_STATS_AVAILABLE = True
except ImportError as e:
    print(f"警报统计不可用: {e}")
    ALERT_STATS_AVAILABLE = False
    # 提供默认实现
    def query_recent_alert_counts():
        return {}
    def query_recent_alerts():
        return []

from src.models.custom_model_manager import add_custom_model, remove_custom_model, list_custom_models

# 语音助手集成
try:
    from src.voice.voice_api import voice_router
    from src.voice.voice_controller import trigger_voice_alert
    VOICE_AVAILABLE = True
    print("✅ 语音助手模块加载成功")
except ImportError as e:
    print(f"⚠️ 语音助手模块未安装: {e}")
    print("运行 python install_voice_dependencies.py 安装语音功能")
    VOICE_AVAILABLE = False
    voice_router = None
    trigger_voice_alert = None
if not os.path.exists('./data/video_warning'):
    os.makedirs('./data/video_warning')
if not os.path.exists('./data/uploads'):
    os.makedirs('./data/uploads')
# 配置日志记录（使用config中的日志配置）
logging.basicConfig(
    level=LOG_CONFIG['level'],
    format=LOG_CONFIG['format'],
    handlers=[logging.FileHandler(LOG_CONFIG['handlers'][0]['filename'], encoding='utf-8'), logging.StreamHandler()]
)


# 解析命令行参数
def parse_args():
    """解析命令行参数
    Returns:
        dict: 包含有效参数的字典，过滤掉未提供的参数
    """
    parser = argparse.ArgumentParser(description='智能视频监控系统')
    parser.add_argument('--video_source', type=str, help='视频源路径')
    parser.add_argument('--video_interval', type=int, help='视频分段时长(秒)')
    parser.add_argument('--analysis_interval', type=int, help='分析间隔(秒)')
    parser.add_argument('--buffer_duration', type=int, help='滑窗分析时长')
    parser.add_argument('--ws_retry_interval', type=int, help='WebSocket重连间隔(秒)')
    parser.add_argument('--max_ws_queue', type=int, help='消息队列最大容量')
    parser.add_argument('--jpeg_quality', type=int, help='JPEG压缩质量')
    parser.add_argument('--host', type=str, help='服务器主机地址')
    parser.add_argument('--port', type=int, help='服务器端口')
    parser.add_argument('--reload', type=bool, help='是否启用热重载')
    parser.add_argument('--workers', type=int, help='工作进程数')

    args = parser.parse_args()
    return {k: v for k, v in vars(args).items() if v is not None}


# 更新配置
args = parse_args()
update_config(args)

# 初始化视频源
video_source = VIDEO_SOURCE
print(f"使用视频源: {video_source}")
print(f"当前检测模型: {current_model['name']} ({MODEL_NAMES.get(current_model['name'], '未知模型')})")
cap = cv2.VideoCapture(video_source)  # 读取视频

# 检查视频是否成功打开
if not cap.isOpened():
    print(f"错误: 无法打开视频源 {video_source}")
    cap.release()
    # 使用默认值
    width, height, fps = 640, 480, 25.0
else:
    # 尝试读取几帧来获取视频信息
    frame = None
    for i in range(5):
        ret, frame = cap.read()
        if ret and frame is not None:
            break

    if frame is not None:
        width = frame.shape[1]
        height = frame.shape[0]
        fps = cap.get(cv2.CAP_PROP_FPS)
    else:
        print(f"警告: 无法从视频源读取帧 {video_source}")
        # 使用默认值
        width, height, fps = 640, 480, 25.0

    cv2.destroyAllWindows()
    cap.release()

print("fps", fps)


class VideoProcessor:
    """视频流处理核心类
    功能：
    - 视频流采集与缓冲
    - 帧率控制
    - 异步分析任务触发
    - 动态视频源切换
    """

    def __init__(self, video_source):
        """初始化视频处理器
        Args:
            video_source (str): 视频源路径（RTSP/文件路径）
        """
        self.video_source = video_source
        self.cap = cv2.VideoCapture(video_source)
        # 尝试读取一帧来验证视频源
        ret, frame = self.cap.read()
        if not ret or frame is None:
            print(f"警告: 无法从视频源读取帧: {video_source}")
        self.buffer = deque(maxlen=int(fps * VideoConfig.BUFFER_DURATION))
        self.executor = ThreadPoolExecutor()
        self.analyzer = MultiModalAnalyzer()
        self.last_analysis = datetime.now().timestamp()
        self._running = False
        self._switching_source = False  # 视频源切换标志
        self.lock = asyncio.Lock()
        self.frame_queue = asyncio.Queue()  # 添加一个异步队列用于缓存帧
        self.start_push_queue = 0  # 视频流推送开关标志

    @property
    def fps(self) -> float:
        """动态获取视频帧率
        返回默认30fps当无法获取有效帧率时"""
        return self.cap.get(cv2.CAP_PROP_FPS) or 30.0

    async def switch_video_source(self, new_source: str):
        """动态切换视频源
        Args:
            new_source (str): 新的视频源路径
        """
        try:
            # 验证新视频源
            test_cap = cv2.VideoCapture(new_source)
            if not test_cap.isOpened():
                test_cap.release()
                raise ValueError(f"无法打开视频源: {new_source}")
            test_cap.release()

            # 设置切换标志
            self._switching_source = True

            # 等待当前帧处理完成
            async with self.lock:
                # 释放旧的视频捕获对象
                if self.cap:
                    self.cap.release()

                # 创建新的视频捕获对象
                self.video_source = new_source
                self.cap = cv2.VideoCapture(new_source)

                # 清空缓冲区和队列
                self.buffer.clear()
                # 清空帧队列
                while not self.frame_queue.empty():
                    try:
                        self.frame_queue.get_nowait()
                    except:
                        break

                # 重置分析时间
                self.last_analysis = datetime.now().timestamp()

            # 清除切换标志
            self._switching_source = False

            logger.info(f"视频源已切换为: {new_source}")
            return True

        except Exception as e:
            self._switching_source = False
            logger.error(f"切换视频源失败: {str(e)}")
            raise

    async def video_streamer(self, websocket: WebSocket):
        try:
            while True:
                # start_time = time.monotonic()
                frame = await self.frame_queue.get()  # 从队列中获取帧
                # 压缩为JPEG格式（调整quality参数控制质量）
                _, buffer = cv2.imencode('.jpg', frame, [int(cv2.IMWRITE_JPEG_QUALITY), VideoConfig.JPEG_QUALITY])

                # 通过WebSocket发送二进制数据
                await websocket.send_bytes(buffer.tobytes())
                # elapsed = time.monotonic()  - start_time
                # await asyncio.sleep(1 / self.fps- elapsed-0.02)  # 发送的数度需要比生产的速度快，根据视频的fps来等待
                # if count%60==0:
                #    print("长度",self.frame_queue.qsize())
        except Exception as e:
            print(f"Error: {e}")
        finally:
            print("停止直播")

    async def frame_generator(self):
        """异步视频帧生成器
        Yields:
            np.ndarray: 视频帧数据
        功能：
        - 控制帧生成速度与视频源同步
        - 维护环形缓冲队列（用于回溯分析）
        - 自动处理断流重连
        - 支持动态视频源切换
        """
        count = 0
        while self._running:
            # 如果正在切换视频源，等待切换完成
            if self._switching_source:
                await asyncio.sleep(0.1)
                continue

            start_time = time.monotonic()
            ret, frame = self.cap.read()
            count = count + 1

            if not ret:
                # 视频流中断，尝试重新连接
                logging.warning("视频流中断，尝试重新连接...")
                await self._reconnect()
                continue

            # 转换颜色空间并缓冲
            if frame.dtype != np.uint8:
                frame = frame.astype(np.uint8)
            if len(frame.shape) == 2:
                frame = cv2.cvtColor(frame, cv2.COLOR_GRAY2BGR)

            # 只有在非切换状态下才添加到缓冲区
            if not self._switching_source:
                self.buffer.append({
                    "frame": frame,
                    "timestamp": datetime.now().strftime('%Y-%m-%d-%H-%M-%S')
                })

                yield frame

                if self.start_push_queue:
                    await self.frame_queue.put(frame)  # 将帧放入队列

            # 控制帧生成速度
            elapsed = time.monotonic() - start_time
            await asyncio.sleep(max(0, 1 / self.fps - elapsed))  # 控制帧生成速度

    async def _reconnect(self):
        """视频流重连逻辑"""
        await asyncio.sleep(VideoConfig.WS_RETRY_INTERVAL)
        self.cap.release()
        self.cap = cv2.VideoCapture(self.video_source)
        # 重新连接后读取一帧验证
        ret, frame = self.cap.read()
        if not ret:
            print("警告: 重连后仍无法读取视频帧")

    async def start_processing(self):
        """启动处理流水线
        主要任务：
        - 创建视频归档任务
        - 定时触发智能分析
        - 维护处理状态标志
        """
        self._running = True
        count = 0
        start = time.time()
        async for frame in self.frame_generator():
            asyncio.create_task(archiver.write_frame(frame))
            count = count + 1

            # 定时触发分析
            if (
                    datetime.now().timestamp() - self.last_analysis) >= VideoConfig.ANALYSIS_INTERVAL and count >= fps * VideoConfig.ANALYSIS_INTERVAL:
                print("count", count)
                print("fps * interval", fps * VideoConfig.ANALYSIS_INTERVAL, fps)
                count = 0
                asyncio.create_task(self.trigger_analysis())
                self.last_analysis = datetime.now().timestamp()

    async def trigger_analysis(self):
        """触发异步分析任务
        流程：
        1. 检查分析器是否启用
        2. 加锁获取当前缓冲片段
        3. 调用多模态分析器
        4. 如有异常则触发警报推送
        异常处理：
        - 记录分析失败日志
        """
        print("start")
        try:
            # 检查分析器是否启用
            global analysis_enabled
            if not analysis_enabled:
                print("分析器已停用，跳过分析")
                return

            async with self.lock:
                clip = list(self.buffer)
                if not clip:
                    return

                print("self.buffer:", len(clip))
                # print("clip[0]['timestamp']:", clip[0]['timestamp'])
                # print("clip[-1]['timestamp']:", clip[-1]['timestamp'])

                result = await self.analyzer.analyze([f["frame"] for f in clip], self.fps,
                                                     (clip[0]['timestamp'], clip[-1]['timestamp']))

                if result["alert"] != "无异常":
                    await AlertService.notify(result)

                    # 语音播报异常警报
                    if VOICE_AVAILABLE and trigger_voice_alert:
                        try:
                            alert_text = result.get("alert", "检测到异常")
                            await trigger_voice_alert(alert_text)
                        except Exception as e:
                            logger.warning(f"语音播报失败: {e}")

        except Exception as e:
            logging.error(f" 分析失败: {str(e)}")


class AlertService:
    _connections = set()

    @classmethod
    async def register(cls, websocket: WebSocket):
        await websocket.accept()
        cls._connections.add(websocket)

    @classmethod
    async def notify(cls, data: Dict):
        """广播警报信息"""
        message = json.dumps({
            "timestamp": datetime.now().isoformat(),
            **data
        })

        for conn in list(cls._connections):
            try:
                if conn.client_state == WebSocketState.CONNECTED:
                    await conn.send_text(message)
                else:
                    cls._connections.remove(conn)
            except Exception as e:
                logging.warning(f"推送失败: {str(e)}")
                cls._connections.remove(conn)


class VideoArchiver:
    def __init__(self):
        self.current_writer: Optional[cv2.VideoWriter] = None
        self.last_split = datetime.now()
        # 添加目录自动创建
        os.makedirs(ARCHIVE_DIR, exist_ok=True)

    def _create_new_file(self):
        if self.current_writer is not None:
            self.current_writer.release()

        filename = f"{ARCHIVE_DIR}/{datetime.now().strftime('%Y%m%d_%H%M')}.mp4"
        # 使用更兼容的MP4V编码器
        self.current_writer = cv2.VideoWriter(
            filename,
            cv2.VideoWriter_fourcc(*'mp4v'),
            fps,
            (width, height)
        )
        self.last_split = datetime.now()

    async def write_frame(self, frame: np.ndarray):
        """异步写入视频帧
        触发条件：
        - 达到配置的分段时间间隔（VideoConfig.VIDEO_INTERVAL）
        编码说明：
        - 使用OpenCV的MP4V编码器
        - 自动转换色彩空间（RGB->BGR）
        """
        if self._should_split():
            self._create_new_file()

        if self.current_writer is not None:
            self.current_writer.write(cv2.cvtColor(frame, cv2.COLOR_RGB2BGR))

    def _should_split(self) -> bool:
        return (datetime.now() - self.last_split).total_seconds() >= VideoConfig.VIDEO_INTERVAL

    def _create_new_file(self):
        if self.current_writer is not None:
            self.current_writer.release()

        filename = f"{ARCHIVE_DIR}/{datetime.now().strftime('%Y%m%d_%H%M')}.mp4"
        self.current_writer = cv2.VideoWriter(
            filename,
            cv2.VideoWriter_fourcc(*'avc1'),
            fps,
            (width, height)
        )
        self.last_split = datetime.now()



# from starlette.middleware.websocket import WebSocketMiddleware
@asynccontextmanager
async def lifespan(app: FastAPI):
    """应用生命周期管理器
    职责：
    - 服务启动时创建视频处理后台任务
    - 服务关闭时自动清理资源
    """
    asyncio.create_task(processor.start_processing())  # 启动视频处理流水线
    yield  # 保持服务运行


# 初始化FastAPI应用（使用现代生命周期管理替代旧的on_event方式）
app = FastAPI(
    title="智能视频监控系统",
    lifespan=lifespan
)
# 调整静态文件路由配置（原根路径冲突WebSocket）
app.mount("/static", StaticFiles(directory="web/static"), name="static")
app.mount("/ui", StaticFiles(directory="web/templates", html=True), name="templates")  # 修改根路径为/ui
# 新增video_warning静态目录配置
app.mount("/video_warning", StaticFiles(directory="data/video_warning"), name="video_warning")
# 新增uploads静态目录配置
app.mount("/uploads", StaticFiles(directory="data/uploads"), name="uploads")
# 新增data静态目录配置（用于访问对话历史等数据文件）
app.mount("/data", StaticFiles(directory="data"), name="data")
processor = VideoProcessor(video_source)  # 视频处理器实例
archiver = VideoArchiver()  # 视频归档服务实例

# 全局变量存储当前算法设置
# 使用YAML配置中的当前模型初始化
try:
    current_model_name = current_model['name']
    current_model_display = MODEL_NAMES.get(current_model_name, current_model_name)
    current_algorithms = [current_model_display]
    current_alert_names = current_model_name
    print(f"初始化检测算法: {current_algorithms} -> {current_alert_names}")
except:
    current_algorithms = []
    current_alert_names = "未设置"

# @app.on_event("startup")
# async def startup():
#     """遗留的启动事件处理（建议迁移到lifespan中）"""
#     asyncio.create_task(processor.start_processing())  # ⚠️ 注意：与lifespan存在重复创建任务问题

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
@app.websocket("/alerts")
async def alert_websocket(websocket: WebSocket):
    """警报推送WebSocket端点
    功能：
    - 注册新客户端连接
    - 维持长连接心跳
    - 自动清理断连客户端
    """
    await AlertService.register(websocket)  # 注册WebSocket连接
    try:
        while True:
            # 通过接收空文本维持连接（实际业务可添加心跳检测）
            await websocket.receive_text()
    except Exception:
        pass  # 连接断开时自动退出循环


@app.websocket("/video_feed")
async def video_feed(websocket: WebSocket):
    try:
        await websocket.accept()  # 接受WebSocket连接
        processor.start_push_queue = 1  # 激活视频帧推送开关
        await processor.video_streamer(websocket)  # 启动视频流推送服务

    except WebSocketDisconnect:
        print("视频流客户端断开连接")
        processor.start_push_queue = 0  # 关闭推送开关
        processor.frame_queue = asyncio.Queue()  # 重置帧队列
    except Exception as e:
        print(f"视频流异常: {e}")
        processor.start_push_queue = 0
        processor.frame_queue = asyncio.Queue()
    finally:
        # 确保资源清理（双重保障）
        processor.start_push_queue = 0
        processor.frame_queue = asyncio.Queue()

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# # 当前选择的模型
# current_model = {
#     'name': 'general',
#     'timestamp': datetime.now().isoformat()
# }
#
# # 模型名称映射
# MODEL_NAMES = {
#     'general': '通用异常检测',
#     'fight': '打架斗殴检测',
#     'fall': '跌倒检测',
#     'intrusion': '入侵检测'
# }


@app.get("/", response_class=HTMLResponse)
async def index():
    return FileResponse("web/templates/index.html")

@app.get("/config", response_class=HTMLResponse)
async def config_page():
    return FileResponse("web/templates/config.html")


@app.post("/api/test-alert")
async def test_alert():
    """发送测试警报"""
    try:
        test_data = {
            "alert": "这是一个测试警报消息",
            "alert_type": "测试检测",
            "alert_level": "danger",
            "description": "这是一个用于测试WebSocket连接的模拟警报",
            "location": "测试区域",
            "details": "系统正常运行，WebSocket连接正常"
        }

        await AlertService.notify(test_data)

        return {
            "status": "success",
            "message": "测试警报已发送",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"发送测试警报失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"发送测试警报失败: {str(e)}")


@app.post("/api/set-model")
async def set_model(request: Request):
    data = await request.json()
    if not data or "model" not in data:
        raise HTTPException(status_code=400, detail="缺少必要参数")

    model_type = data["model"]
    # 新增自定义模型处理逻辑
    if model_type == "custom":
        MODEL_NAMES["custom"] = data["custom_prompt"]
        # 这里可以添加自定义提示词处理逻辑
        current_model.update({
            "name": "custom",
            "timestamp": datetime.now().isoformat(),
            "prompt": data["custom_prompt"]
        })
    else:
        if model_type not in MODEL_NAMES:
            raise HTTPException(
                status_code=400,
                detail=f"无效的模型类型，可用选项: {list(MODEL_NAMES.keys())}"
            )
        current_model.update({
            "name": model_type,
            "timestamp": datetime.now().isoformat()
        })

    logger.info(f" 模型已更新: {MODEL_NAMES[current_model['name']]}")
    return {
        "status": "success",
        "model_name": MODEL_NAMES[current_model["name"]],
        "timestamp": current_model["timestamp"]
    }


@app.get("/api/get-model")
async def get_model():
    return {
        "current_model": current_model,
        "model_name": MODEL_NAMES.get(current_model["name"], "未知模型")
    }

@app.post("/api/set-algorithms")
async def set_algorithms(request: Request):
    """设置多个检测算法"""
    try:
        data = await request.json()
        if not data or "algorithms" not in data:
            raise HTTPException(status_code=400, detail="缺少algorithms参数")

        algorithms = data["algorithms"]
        if not isinstance(algorithms, list) or len(algorithms) == 0:
            raise HTTPException(status_code=400, detail="algorithms必须是非空列表")

        # 验证算法名称的有效性
        algorithm_names = []
        for alg in algorithms:
            if "value" not in alg or "name" not in alg:
                raise HTTPException(status_code=400, detail="算法对象必须包含value和name字段")

            # 检查算法值是否有效（在MODEL_NAMES中或是自定义算法）
            if alg["value"] not in MODEL_NAMES and not alg["value"].startswith("custom_"):
                raise HTTPException(status_code=400, detail=f"无效的算法类型: {alg['value']}")

            algorithm_names.append(alg["name"])

        # 更新全局配置
        config.ANOMALY_SURVEILLANCE = algorithm_names
        config.ALERT_NAMES = "多模型检测"  # 设置为多模型检测标识

        # 更新全局变量
        global current_algorithms, current_alert_names
        current_algorithms = algorithm_names
        current_alert_names = "多模型检测"

        # 同时更新导入的变量（确保兼容性）
        import importlib
        importlib.reload(config)

        logger.info(f"检测算法已更新: {algorithm_names}")
        return {
            "status": "success",
            "message": "检测算法设置成功",
            "algorithms": algorithm_names,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"设置检测算法失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"设置检测算法失败: {str(e)}")

@app.get("/api/get-algorithms")
async def get_algorithms():
    """获取当前设置的检测算法"""
    try:
        global current_algorithms, current_alert_names

        # 如果没有设置过算法，使用默认配置
        if not current_algorithms:
            current_algorithms = config.ANOMALY_SURVEILLANCE if isinstance(config.ANOMALY_SURVEILLANCE, list) else [config.ANOMALY_SURVEILLANCE]
            current_alert_names = config.ALERT_NAMES

        return {
            "status": "success",
            "algorithms": current_algorithms,
            "alert_names": current_alert_names,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"获取检测算法失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取检测算法失败: {str(e)}")

# 分析器控制状态
analysis_enabled = True  # 分析器启用状态

@app.post("/api/analysis/start")
async def start_analysis(request: Request):
    """启动分析器"""
    try:
        global analysis_enabled

        data = await request.json()
        if not data or "algorithms" not in data:
            raise HTTPException(status_code=400, detail="缺少algorithms参数")

        algorithms = data["algorithms"]
        if not isinstance(algorithms, list) or len(algorithms) == 0:
            raise HTTPException(status_code=400, detail="algorithms必须是非空列表")

        # 验证算法名称的有效性
        algorithm_names = []
        for alg in algorithms:
            if "value" not in alg or "name" not in alg:
                raise HTTPException(status_code=400, detail="算法对象必须包含value和name字段")

            # 检查算法值是否有效（在MODEL_NAMES中或是自定义算法）
            if alg["value"] not in MODEL_NAMES and not alg["value"].startswith("custom_"):
                raise HTTPException(status_code=400, detail=f"无效的算法类型: {alg['value']}")

            algorithm_names.append(alg["name"])

        # 更新全局配置
        config.ANOMALY_SURVEILLANCE = algorithm_names
        config.ALERT_NAMES = "多模型检测"  # 设置为多模型检测标识

        # 更新全局变量
        global current_algorithms, current_alert_names
        current_algorithms = algorithm_names
        current_alert_names = "多模型检测"

        # 启用分析器
        analysis_enabled = True

        # 同时更新导入的变量（确保兼容性）
        import importlib
        importlib.reload(config)

        logger.info(f"分析器已启动，检测算法: {algorithm_names}")
        return {
            "status": "success",
            "message": "分析器启动成功",
            "algorithms": algorithm_names,
            "analysis_enabled": analysis_enabled,
            "timestamp": datetime.now().isoformat()
        }

    except HTTPException as he:
        raise he
    except Exception as e:
        logger.error(f"启动分析器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"启动分析器失败: {str(e)}")

@app.post("/api/analysis/stop")
async def stop_analysis():
    """停止分析器"""
    try:
        global analysis_enabled

        # 停用分析器
        analysis_enabled = False

        logger.info("分析器已停止")
        return {
            "status": "success",
            "message": "分析器停止成功",
            "analysis_enabled": analysis_enabled,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"停止分析器失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"停止分析器失败: {str(e)}")

@app.get("/api/analysis/status")
async def get_analysis_status():
    """获取分析器状态"""
    try:
        global analysis_enabled, current_algorithms

        return {
            "status": "success",
            "analysis_enabled": analysis_enabled,
            "algorithms": current_algorithms,
            "timestamp": datetime.now().isoformat()
        }

    except Exception as e:
        logger.error(f"获取分析器状态失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取分析器状态失败: {str(e)}")

# 配置管理API接口
@app.get("/api/config")
async def get_config():
    """获取当前系统配置"""
    try:
        return {
            "status": "success",
            "config": {
                "video_source": VIDEO_SOURCE,
                "video_config": {
                    "video_interval": VideoConfig.VIDEO_INTERVAL,
                    "analysis_interval": VideoConfig.ANALYSIS_INTERVAL,
                    "buffer_duration": VideoConfig.BUFFER_DURATION,
                    "ws_retry_interval": VideoConfig.WS_RETRY_INTERVAL,
                    "max_ws_queue": VideoConfig.MAX_WS_QUEUE,
                    "video_storage_path": getattr(config, 'VIDEO_WARNING_DIR', './data/video_warning'),
                    "jpeg_quality": VideoConfig.JPEG_QUALITY
                },
                "server_config": {
                    "host": ServerConfig.HOST,
                    "port": ServerConfig.PORT,
                    "reload": ServerConfig.RELOAD,
                    "workers": ServerConfig.WORKERS
                },
                "api_config": {
                    "qwen_api_url": APIConfig.QWEN_API_URL,
                    "qwen_model": APIConfig.QWEN_MODEL,
                    "moonshot_api_url": APIConfig.MOONSHOT_API_URL,
                    "moonshot_model": APIConfig.MOONSHOT_MODEL,
                    "request_timeout": APIConfig.REQUEST_TIMEOUT,
                    "temperature": APIConfig.TEMPERATURE,
                    "top_p": APIConfig.TOP_P,
                    "top_k": APIConfig.TOP_K,
                    "repetition_penalty": APIConfig.REPETITION_PENALTY
                },
                "rag_config": {
                    "enable_rag": RAGConfig.ENABLE_RAG,
                    "milvus_host": RAGConfig.MILVUS_HOST,
                    "milvus_port": RAGConfig.MILVUS_PORT,
                    "collection_name": RAGConfig.COLLECTION_NAME,
                    "embedding_dim": RAGConfig.EMBEDDING_DIM,
                    "vector_api_url": RAGConfig.VECTOR_API_URL,
                    "history_file": RAGConfig.HISTORY_FILE
                }
            }
        }
    except Exception as e:
        logger.error(f"获取配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取配置失败: {str(e)}")

@app.post("/api/config")
async def update_config_api(request: Request):
    """更新系统配置"""
    try:
        data = await request.json()
        if not data:
            raise HTTPException(status_code=400, detail="缺少配置数据")

        # 更新配置
        update_config(data)

        # 如果更新了视频源，使用动态切换方法
        if 'video_source' in data:
            global VIDEO_SOURCE
            VIDEO_SOURCE = data['video_source']
            await processor.switch_video_source(VIDEO_SOURCE)
            logger.info(f"视频源已更新为: {VIDEO_SOURCE}")

        return {
            "status": "success",
            "message": "配置更新成功",
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"更新配置失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"更新配置失败: {str(e)}")

@app.post("/api/custom-models")
async def add_custom_model_api(request: Request):
    """添加自定义模型"""
    try:
        data = await request.json()
        if not data or "name" not in data:
            raise HTTPException(status_code=400, detail="缺少模型名称")

        chinese_name = data["name"].strip()
        keywords = data.get("keywords", [])

        if not chinese_name:
            raise HTTPException(status_code=400, detail="模型名称不能为空")

        # 添加自定义模型
        success, english_name, message = add_custom_model(chinese_name, keywords)

        if success:
            # 重新加载配置以获取最新的MODEL_NAMES
            import importlib
            importlib.reload(config)

            return {
                "status": "success",
                "message": message,
                "english_name": english_name,
                "chinese_name": chinese_name,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"添加自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"添加自定义模型失败: {str(e)}")

@app.delete("/api/custom-models/{english_name}")
async def remove_custom_model_api(english_name: str):
    """删除自定义模型"""
    try:
        success, message = remove_custom_model(english_name)

        if success:
            # 重新加载配置以获取最新的MODEL_NAMES
            import importlib
            importlib.reload(config)

            return {
                "status": "success",
                "message": message,
                "timestamp": datetime.now().isoformat()
            }
        else:
            raise HTTPException(status_code=400, detail=message)

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"删除自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"删除自定义模型失败: {str(e)}")

@app.get("/api/custom-models")
async def get_custom_models_api():
    """获取所有自定义模型"""
    try:
        custom_models = list_custom_models()
        return {
            "status": "success",
            "models": custom_models,
            "count": len(custom_models)
        }
    except Exception as e:
        logger.error(f"获取自定义模型失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"获取自定义模型失败: {str(e)}")

@app.post("/api/upload-video")
async def upload_video(file: UploadFile = File(...)):
    """上传视频文件作为视频源"""
    try:
        # 检查文件类型
        if not file.content_type.startswith('video/'):
            raise HTTPException(status_code=400, detail="只支持视频文件")

        # 创建上传目录
        upload_dir = Path("uploads")
        upload_dir.mkdir(exist_ok=True)

        # 保存文件
        file_path = upload_dir / file.filename
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        # 更新视频源配置
        global VIDEO_SOURCE
        VIDEO_SOURCE = str(file_path)

        # 使用动态切换方法
        await processor.switch_video_source(VIDEO_SOURCE)

        logger.info(f"视频文件上传成功: {file.filename}")
        return {
            "status": "success",
            "message": "视频文件上传成功",
            "filename": file.filename,
            "video_source": VIDEO_SOURCE,
            "timestamp": datetime.now().isoformat()
        }
    except Exception as e:
        logger.error(f"视频上传失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"视频上传失败: {str(e)}")

@app.post("/api/set-video-source")
async def set_video_source(request: Request):
    """设置视频源（RTSP或文件路径）"""
    try:
        data = await request.json()
        if not data or "video_source" not in data:
            raise HTTPException(status_code=400, detail="缺少video_source参数")

        new_source = data["video_source"]
        test_only = data.get("test_only", False)  # 是否仅测试连接

        # 验证视频源
        test_cap = cv2.VideoCapture(new_source)
        if not test_cap.isOpened():
            test_cap.release()
            raise HTTPException(status_code=400, detail="无法打开视频源，请检查路径或RTSP地址")
        test_cap.release()

        # 如果仅测试，不实际切换视频源
        if test_only:
            return {
                "status": "success",
                "message": "视频源连接测试成功",
                "video_source": new_source,
                "timestamp": datetime.now().isoformat()
            }

        # 更新视频源
        global VIDEO_SOURCE
        VIDEO_SOURCE = new_source

        # 使用动态切换方法
        await processor.switch_video_source(VIDEO_SOURCE)

        logger.info(f"视频源已设置为: {VIDEO_SOURCE}")
        return {
            "status": "success",
            "message": "视频源设置成功",
            "video_source": VIDEO_SOURCE,
            "timestamp": datetime.now().isoformat()
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"设置视频源失败: {str(e)}")
        raise HTTPException(status_code=500, detail=f"设置视频源失败: {str(e)}")

@app.get("/api/video-clips")
async def get_video_clips(request: Request):
    try:
        date_filter = request.query_params.get('date')
        if not date_filter or not re.match(r'^\d{4}-\d{2}-\d{2}$', date_filter):
            return JSONResponse(
                status_code=400,
                content={"status": "error", "message": "日期参数格式应为YYYY-MM-DD"}
            )
        clips = []
        video_dir = os.path.abspath("data/video_warning")
        
        # 添加目录存在性检查
        if not os.path.exists(video_dir):
            raise HTTPException(status_code=404, detail="Video directory not found")
            
        for filename in os.listdir(video_dir):
            if filename.endswith(".mp4"):
                # 解析文件名格式：英文模型名_YYYY-MM-DD-HH-MM-SS.mp4
                base_name = os.path.splitext(filename)[0]
                parts = base_name.split('_')
                if len(parts) < 2:  # 至少需要：模型名_时间戳
                    continue  # 跳过格式错误文件

                try:
                    # 智能解析文件名：最后一部分是时间戳，前面的部分组合成模型名
                    time_part = parts[-1]  # 最后一部分是时间戳

                    # 验证时间戳格式
                    datetime.strptime(time_part, "%Y-%m-%d-%H-%M-%S")

                    # 模型名是除最后一部分外的所有部分组合
                    if len(parts) == 2:
                        alert_type = parts[0]  # 只有两部分：模型名_时间戳
                    else:
                        alert_type = '_'.join(parts[:-1])  # 多部分：模型名可能包含下划线

                    # 解析日期用于过滤
                    date_part = time_part.split('-')[:3]  # 取前3部分（年、月、日）
                    file_date = '-'.join(date_part)  # 组合成 "YYYY-MM-DD"

                    # 只保留匹配日期的文件
                    if file_date != date_filter:
                        continue

                    # 提取完整时间戳（YYYY-MM-DD-HH-MM-SS）
                    timestamp = datetime.strptime(time_part, "%Y-%m-%d-%H-%M-%S").isoformat()

                    # 单个模型名称转换为中文
                    chinese_alert = MODEL_NAMES.get(alert_type, "未知异常")

                except (ValueError, IndexError) as e:
                    print(f"解析文件名时间戳失败: {filename}, 错误: {e}")
                    continue  # 跳过无法解析的文件
                
                clip_data = {
                    "videoPath": f"/video_warning/{filename}",
                    "thumbnail": f"/video_warning/{base_name}.jpg",
                    "alertType": f"{chinese_alert}",  # 使用中文异常类型
                    "timestamp": timestamp
                }
                clips.append(clip_data)
        
        return clips

    except HTTPException as he:
        raise he
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

router = APIRouter()

@router.post("/api/ask")
async def handle_question(request: Request):
    data = await request.json()
    answer = await rag_query(data['question'])
    return {"answer": answer}

@app.get('/api/alerts/trend')
def alert_trend(request: Request):  # 添加FastAPI的Request参数
    try:
        dates, counts = query_recent_alert_counts()
        return Response(
            content=json.dumps({
                'labels': dates,
                'data': counts,
                'status': 'success'
            }),
            media_type='application/json'
        )
    except Exception as e:
        return Response(
            content=json.dumps({'status': 'error', 'message': str(e)}),
            status_code=500,
            media_type='application/json'
        )
@app.get('/api/recent-alerts')
def get_recent_alerts(request: Request):
    try:
        # 首先尝试从Milvus数据库获取数据
        try:
            labels, data = query_recent_alerts()
            if labels and data:  # 如果数据库有数据，直接返回
                return Response(
                    content=json.dumps({
                        'labels': labels,
                        'data': data,
                        'status': 'success'
                    }),
                    media_type='application/json'
                )
        except Exception as db_error:
            print(f"数据库查询失败: {db_error}")

        # 如果数据库没有数据或查询失败，从视频文件统计
        labels, data = get_alerts_from_video_files()
        return Response(
            content=json.dumps({
                'labels': labels,
                'data': data,
                'status': 'success'
            }),
            media_type='application/json'
        )
    except Exception as e:
        return Response(
            content=json.dumps({'status': 'error', 'message': str(e)}),
            status_code=500,
            media_type='application/json'
        )

def get_alerts_from_video_files():
    """从视频文件统计异常类型分布"""
    try:
        video_dir = os.path.abspath("data/video_warning")
        if not os.path.exists(video_dir):
            return [], []

        # 统计各种异常类型的数量
        alert_counter = {}

        # 获取最近7天的日期范围
        end_date = datetime.now()
        start_date = end_date - timedelta(days=7)

        for filename in os.listdir(video_dir):
            if filename.endswith(".mp4"):
                try:
                    # 解析文件名格式：英文模型名_YYYY-MM-DD-HH-MM-SS.mp4
                    base_name = os.path.splitext(filename)[0]
                    parts = base_name.split('_')
                    if len(parts) < 2:
                        continue

                    # 模型名是第一部分
                    alert_type = parts[0]

                    # 时间戳是第二部分：YYYY-MM-DD-HH-MM-SS
                    time_part = parts[1]

                    # 验证时间戳格式并检查是否在7天内
                    file_datetime = datetime.strptime(time_part, "%Y-%m-%d-%H-%M-%S")
                    if file_datetime < start_date:
                        continue  # 跳过7天前的文件

                    # 转换为中文名称
                    chinese_alert = MODEL_NAMES.get(alert_type, "未知异常")

                    # 统计数量
                    if chinese_alert in alert_counter:
                        alert_counter[chinese_alert] += 1
                    else:
                        alert_counter[chinese_alert] = 1

                except (ValueError, IndexError) as e:
                    print(f"解析文件名失败: {filename}, 错误: {e}")
                    continue

        # 转换为前端需要的格式
        labels = list(alert_counter.keys())
        data = list(alert_counter.values())

        print(f"从视频文件统计到的异常类型: {dict(zip(labels, data))}")
        return labels, data

    except Exception as e:
        print(f"从视频文件统计异常类型失败: {e}")
        return [], []


# 将router挂载到您的FastAPI app
app.include_router(router)

# 集成语音助手API
if VOICE_AVAILABLE and voice_router:
    app.include_router(voice_router)
    print("✅ 语音助手API已集成")
if __name__ == "__main__":
    """服务启动配置
    参数说明：
    - reload: 开发模式热重载
    - workers: 工作进程数（生产环境建议>=CPU核心数）
    """
    uvicorn.run(
        app="video_server:app",
        host=ServerConfig.HOST,
        port=ServerConfig.PORT,
        reload=ServerConfig.RELOAD,
        workers=ServerConfig.WORKERS
    )

# python video_server.py --video_source "./test.mp4"




