<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>API接口测试页面</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-6xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">API接口联调测试</h1>
        
        <!-- 测试结果概览 -->
        <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-8">
            <div class="bg-gray-800 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-green-400" id="passedCount">0</div>
                <div class="text-sm text-gray-400">通过</div>
            </div>
            <div class="bg-gray-800 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-red-400" id="failedCount">0</div>
                <div class="text-sm text-gray-400">失败</div>
            </div>
            <div class="bg-gray-800 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-yellow-400" id="warningCount">0</div>
                <div class="text-sm text-gray-400">警告</div>
            </div>
            <div class="bg-gray-800 p-4 rounded-lg text-center">
                <div class="text-2xl font-bold text-blue-400" id="totalCount">0</div>
                <div class="text-sm text-gray-400">总计</div>
            </div>
        </div>

        <!-- 测试控制 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-8">
            <div class="flex justify-between items-center">
                <h2 class="text-xl font-semibold">测试控制</h2>
                <div class="space-x-4">
                    <button id="runAllTests" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded">
                        <i class="fas fa-play mr-2"></i>运行所有测试
                    </button>
                    <button id="clearResults" class="px-4 py-2 bg-gray-600 hover:bg-gray-700 rounded">
                        <i class="fas fa-trash mr-2"></i>清空结果
                    </button>
                </div>
            </div>
        </div>

        <!-- 测试项目 -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
            
            <!-- 核心API测试 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">核心API测试</h3>
                <div class="space-y-3">
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>智能问答 API</span>
                        <button onclick="testAskAPI()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="askAPI-status" class="test-status">⏳</span>
                    </div>
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>视频源配置 API</span>
                        <button onclick="testVideoSourceAPI()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="videoSourceAPI-status" class="test-status">⏳</span>
                    </div>
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>分析控制 API</span>
                        <button onclick="testAnalysisAPI()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="analysisAPI-status" class="test-status">⏳</span>
                    </div>
                </div>
            </div>

            <!-- 语音API测试 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">语音API测试</h3>
                <div class="space-y-3">
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>语音控制 API</span>
                        <button onclick="testVoiceControlAPI()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="voiceControlAPI-status" class="test-status">⏳</span>
                    </div>
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>语音播放 API</span>
                        <button onclick="testVoiceSpeakAPI()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="voiceSpeakAPI-status" class="test-status">⏳</span>
                    </div>
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>语音状态 API</span>
                        <button onclick="testVoiceStatusAPI()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="voiceStatusAPI-status" class="test-status">⏳</span>
                    </div>
                </div>
            </div>

            <!-- WebSocket测试 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">WebSocket测试</h3>
                <div class="space-y-3">
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>视频流 WebSocket</span>
                        <button onclick="testVideoWebSocket()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="videoWS-status" class="test-status">⏳</span>
                    </div>
                    <div class="test-item flex justify-between items-center p-3 bg-gray-700 rounded">
                        <span>警报流 WebSocket</span>
                        <button onclick="testAlertWebSocket()" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">测试</button>
                        <span id="alertWS-status" class="test-status">⏳</span>
                    </div>
                </div>
            </div>

            <!-- 测试日志 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4">测试日志</h3>
                <div id="testLog" class="bg-gray-900 p-4 rounded h-64 overflow-y-auto font-mono text-sm">
                    <div class="text-gray-400">等待测试开始...</div>
                </div>
            </div>
        </div>

        <!-- 返回主页 -->
        <div class="text-center mt-8">
            <a href="index.html" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium">
                <i class="fas fa-home mr-2"></i>返回主页
            </a>
        </div>
    </div>

    <script>
        const host = window.location.hostname;
        const port = '16532';
        let testResults = { passed: 0, failed: 0, warning: 0, total: 0 };

        // 添加日志
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('testLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            let colorClass = 'text-gray-300';
            let icon = 'info-circle';
            
            switch (type) {
                case 'success':
                    colorClass = 'text-green-400';
                    icon = 'check-circle';
                    break;
                case 'error':
                    colorClass = 'text-red-400';
                    icon = 'times-circle';
                    break;
                case 'warning':
                    colorClass = 'text-yellow-400';
                    icon = 'exclamation-triangle';
                    break;
            }
            
            logEntry.className = `${colorClass} mb-1`;
            logEntry.innerHTML = `<i class="fas fa-${icon} mr-2"></i>[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }

        // 更新状态
        function updateStatus(testId, status) {
            const statusEl = document.getElementById(`${testId}-status`);
            switch (status) {
                case 'success':
                    statusEl.innerHTML = '✅';
                    testResults.passed++;
                    break;
                case 'error':
                    statusEl.innerHTML = '❌';
                    testResults.failed++;
                    break;
                case 'warning':
                    statusEl.innerHTML = '⚠️';
                    testResults.warning++;
                    break;
                case 'testing':
                    statusEl.innerHTML = '🔄';
                    break;
            }
            testResults.total = testResults.passed + testResults.failed + testResults.warning;
            updateCounters();
        }

        // 更新计数器
        function updateCounters() {
            document.getElementById('passedCount').textContent = testResults.passed;
            document.getElementById('failedCount').textContent = testResults.failed;
            document.getElementById('warningCount').textContent = testResults.warning;
            document.getElementById('totalCount').textContent = testResults.total;
        }

        // 测试智能问答API
        async function testAskAPI() {
            updateStatus('askAPI', 'testing');
            addLog('测试智能问答API...', 'info');
            
            try {
                const response = await fetch(`http://${host}:${port}/api/ask`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ question: 'API测试' })
                });
                
                if (response.ok) {
                    const result = await response.json();
                    addLog('智能问答API测试成功', 'success');
                    updateStatus('askAPI', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`智能问答API测试失败: ${error.message}`, 'error');
                updateStatus('askAPI', 'error');
            }
        }

        // 测试视频源配置API
        async function testVideoSourceAPI() {
            updateStatus('videoSourceAPI', 'testing');
            addLog('测试视频源配置API...', 'info');
            
            try {
                const response = await fetch(`http://${host}:${port}/api/set-video-source`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ video_source: 'rtsp://test' })
                });
                
                if (response.ok) {
                    addLog('视频源配置API测试成功', 'success');
                    updateStatus('videoSourceAPI', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`视频源配置API测试失败: ${error.message}`, 'error');
                updateStatus('videoSourceAPI', 'error');
            }
        }

        // 测试分析控制API
        async function testAnalysisAPI() {
            updateStatus('analysisAPI', 'testing');
            addLog('测试分析控制API...', 'info');
            
            // 由于后端没有这些接口，标记为警告
            addLog('分析控制API不存在，使用本地状态管理', 'warning');
            updateStatus('analysisAPI', 'warning');
        }

        // 测试语音控制API
        async function testVoiceControlAPI() {
            updateStatus('voiceControlAPI', 'testing');
            addLog('测试语音控制API...', 'info');
            
            try {
                const response = await fetch(`http://${host}:${port}/api/voice/control`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'start' })
                });
                
                if (response.ok) {
                    addLog('语音控制API测试成功', 'success');
                    updateStatus('voiceControlAPI', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`语音控制API测试失败: ${error.message}`, 'error');
                updateStatus('voiceControlAPI', 'error');
            }
        }

        // 测试语音播放API
        async function testVoiceSpeakAPI() {
            updateStatus('voiceSpeakAPI', 'testing');
            addLog('测试语音播放API...', 'info');
            
            try {
                const response = await fetch(`http://${host}:${port}/api/voice/speak`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ text: 'API测试' })
                });
                
                if (response.ok) {
                    addLog('语音播放API测试成功', 'success');
                    updateStatus('voiceSpeakAPI', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`语音播放API测试失败: ${error.message}`, 'error');
                updateStatus('voiceSpeakAPI', 'error');
            }
        }

        // 测试语音状态API
        async function testVoiceStatusAPI() {
            updateStatus('voiceStatusAPI', 'testing');
            addLog('测试语音状态API...', 'info');
            
            try {
                const response = await fetch(`http://${host}:${port}/api/voice/status`);
                
                if (response.ok) {
                    addLog('语音状态API测试成功', 'success');
                    updateStatus('voiceStatusAPI', 'success');
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`语音状态API测试失败: ${error.message}`, 'error');
                updateStatus('voiceStatusAPI', 'error');
            }
        }

        // 测试视频WebSocket
        function testVideoWebSocket() {
            updateStatus('videoWS', 'testing');
            addLog('测试视频流WebSocket...', 'info');
            
            const ws = new WebSocket(`ws://${host}:${port}/video_feed`);
            
            const timeout = setTimeout(() => {
                ws.close();
                addLog('视频流WebSocket连接超时', 'warning');
                updateStatus('videoWS', 'warning');
            }, 5000);
            
            ws.onopen = () => {
                clearTimeout(timeout);
                addLog('视频流WebSocket连接成功', 'success');
                updateStatus('videoWS', 'success');
                ws.close();
            };
            
            ws.onerror = () => {
                clearTimeout(timeout);
                addLog('视频流WebSocket连接失败', 'error');
                updateStatus('videoWS', 'error');
            };
        }

        // 测试警报WebSocket
        function testAlertWebSocket() {
            updateStatus('alertWS', 'testing');
            addLog('测试警报流WebSocket...', 'info');
            
            const ws = new WebSocket(`ws://${host}:${port}/alerts`);
            
            const timeout = setTimeout(() => {
                ws.close();
                addLog('警报流WebSocket连接超时', 'warning');
                updateStatus('alertWS', 'warning');
            }, 5000);
            
            ws.onopen = () => {
                clearTimeout(timeout);
                addLog('警报流WebSocket连接成功', 'success');
                updateStatus('alertWS', 'success');
                ws.close();
            };
            
            ws.onerror = () => {
                clearTimeout(timeout);
                addLog('警报流WebSocket连接失败', 'error');
                updateStatus('alertWS', 'error');
            };
        }

        // 运行所有测试
        async function runAllTests() {
            addLog('开始运行所有测试...', 'info');
            testResults = { passed: 0, failed: 0, warning: 0, total: 0 };
            
            await testAskAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testVideoSourceAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testAnalysisAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testVoiceControlAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testVoiceSpeakAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            await testVoiceStatusAPI();
            await new Promise(resolve => setTimeout(resolve, 500));
            
            testVideoWebSocket();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            testAlertWebSocket();
            await new Promise(resolve => setTimeout(resolve, 1000));
            
            addLog('所有测试完成', 'info');
        }

        // 清空结果
        function clearResults() {
            document.getElementById('testLog').innerHTML = '<div class="text-gray-400">等待测试开始...</div>';
            testResults = { passed: 0, failed: 0, warning: 0, total: 0 };
            updateCounters();
            
            // 重置所有状态
            document.querySelectorAll('.test-status').forEach(el => {
                el.innerHTML = '⏳';
            });
        }

        // 事件监听器
        document.getElementById('runAllTests').addEventListener('click', runAllTests);
        document.getElementById('clearResults').addEventListener('click', clearResults);
    </script>
</body>
</html>
