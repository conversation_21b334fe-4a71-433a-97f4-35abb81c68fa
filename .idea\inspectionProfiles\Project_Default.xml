<component name="InspectionProjectProfileManager">
  <profile version="1.0">
    <option name="myName" value="Project Default" />
    <inspection_tool class="PyPackageRequirementsInspection" enabled="true" level="WARNING" enabled_by_default="true">
      <option name="ignoredPackages">
        <value>
          <list size="11">
            <item index="0" class="java.lang.String" itemvalue="openai" />
            <item index="1" class="java.lang.String" itemvalue="dashscope" />
            <item index="2" class="java.lang.String" itemvalue="arxiv" />
            <item index="3" class="java.lang.String" itemvalue="langchain_community" />
            <item index="4" class="java.lang.String" itemvalue="elevenlabs" />
            <item index="5" class="java.lang.String" itemvalue="httpx" />
            <item index="6" class="java.lang.String" itemvalue="fastapi" />
            <item index="7" class="java.lang.String" itemvalue="opencv-python" />
            <item index="8" class="java.lang.String" itemvalue="numpy" />
            <item index="9" class="java.lang.String" itemvalue="uvicorn" />
            <item index="10" class="java.lang.String" itemvalue="websockets" />
          </list>
        </value>
      </option>
    </inspection_tool>
  </profile>
</component>