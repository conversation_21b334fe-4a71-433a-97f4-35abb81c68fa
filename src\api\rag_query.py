from threading import Thread, Lock
from queue import Queue
import time
from pymilvus import connections, Collection
from src.core.config_adapter import RAGConfig
from utils.utility import get_embeddings, chat_request
import sounddevice as sd
import soundfile as sf
from vosk import Model, KaldiRecognizer
import speech_recognition as sr
import pyttsx3
import asyncio
import numpy as np
import traceback
import json
import os
import datetime
import re
# 新增全局变量和锁
audio_queue = Queue()  # 用于传递录音数据
text_queue = Queue()  # 用于传递识别文本
response_queue = Queue()  # 用于传递大模型响应
stop_event = False  # 全局停止标志
lock = Lock()  # 全局锁

# 新增语音处理函数
def init_voice_engine():
    # 初始化语音引擎
    recognizer = sr.Recognizer()
    # 添加模型路径配置
    model_path = r'D:\LLM\voice\zh-1'
    if not os.path.exists(model_path):
        raise FileNotFoundError(f"模型路径不存在: {model_path}")
    recognizer.vosk_model = Model(model_path)
    tts_engine = pyttsx3.init()
    tts_engine.setProperty('rate', 150)
    return recognizer, tts_engine


async def record_audio(duration=5, fs=16000):
    print("请说话...")
    try:
        # 增加调试日志
        print(f"采样率: {fs}, 时长: {duration}s")
        # 创建输入流确保设备可用
        with sd.InputStream(samplerate=fs, channels=1, dtype='int16') as stream:
            print("麦克风已就绪")
            frames = []
            for _ in range(int(fs * duration / 1024)):
                data, overflow = stream.read(1024)
                if overflow:
                    print("警告: 音频缓冲区溢出")
                frames.append(data)
            # 合并音频帧并转换格式
            audio_data = np.concatenate(frames, axis=0)
            return audio_data.flatten().astype('int16')
    except sd.PortAudioError as pae:
        print(f"设备异常: {str(pae)}")
    except Exception as e:
        print(f"录音失败: {traceback.format_exc()}")
    return np.array([], dtype='int16')


async def speech_to_text(recognizer, audio_data):
    try:
        # 转换为AudioData对象
        audio_data = sr.AudioData(
            frame_data=audio_data.tobytes(),
            sample_rate=16000,
            sample_width=2  # int16对应2字节
        )
        text = await asyncio.wait_for(
            asyncio.get_event_loop().run_in_executor(
                None,
                lambda: recognizer.recognize_vosk(audio_data)
            ),
            timeout=10
        )
        # 解析结果
        return json.loads(text)['text']
    except KeyError:
        print("无有效识别结果")
    except Exception as e:
        print(f"最终错误: {str(e)}")
    return ""


async def text_to_speech(engine, text):
    # 文字转语音
    def _speak():
        engine.say(text)
        engine.runAndWait()

    await asyncio.get_event_loop().run_in_executor(None, _speak)

async def rag_query(user_query: str, top_k: int = 3) -> str:
    # 获取查询向量
    query_vector = get_embeddings(user_query)

    # 连接Milvus
    connections.connect(
        alias="default",
        host=RAGConfig.MILVUS_HOST,
        port=RAGConfig.MILVUS_PORT,
        user=RAGConfig.MILVUS_USER,
        password=RAGConfig.MILVUS_PASSWORD
    )

    # 获取行为描述集合
    collection = Collection("table_video_table")
    collection.load()

    # 向量相似度搜索
    search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
    results = collection.search(
        data=[query_vector],
        anns_field="vector",
        param=search_params,
        limit=top_k,
        output_fields=["text"]
    )

    # 提取检索结果
    retrieved_texts = [hit.entity.get("text") for hit in results[0]]

    # 构建增强提示词
    prompt_system_intro = """
    [系统角色] 你是安全智能助手小凯。
    """
    augmented_prompt = f"{prompt_system_intro}\n当前日期：{datetime.datetime.now().strftime('%Y年%m月%d日')}\n只针对以下内容回答，精简回答内容，原始查询：{user_query}\n相关上下文："
    augmented_prompt += "\n".join([f"- {text}" for text in retrieved_texts])

    # 获取知识库集合
    collection = Collection("knowledge_base")
    collection.load()

    # 向量相似度搜索
    search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
    results = collection.search(
        data=[query_vector],
        anns_field="embedding",
        param=search_params,
        limit=top_k,
        output_fields=["text"]
    )

    # 提取检索结果
    retrieved_texts = [hit.entity.get("text") for hit in results[0]]

    # 构建增强提示词
    augmented_prompt += "\n".join([f"- {text}" for text in retrieved_texts])

    # 调用大模型接口
    response = await chat_request(augmented_prompt)

    cleaned_text = re.sub(r'<think[^>]*>.*?</think>', '', response, flags=re.DOTALL)
    print("大模型响应：", cleaned_text)
    return cleaned_text

class AudioRecorder(Thread):
    def __init__(self):
        super().__init__()
        self.daemon = True

    def run(self):
        global stop_event
        while not stop_event:
            try:
                audio_data = asyncio.run(record_audio())
                if audio_data.size > 0:
                    audio_queue.put(audio_data)
                time.sleep(0.1)  # 防止CPU占用过高
            except Exception as e:
                print(f"录音线程异常: {str(e)}")
                time.sleep(1)  # 出错后等待1秒再重试


class SpeechRecognizer(Thread):
    def __init__(self, recognizer):
        super().__init__()
        self.recognizer = recognizer
        self.daemon = True

    def run(self):
        global stop_event
        while not stop_event:
            try:
                if not audio_queue.empty():
                    audio_data = audio_queue.get()
                    text = asyncio.run(speech_to_text(self.recognizer, audio_data))
                    if text:
                        text_queue.put(text)
                time.sleep(0.1)
            except Exception as e:
                print(f"识别线程异常: {str(e)}")
                time.sleep(1)


class RAGProcessor(Thread):
    def __init__(self):
        super().__init__()
        self.daemon = True

    def run(self):
        global stop_event
        while not stop_event:
            try:
                if not text_queue.empty():
                    query = text_queue.get()
                    response = asyncio.run(rag_query(query))
                    response_queue.put(response)
                time.sleep(0.1)
            except Exception as e:
                print(f"RAG处理线程异常: {str(e)}")
                time.sleep(1)


class SpeechSynthesizer(Thread):
    def __init__(self, tts_engine):
        super().__init__()
        self._engine_lock = Lock()
        self.initialize_engine()
        self.daemon = True

    def initialize_engine(self):
        with self._engine_lock:
            self.tts_engine = pyttsx3.init()
            self.tts_engine.setProperty('rate', 150)
            # 添加引擎事件回调
            self.tts_engine.connect('started-utterance', lambda name: print("开始语音合成"))
            self.tts_engine.connect('finished-utterance', lambda name: print("完成语音合成"))
            self.tts_engine.connect('error', lambda name: print("语音合成错误"))

    def run(self):
        global stop_event
        while not stop_event:
            try:
                if not response_queue.empty():
                    response = response_queue.get()
                    print(f"准备合成: {response[:50]}...")  # 只打印前50字符

                    with self._engine_lock:
                        try:
                            # 先停止任何可能正在进行的语音
                            self.tts_engine.stop()
                            # 开始新的语音合成
                            self.tts_engine.say(response)
                            # 使用超时防止永久阻塞
                            self.tts_engine.startLoop(False)
                            for _ in range(100):  # 最多等待10秒
                                if not self.tts_engine.isBusy():
                                    break
                                self.tts_engine.iterate()
                                time.sleep(0.1)
                            self.tts_engine.endLoop()
                        except Exception as e:
                            print(f"引擎操作异常: {str(e)}")
                            self.initialize_engine()  # 重新初始化引擎

                time.sleep(0.1)
            except Exception as e:
                print(f"语音合成线程异常: {str(e)}")
                self.initialize_engine()
                time.sleep(1)

def main():
    global stop_event
    try:
        recognizer, tts = init_voice_engine()

        # 创建并启动线程
        recorder = AudioRecorder()
        recognizer_thread = SpeechRecognizer(recognizer)
        rag_thread = RAGProcessor()
        synthesizer = SpeechSynthesizer(tts)

        recorder.start()
        recognizer_thread.start()
        rag_thread.start()
        synthesizer.start()

        # 主线程等待退出信号
        while True:
            time.sleep(1)

    except KeyboardInterrupt:
        print("\n收到停止信号，优雅退出...")
        stop_event = True
        recorder.join()
        recognizer_thread.join()
        rag_thread.join()
        synthesizer.join()
    except Exception as e:
        print(f"主线程异常: {str(e)}")
        stop_event = True


if __name__ == "__main__":
    main()