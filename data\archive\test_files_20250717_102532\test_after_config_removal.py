#!/usr/bin/env python3
"""
测试删除config.py后系统是否正常
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_config_file_removed():
    """确认config.py已被删除"""
    print("检查config.py文件状态...")
    print("=" * 40)
    
    config_path = "src/core/config.py"
    backup_path = "src/core/config.py.backup"
    
    if os.path.exists(config_path):
        print(f"❌ {config_path} 仍然存在")
        return False
    else:
        print(f"✅ {config_path} 已成功删除")
    
    if os.path.exists(backup_path):
        print(f"✅ {backup_path} 备份文件存在")
    else:
        print(f"⚠️ {backup_path} 备份文件不存在")
    
    return True

def test_imports_without_config():
    """测试删除config.py后的导入"""
    print("\n测试删除config.py后的导入...")
    print("-" * 40)
    
    modules_to_test = [
        ("配置适配器", "src.core.config_adapter", ["VideoConfig", "APIConfig", "RAGConfig"]),
        ("配置加载器", "src.core.config_loader", ["config_loader"]),
        ("工具模块", "utils.utility", ["APIConfig", "RAGConfig"]),
        ("警报统计", "utils.alert_statistics", ["RAGConfig", "MODEL_NAMES"]),
        ("RAG查询", "src.api.rag_query", ["rag_query"]),
        ("RAG简化版", "src.api.rag_query_simple", ["rag_query"]),
        ("多模态分析器", "src.models.multi_modal_analyzer", ["MultiModalAnalyzer"]),
    ]
    
    success_count = 0
    total_count = len(modules_to_test)
    
    for module_name, module_path, imports in modules_to_test:
        try:
            module = __import__(module_path, fromlist=imports)
            for import_name in imports:
                getattr(module, import_name)
            print(f"✅ {module_name}: 导入成功")
            success_count += 1
        except Exception as e:
            print(f"❌ {module_name}: 导入失败 - {e}")
    
    print(f"\n导入测试结果: {success_count}/{total_count} 成功")
    return success_count == total_count

def test_yaml_config_loading():
    """测试YAML配置加载"""
    print("\n测试YAML配置加载...")
    print("-" * 30)
    
    try:
        from src.core.config_loader import config_loader
        
        # 测试各种配置加载
        video_source = config_loader.get_video_source()
        current_model = config_loader.get_current_model()
        model_names = config_loader.get_model_names()
        server_config = config_loader.get_server_config()
        
        print(f"✅ 视频源: {video_source}")
        print(f"✅ 当前模型: {current_model['name']}")
        print(f"✅ 服务器端口: {server_config['port']}")
        print(f"✅ 模型数量: {len(model_names)}")
        
        return True
        
    except Exception as e:
        print(f"❌ YAML配置加载失败: {e}")
        return False

def test_config_adapter_functionality():
    """测试配置适配器功能"""
    print("\n测试配置适配器功能...")
    print("-" * 30)
    
    try:
        from src.core.config_adapter import (
            VideoConfig, APIConfig, RAGConfig, ServerConfig,
            VIDEO_SOURCE, current_model, MODEL_NAMES
        )
        
        # 测试配置类属性访问
        video_interval = VideoConfig.VIDEO_INTERVAL
        api_url = APIConfig.QWEN_API_URL
        rag_enabled = RAGConfig.ENABLE_RAG
        server_port = ServerConfig.PORT
        
        print(f"✅ VideoConfig.VIDEO_INTERVAL: {video_interval}")
        print(f"✅ APIConfig.QWEN_API_URL: {api_url}")
        print(f"✅ RAGConfig.ENABLE_RAG: {rag_enabled}")
        print(f"✅ ServerConfig.PORT: {server_port}")
        
        # 测试全局变量
        print(f"✅ VIDEO_SOURCE: {VIDEO_SOURCE}")
        print(f"✅ current_model: {current_model['name']}")
        print(f"✅ MODEL_NAMES数量: {len(MODEL_NAMES)}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置适配器功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("删除config.py后的系统测试")
    print("=" * 60)
    
    test1 = test_config_file_removed()
    test2 = test_imports_without_config()
    test3 = test_yaml_config_loading()
    test4 = test_config_adapter_functionality()
    
    print("\n" + "=" * 60)
    if all([test1, test2, test3, test4]):
        print("🎉 所有测试通过!")
        print("\n✅ config.py 已成功删除")
        print("✅ 所有模块已迁移到YAML配置系统")
        print("✅ 配置适配器正常工作")
        print("✅ 系统可以正常启动")
        
        print("\n现在可以使用以下命令启动系统:")
        print("python main.py")
        
    else:
        print("❌ 部分测试失败")
        print("建议检查配置适配器或恢复config.py备份文件")
    
    print(f"\n备份文件位置: src/core/config.py.backup")

if __name__ == "__main__":
    main()
