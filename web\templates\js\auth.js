
// ai_security_monitor/frontend/js/auth.js
// 用户认证和会话管理模块

// 模拟用户数据库
const users = [
    { username: 'admin', password: '123', role: 'admin' }
];

// 认证用户
function authenticateUser(username, password, remember) {
    const user = users.find(u => u.username === username && u.password === password);
    if (user) {
        // 创建会话
        createSession(user, remember);
        return true;
    }
    return false;
}

// 创建会话
function createSession(user, remember) {
    const session = {
        username: user.username,
        role: user.role,
        timestamp: new Date().getTime(),
        expires: remember ? new Date().getTime() + (30 * 24 * 60 * 60 * 1000) : null // 30天或会话期间
    };
    
    localStorage.setItem('ai_security_session', JSON.stringify(session));
}

// 检查用户是否已认证
function isUserAuthenticated() {
    const session = getSession();
    if (!session) return false;
    
    // 检查会话是否过期
    if (session.expires && session.expires < new Date().getTime()) {
        destroySession();
        return false;
    }
    
    return true;
}

// 获取当前会话
function getSession() {
    const sessionData = localStorage.getItem('ai_security_session');
    return sessionData ? JSON.parse(sessionData) : null;
}

// 销毁会话
function destroySession() {
    localStorage.removeItem('ai_security_session');
}

// 获取当前用户角色
function getCurrentUserRole() {
    const session = getSession();
    return session ? session.role : null;
}

// 导出方法
export { 
    authenticateUser, 
    isUserAuthenticated, 
    getSession, 
    destroySession, 
    getCurrentUserRole 
};
