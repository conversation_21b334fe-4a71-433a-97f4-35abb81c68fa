#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR增强测试脚本
功能：测试改进后的FunASR语音识别实现
"""

import asyncio
import sys
import os

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from voice_assistant import SpeechRecognizer, VoiceConfig
import sounddevice as sd
import wave
import logging

# 配置日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class FunASREnhancedTester:
    """FunASR增强测试器"""
    
    def __init__(self):
        self.recognizer = SpeechRecognizer()
        self.sample_rate = 16000
    
    def print_banner(self):
        """打印测试横幅"""
        print("\n" + "=" * 60)
        print("🚀 FunASR增强版测试")
        print("=" * 60)
        print(f"FunASR服务器: {VoiceConfig.FUNASR_WS_URL}")
        print(f"超时设置: {VoiceConfig.FUNASR_TIMEOUT}秒")
        print("包含音频增强和重试机制")
        print("=" * 60)
    
    def record_high_quality_audio(self, duration=3):
        """录制高质量音频"""
        print(f"\n🎤 录制高质量音频 ({duration}秒)...")
        print("请清晰地说出一句话，比如：'你好小凯，查询今天的警报'")
        
        try:
            # 录制音频
            audio_data = sd.rec(
                int(duration * self.sample_rate),
                samplerate=self.sample_rate,
                channels=1,
                dtype='int16'
            )
            sd.wait()
            
            # 保存为WAV文件
            filename = "enhanced_test_audio.wav"
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(self.sample_rate)
                wf.writeframes(audio_data.tobytes())
            
            print(f"✅ 高质量音频已保存: {filename}")
            
            # 分析音频质量
            import numpy as np
            max_amplitude = np.max(np.abs(audio_data))
            rms = np.sqrt(np.mean(audio_data.astype(np.float64) ** 2))
            
            print(f"📊 音频质量分析:")
            print(f"   最大振幅: {max_amplitude}")
            print(f"   RMS值: {rms:.2f}")
            
            if max_amplitude < 1000:
                print("⚠️ 音频信号较弱，可能影响识别效果")
            elif max_amplitude > 30000:
                print("⚠️ 音频信号较强，可能出现失真")
            else:
                print("✅ 音频信号强度良好")
            
            return filename
            
        except Exception as e:
            print(f"❌ 录制失败: {e}")
            return None
    
    async def test_funasr_recognition(self, audio_file):
        """测试FunASR识别"""
        print(f"\n🧠 测试FunASR识别: {audio_file}")
        
        try:
            # 使用增强版识别器
            result = await self.recognizer._funasr_recognize(audio_file)
            
            if result:
                print(f"✅ FunASR识别成功: '{result}'")
                return result
            else:
                print("❌ FunASR识别失败")
                return None
                
        except Exception as e:
            print(f"❌ FunASR测试异常: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def test_full_recognition_pipeline(self, audio_file):
        """测试完整识别流程"""
        print(f"\n🔄 测试完整识别流程: {audio_file}")
        
        try:
            # 使用完整的识别流程（包含降级机制）
            result = await self.recognizer.recognize(audio_file)
            
            if result:
                print(f"✅ 完整流程识别成功: '{result}'")
                return result
            else:
                print("❌ 完整流程识别失败")
                return None
                
        except Exception as e:
            print(f"❌ 完整流程测试异常: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def create_test_scenarios(self):
        """创建测试场景"""
        scenarios = [
            {
                "name": "短语测试",
                "duration": 2,
                "prompt": "请说：'你好'"
            },
            {
                "name": "命令测试", 
                "duration": 3,
                "prompt": "请说：'查询今天的警报'"
            },
            {
                "name": "长句测试",
                "duration": 4,
                "prompt": "请说：'小凯同学，帮我显示系统统计信息'"
            }
        ]
        return scenarios
    
    async def run_comprehensive_test(self):
        """运行综合测试"""
        self.print_banner()
        
        # 检查识别器状态
        print(f"\n🔧 识别器状态:")
        print(f"   FunASR可用: {self.recognizer.use_funasr}")
        print(f"   Whisper可用: {self.recognizer.whisper_model is not None}")
        
        # 测试场景
        scenarios = self.create_test_scenarios()
        results = []
        
        for i, scenario in enumerate(scenarios, 1):
            print(f"\n{'='*20} 场景 {i}: {scenario['name']} {'='*20}")
            print(f"提示: {scenario['prompt']}")
            
            # 录制音频
            audio_file = self.record_high_quality_audio(scenario['duration'])
            if not audio_file:
                results.append({"scenario": scenario['name'], "success": False, "error": "录制失败"})
                continue
            
            # 测试FunASR
            funasr_result = await self.test_funasr_recognition(audio_file)
            
            # 测试完整流程
            full_result = await self.test_full_recognition_pipeline(audio_file)
            
            # 记录结果
            results.append({
                "scenario": scenario['name'],
                "funasr_result": funasr_result,
                "full_result": full_result,
                "success": funasr_result is not None or full_result is not None
            })
            
            # 清理临时文件
            try:
                os.remove(audio_file)
            except:
                pass
        
        # 显示测试总结
        self.print_test_summary(results)
        
        return results
    
    def print_test_summary(self, results):
        """打印测试总结"""
        print("\n" + "=" * 60)
        print("📊 测试总结")
        print("=" * 60)
        
        total_tests = len(results)
        successful_tests = sum(1 for r in results if r['success'])
        
        print(f"总测试数: {total_tests}")
        print(f"成功测试: {successful_tests}")
        print(f"成功率: {successful_tests/total_tests*100:.1f}%")
        
        print("\n📋 详细结果:")
        for i, result in enumerate(results, 1):
            status = "✅" if result['success'] else "❌"
            print(f"{i}. {status} {result['scenario']}")
            
            if 'funasr_result' in result and result['funasr_result']:
                print(f"   FunASR: '{result['funasr_result']}'")
            elif 'full_result' in result and result['full_result']:
                print(f"   备用识别: '{result['full_result']}'")
            elif 'error' in result:
                print(f"   错误: {result['error']}")
            else:
                print(f"   无识别结果")
        
        print("\n💡 建议:")
        if successful_tests == total_tests:
            print("🎉 所有测试通过！FunASR集成工作正常。")
        elif successful_tests > 0:
            print("⚠️ 部分测试通过，建议检查:")
            print("   1. 说话时声音清晰、音量适中")
            print("   2. 减少环境噪音")
            print("   3. 确保网络连接稳定")
        else:
            print("❌ 所有测试失败，建议检查:")
            print("   1. FunASR服务器是否正常运行")
            print("   2. 网络连接是否正常")
            print("   3. 麦克风设备是否工作正常")
            print("   4. 音频格式是否正确")
        
        print("=" * 60)

async def main():
    """主函数"""
    tester = FunASREnhancedTester()
    
    try:
        results = await tester.run_comprehensive_test()
        
        # 根据结果返回适当的退出码
        successful_tests = sum(1 for r in results if r['success'])
        if successful_tests > 0:
            print("\n✅ 测试完成，至少有部分功能正常")
        else:
            print("\n❌ 测试完成，但所有功能都失败")
            
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
