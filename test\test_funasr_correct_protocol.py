#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR正确协议测试脚本
功能：基于HTML示例的正确协议格式测试FunASR
"""

import asyncio
import json
import struct
import wave
import sounddevice as sd
import numpy as np

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装")

# FunASR配置
FUNASR_WS_URL = "ws://192.168.3.95:10096/"
SAMPLE_RATE = 16000

class FunASRCorrectProtocolTester:
    """FunASR正确协议测试器"""
    
    def __init__(self):
        self.ws_url = FUNASR_WS_URL
    
    def print_banner(self):
        """打印测试横幅"""
        print("\n" + "=" * 60)
        print("🎯 FunASR正确协议测试")
        print("=" * 60)
        print(f"服务器地址: {self.ws_url}")
        print("基于HTML示例的正确协议格式")
        print("=" * 60)
    
    def record_audio(self, duration=3):
        """录制音频"""
        print(f"\n🎤 录制音频 ({duration}秒)...")
        print("请清晰地说话，比如：'你好，这是语音识别测试'")
        
        try:
            audio_data = sd.rec(
                int(duration * SAMPLE_RATE),
                samplerate=SAMPLE_RATE,
                channels=1,
                dtype='int16'
            )
            sd.wait()
            
            filename = "correct_protocol_test.wav"
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(SAMPLE_RATE)
                wf.writeframes(audio_data.tobytes())
            
            print(f"✅ 音频已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 录制失败: {e}")
            return None
    
    async def test_correct_protocol(self, audio_file):
        """使用正确协议测试FunASR"""
        print(f"\n🧠 使用正确协议测试: {audio_file}")
        
        try:
            # 读取WAV文件并转换为PCM
            with wave.open(audio_file, 'rb') as wf:
                frames = wf.getnframes()
                pcm_data = wf.readframes(frames)
                print(f"📊 音频信息: {wf.getframerate()}Hz, {wf.getnchannels()}ch, {len(pcm_data)}字节")
            
            # 转换为int16数组
            pcm_int16 = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
            print(f"📊 PCM数组长度: {len(pcm_int16)} 样本")
            
            # 连接WebSocket
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                print("✅ WebSocket连接成功")
                
                # 1. 发送初始配置（模仿HTML示例）
                initial_config = {
                    "chunk_size": [5, 10, 5],
                    "wav_name": "correct_test",
                    "is_speaking": True,
                    "chunk_interval": 10,
                    "itn": False,
                    "mode": "2pass"
                }
                await websocket.send(json.dumps(initial_config))
                print(f"📤 发送初始配置: {initial_config}")
                
                # 2. 分块发送PCM数据（模仿HTML示例）
                chunk_size = 960  # 与HTML示例一致
                chunk_count = 0
                
                for i in range(0, len(pcm_int16), chunk_size):
                    chunk = pcm_int16[i:i+chunk_size]
                    if len(chunk) > 0:
                        # 转换为bytes并发送
                        chunk_bytes = struct.pack(f'<{len(chunk)}h', *chunk)
                        await websocket.send(chunk_bytes)
                        chunk_count += 1
                        if chunk_count % 10 == 0:  # 每10块显示一次进度
                            print(f"📤 已发送 {chunk_count} 个数据块...")
                
                print(f"📤 总共发送了 {chunk_count} 个PCM数据块")
                
                # 3. 发送结束配置（模仿HTML示例）
                end_config = {
                    "chunk_size": [5, 10, 5],
                    "wav_name": "correct_test",
                    "is_speaking": False,
                    "chunk_interval": 10,
                    "mode": "2pass"
                }
                await websocket.send(json.dumps(end_config))
                print(f"📤 发送结束配置: {end_config}")
                
                # 4. 接收识别结果
                print("\n📥 等待识别结果...")
                responses = []
                final_result = None
                
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=8)
                        responses.append(response)
                        print(f"📥 收到响应: {response}")
                        
                        if isinstance(response, str):
                            try:
                                data = json.loads(response)
                                text = data.get('text', '').strip()
                                is_final = data.get('is_final', False)
                                mode = data.get('mode', '')
                                
                                if text:
                                    print(f"🔍 解析结果: text='{text}', is_final={is_final}, mode='{mode}'")
                                    
                                    if is_final or mode in ['2pass-offline', 'offline']:
                                        final_result = text
                                        print(f"✅ 最终识别结果: '{text}'")
                                        break
                                    else:
                                        print(f"🔄 中间结果: '{text}'")
                                        if not final_result:
                                            final_result = text
                                elif is_final:
                                    print("⚠️ 最终响应但无文本内容")
                                    break
                                    
                            except json.JSONDecodeError:
                                print(f"⚠️ 非JSON响应: {response}")
                        
                except asyncio.TimeoutError:
                    print(f"⏰ 响应接收完成，共收到 {len(responses)} 个响应")
                
                return final_result
                
        except Exception as e:
            print(f"❌ 测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    async def run_test(self):
        """运行测试"""
        self.print_banner()
        
        if not WEBSOCKETS_AVAILABLE:
            print("❌ websockets库不可用")
            return
        
        # 录制音频
        audio_file = self.record_audio(duration=3)
        if not audio_file:
            return
        
        # 测试正确协议
        result = await self.test_correct_protocol(audio_file)
        
        # 显示结果
        print("\n" + "=" * 60)
        if result:
            print(f"🎉 测试成功！识别结果: '{result}'")
            print("✅ FunASR正确协议工作正常")
        else:
            print("❌ 测试失败，未获得识别结果")
            print("💡 可能的原因:")
            print("   1. 音频质量问题")
            print("   2. 服务器配置问题")
            print("   3. 协议细节差异")
        print("=" * 60)
        
        # 清理临时文件
        try:
            import os
            os.remove(audio_file)
            print(f"🗑️ 已清理临时文件: {audio_file}")
        except:
            pass
        
        return result is not None

async def main():
    """主函数"""
    tester = FunASRCorrectProtocolTester()
    
    try:
        success = await tester.run_test()
        if success:
            print("\n🎊 恭喜！FunASR集成测试成功！")
            print("现在可以在语音助手中正常使用FunASR了。")
        else:
            print("\n😞 FunASR测试失败，需要进一步调试。")
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
