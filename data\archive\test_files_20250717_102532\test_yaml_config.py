#!/usr/bin/env python3
"""
测试YAML配置是否正确加载
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_yaml_config():
    """测试YAML配置加载"""
    print("测试YAML配置加载...")
    print("=" * 50)
    
    try:
        from src.core.config_loader import config_loader
        
        # 测试视频源配置
        video_source = config_loader.get_video_source()
        print(f"✅ 视频源: {video_source}")
        
        # 检查视频文件是否存在
        if os.path.exists(video_source):
            print(f"✅ 视频文件存在: {video_source}")
        else:
            print(f"❌ 视频文件不存在: {video_source}")
        
        # 测试当前模型配置
        current_model = config_loader.get_current_model()
        print(f"✅ 当前模型: {current_model}")
        
        # 测试模型名称映射
        model_names = config_loader.get_model_names()
        current_model_name = current_model['name']
        if current_model_name in model_names:
            print(f"✅ 模型名称: {model_names[current_model_name]}")
        else:
            print(f"❌ 模型名称未找到: {current_model_name}")
        
        return True
        
    except Exception as e:
        print(f"❌ YAML配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_old_config():
    """测试旧配置文件"""
    print("\n测试旧配置文件...")
    print("-" * 30)
    
    try:
        from src.core.config_adapter import VIDEO_SOURCE, current_model, MODEL_NAMES
        
        print(f"旧配置 - 视频源: {VIDEO_SOURCE}")
        print(f"旧配置 - 当前模型: {current_model}")
        print(f"旧配置 - 模型名称: {MODEL_NAMES.get(current_model['name'], '未知')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 旧配置加载失败: {e}")
        return False

def main():
    print("配置加载测试")
    print("=" * 60)
    
    # 测试YAML配置
    yaml_success = test_yaml_config()
    
    # 测试旧配置
    old_success = test_old_config()
    
    print("\n" + "=" * 60)
    if yaml_success:
        print("🎉 YAML配置加载成功!")
        print("系统应该使用YAML配置中的设置")
    else:
        print("❌ YAML配置加载失败")
    
    if old_success:
        print("ℹ️ 旧配置仍然可用作备用")
    
    print("\n建议:")
    print("1. 确保YAML配置文件格式正确")
    print("2. 检查视频文件路径是否正确")
    print("3. 重启系统以应用新配置")

if __name__ == "__main__":
    main()
