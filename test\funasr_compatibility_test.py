#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR兼容性测试脚本
功能：测试不同的FunASR协议格式，找到正确的通信方式
"""

import asyncio
import json
import base64
import wave
import sounddevice as sd
import numpy as np

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装")

# FunASR配置
FUNASR_WS_URL = "ws://192.168.3.95:10096/"
SAMPLE_RATE = 16000

class FunASRCompatibilityTester:
    """FunASR兼容性测试器"""
    
    def __init__(self):
        self.ws_url = FUNASR_WS_URL
    
    def create_test_audio(self):
        """创建测试音频"""
        print("🎤 创建测试音频...")
        
        # 生成简单的正弦波作为测试音频
        duration = 2  # 2秒
        frequency = 440  # A4音符
        
        t = np.linspace(0, duration, int(SAMPLE_RATE * duration), False)
        audio_data = np.sin(frequency * 2 * np.pi * t) * 0.3  # 降低音量
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        # 保存为WAV文件
        filename = "compatibility_test.wav"
        with wave.open(filename, 'wb') as wf:
            wf.setnchannels(1)
            wf.setsampwidth(2)
            wf.setframerate(SAMPLE_RATE)
            wf.writeframes(audio_int16.tobytes())
        
        print(f"✅ 测试音频已创建: {filename}")
        return filename, audio_int16.tobytes()
    
    def record_real_audio(self):
        """录制真实语音"""
        print("🎤 录制真实语音 (3秒)...")
        print("请说：'你好，这是测试'")
        
        try:
            audio_data = sd.rec(
                int(3 * SAMPLE_RATE),
                samplerate=SAMPLE_RATE,
                channels=1,
                dtype='int16'
            )
            sd.wait()
            
            filename = "real_voice_test.wav"
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(SAMPLE_RATE)
                wf.writeframes(audio_data.tobytes())
            
            print(f"✅ 真实语音已录制: {filename}")
            return filename, audio_data.tobytes()
            
        except Exception as e:
            print(f"❌ 录制失败: {e}")
            return None, None
    
    async def test_protocol_variant_1(self, pcm_data, wav_name):
        """测试协议变体1：标准FunASR协议"""
        print("\n🧪 测试协议变体1：标准FunASR协议")
        
        try:
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                # 发送配置
                config = {
                    "mode": "offline",
                    "chunk_size": [5, 10, 5],
                    "chunk_interval": 10,
                    "wav_name": wav_name
                }
                await websocket.send(json.dumps(config))
                print(f"📤 发送配置: {config}")
                
                # 发送音频
                audio_msg = {
                    "audio": base64.b64encode(pcm_data).decode(),
                    "is_speaking": True,
                    "wav_format": "pcm"
                }
                await websocket.send(json.dumps(audio_msg))
                print("📤 发送音频数据")
                
                # 发送结束
                end_msg = {"is_speaking": False}
                await websocket.send(json.dumps(end_msg))
                print("📤 发送结束信号")
                
                # 接收响应
                return await self._collect_responses(websocket, "协议变体1")
                
        except Exception as e:
            print(f"❌ 协议变体1失败: {e}")
            return None
    
    async def test_protocol_variant_2(self, pcm_data, wav_name):
        """测试协议变体2：简化协议"""
        print("\n🧪 测试协议变体2：简化协议")
        
        try:
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                # 直接发送音频数据
                audio_msg = {
                    "audio": base64.b64encode(pcm_data).decode(),
                    "wav_name": wav_name,
                    "wav_format": "pcm",
                    "mode": "offline"
                }
                await websocket.send(json.dumps(audio_msg))
                print(f"📤 发送简化消息: {audio_msg.keys()}")
                
                # 接收响应
                return await self._collect_responses(websocket, "协议变体2")
                
        except Exception as e:
            print(f"❌ 协议变体2失败: {e}")
            return None
    
    async def test_protocol_variant_3(self, pcm_data, wav_name):
        """测试协议变体3：流式协议"""
        print("\n🧪 测试协议变体3：流式协议")
        
        try:
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                # 发送开始信号
                start_msg = {
                    "signal": "start",
                    "wav_name": wav_name,
                    "mode": "offline"
                }
                await websocket.send(json.dumps(start_msg))
                print(f"📤 发送开始信号: {start_msg}")
                
                # 分块发送音频
                chunk_size = 8000  # 每块8000字节
                for i in range(0, len(pcm_data), chunk_size):
                    chunk = pcm_data[i:i+chunk_size]
                    chunk_msg = {
                        "audio": base64.b64encode(chunk).decode(),
                        "chunk_id": i // chunk_size,
                        "is_final": i + chunk_size >= len(pcm_data)
                    }
                    await websocket.send(json.dumps(chunk_msg))
                    print(f"📤 发送音频块 {i//chunk_size + 1}")
                
                # 接收响应
                return await self._collect_responses(websocket, "协议变体3")
                
        except Exception as e:
            print(f"❌ 协议变体3失败: {e}")
            return None
    
    async def test_protocol_variant_4(self, pcm_data, wav_name):
        """测试协议变体4：原始二进制数据"""
        print("\n🧪 测试协议变体4：原始二进制数据")
        
        try:
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                # 直接发送PCM二进制数据
                await websocket.send(pcm_data)
                print("📤 发送原始PCM数据")
                
                # 接收响应
                return await self._collect_responses(websocket, "协议变体4")
                
        except Exception as e:
            print(f"❌ 协议变体4失败: {e}")
            return None
    
    async def test_protocol_variant_5(self, wav_file):
        """测试协议变体5：发送完整WAV文件"""
        print("\n🧪 测试协议变体5：完整WAV文件")
        
        try:
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                # 读取完整WAV文件
                with open(wav_file, 'rb') as f:
                    wav_data = f.read()
                
                # 发送WAV文件数据
                wav_msg = {
                    "wav_file": base64.b64encode(wav_data).decode(),
                    "wav_name": wav_file.replace('.wav', ''),
                    "format": "wav"
                }
                await websocket.send(json.dumps(wav_msg))
                print("📤 发送完整WAV文件")
                
                # 接收响应
                return await self._collect_responses(websocket, "协议变体5")
                
        except Exception as e:
            print(f"❌ 协议变体5失败: {e}")
            return None
    
    async def _collect_responses(self, websocket, variant_name):
        """收集WebSocket响应"""
        responses = []
        try:
            while True:
                response = await asyncio.wait_for(websocket.recv(), timeout=5)
                responses.append(response)
                print(f"📥 {variant_name}响应: {response}")
                
                # 检查是否是最终响应
                if isinstance(response, str):
                    try:
                        data = json.loads(response)
                        if data.get('is_final', False):
                            break
                        text = data.get('text', '').strip()
                        if text:
                            print(f"✅ {variant_name}识别到文本: '{text}'")
                            return text
                    except:
                        pass
                        
        except asyncio.TimeoutError:
            print(f"⏰ {variant_name}响应收集完成")
        
        # 尝试从所有响应中提取文本
        for response in responses:
            if isinstance(response, str):
                try:
                    data = json.loads(response)
                    text = data.get('text', '').strip()
                    if text:
                        print(f"✅ {variant_name}从响应中提取文本: '{text}'")
                        return text
                except:
                    continue
        
        print(f"❌ {variant_name}未找到有效文本")
        return None
    
    async def run_compatibility_tests(self):
        """运行兼容性测试"""
        print("\n" + "=" * 60)
        print("🔬 FunASR兼容性测试")
        print("=" * 60)
        print(f"服务器地址: {self.ws_url}")
        print("=" * 60)
        
        if not WEBSOCKETS_AVAILABLE:
            print("❌ websockets库不可用")
            return
        
        # 创建测试音频
        test_wav, test_pcm = self.create_test_audio()
        
        # 录制真实语音
        real_wav, real_pcm = self.record_real_audio()
        
        # 测试不同协议变体
        test_cases = [
            ("合成音频", test_pcm, "compatibility_test"),
        ]
        
        if real_pcm is not None:
            test_cases.append(("真实语音", real_pcm, "real_voice_test"))
        
        results = []
        
        for audio_type, pcm_data, wav_name in test_cases:
            print(f"\n{'='*20} 测试 {audio_type} {'='*20}")
            
            # 测试各种协议变体
            variants = [
                ("标准协议", self.test_protocol_variant_1),
                ("简化协议", self.test_protocol_variant_2),
                ("流式协议", self.test_protocol_variant_3),
                ("二进制数据", self.test_protocol_variant_4),
            ]
            
            for variant_name, test_func in variants:
                try:
                    if variant_name == "二进制数据":
                        result = await test_func(pcm_data, wav_name)
                    else:
                        result = await test_func(pcm_data, wav_name)
                    
                    results.append({
                        "audio_type": audio_type,
                        "variant": variant_name,
                        "result": result,
                        "success": result is not None
                    })
                    
                except Exception as e:
                    print(f"❌ {variant_name}测试异常: {e}")
                    results.append({
                        "audio_type": audio_type,
                        "variant": variant_name,
                        "result": None,
                        "success": False,
                        "error": str(e)
                    })
        
        # 显示测试结果
        self.print_compatibility_results(results)
        
        return results
    
    def print_compatibility_results(self, results):
        """打印兼容性测试结果"""
        print("\n" + "=" * 60)
        print("📊 兼容性测试结果")
        print("=" * 60)
        
        successful_tests = [r for r in results if r['success']]
        
        print(f"总测试数: {len(results)}")
        print(f"成功测试: {len(successful_tests)}")
        
        if successful_tests:
            print("\n✅ 成功的协议变体:")
            for result in successful_tests:
                print(f"   {result['audio_type']} - {result['variant']}: '{result['result']}'")
            
            print(f"\n💡 建议使用以下协议格式:")
            best_result = successful_tests[0]
            print(f"   音频类型: {best_result['audio_type']}")
            print(f"   协议变体: {best_result['variant']}")
            print(f"   识别结果: {best_result['result']}")
        else:
            print("\n❌ 所有协议变体都失败了")
            print("\n可能的原因:")
            print("1. FunASR服务器使用了不同的协议格式")
            print("2. 服务器需要特定的认证或配置")
            print("3. 音频格式不被支持")
            print("4. 网络连接问题")
        
        print("=" * 60)

async def main():
    """主函数"""
    tester = FunASRCompatibilityTester()
    
    try:
        await tester.run_compatibility_tests()
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
