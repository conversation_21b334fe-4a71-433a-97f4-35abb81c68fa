#!/usr/bin/env python3
"""
测试配置适配器是否正常工作
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_config_adapter():
    """测试配置适配器"""
    print("测试配置适配器...")
    print("=" * 50)
    
    try:
        # 测试导入
        from src.core.config_adapter import VideoConfig, APIConfig, RAGConfig, ServerConfig
        from src.core.config_adapter import VIDEO_SOURCE, current_model, MODEL_NAMES
        from src.core.config_adapter import ARCHIVE_DIR, VIDEO_WARNING_DIR, LOG_CONFIG
        
        print("✅ 配置适配器导入成功")
        
        # 测试配置值
        print(f"\n配置值测试:")
        print(f"   视频源: {VIDEO_SOURCE}")
        print(f"   当前模型: {current_model}")
        print(f"   归档目录: {ARCHIVE_DIR}")
        print(f"   视频警告目录: {VIDEO_WARNING_DIR}")
        
        # 测试配置类属性
        print(f"\n配置类属性测试:")
        print(f"   视频间隔: {VideoConfig.VIDEO_INTERVAL}")
        print(f"   分析间隔: {VideoConfig.ANALYSIS_INTERVAL}")
        print(f"   API URL: {APIConfig.QWEN_API_URL}")
        print(f"   RAG启用: {RAGConfig.ENABLE_RAG}")
        print(f"   服务器端口: {ServerConfig.PORT}")
        
        # 测试模型名称
        print(f"\n模型名称测试:")
        print(f"   入侵检测: {MODEL_NAMES.get('intrusion', '未找到')}")
        print(f"   通用检测: {MODEL_NAMES.get('general', '未找到')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置适配器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_imports_from_other_modules():
    """测试其他模块的导入"""
    print("\n测试其他模块导入...")
    print("-" * 30)
    
    try:
        # 测试video_server导入
        print("测试video_server导入...")
        from src.video.video_server import VIDEO_SOURCE as vs_video_source
        print(f"   video_server视频源: {vs_video_source}")
        
        # 测试utility导入
        print("测试utility导入...")
        from utils.utility import APIConfig as util_api_config
        print(f"   utility API配置: {util_api_config.QWEN_API_URL}")
        
        # 测试multi_modal_analyzer导入
        print("测试multi_modal_analyzer导入...")
        from src.models.multi_modal_analyzer import RAGConfig as mma_rag_config
        print(f"   multi_modal_analyzer RAG配置: {mma_rag_config.ENABLE_RAG}")
        
        return True
        
    except Exception as e:
        print(f"❌ 其他模块导入测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("配置适配器完整测试")
    print("=" * 60)
    
    success1 = test_config_adapter()
    success2 = test_imports_from_other_modules()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过!")
        print("\n现在可以安全删除 src/core/config.py 文件")
        print("所有模块都已成功迁移到YAML配置系统")
    else:
        print("❌ 部分测试失败，请检查配置适配器")
    
    print("\n删除config.py后的启动命令:")
    print("python main.py")

if __name__ == "__main__":
    main()
