/**
 * 本地存储管理
 * 负责应用数据的本地存储和恢复
 */

// 存储管理器
class StorageManager {
    constructor() {
        this.prefix = 'ai_security_';
        this.keys = {
            algorithms: 'selectedAlgorithms',
            videoSource: 'videoSource',
            settings: 'appSettings',
            voiceEnabled: 'voiceEnabled',
            customModels: 'customModels',
            conversationHistory: 'conversationHistory'
        };
    }

    /**
     * 设置存储项
     */
    setItem(key, value) {
        try {
            const fullKey = this.prefix + key;
            const serializedValue = JSON.stringify(value);
            localStorage.setItem(fullKey, serializedValue);
            return true;
        } catch (error) {
            console.error('存储数据失败:', error);
            return false;
        }
    }

    /**
     * 获取存储项
     */
    getItem(key, defaultValue = null) {
        try {
            const fullKey = this.prefix + key;
            const item = localStorage.getItem(fullKey);
            return item ? JSON.parse(item) : defaultValue;
        } catch (error) {
            console.error('读取存储数据失败:', error);
            return defaultValue;
        }
    }

    /**
     * 删除存储项
     */
    removeItem(key) {
        try {
            const fullKey = this.prefix + key;
            localStorage.removeItem(fullKey);
            return true;
        } catch (error) {
            console.error('删除存储数据失败:', error);
            return false;
        }
    }

    /**
     * 清空所有存储
     */
    clear() {
        try {
            const keys = Object.keys(localStorage);
            keys.forEach(key => {
                if (key.startsWith(this.prefix)) {
                    localStorage.removeItem(key);
                }
            });
            return true;
        } catch (error) {
            console.error('清空存储失败:', error);
            return false;
        }
    }

    /**
     * 保存选择的算法
     */
    saveSelectedAlgorithms(algorithms) {
        return this.setItem(this.keys.algorithms, algorithms);
    }

    /**
     * 获取选择的算法
     */
    getSelectedAlgorithms() {
        return this.getItem(this.keys.algorithms, []);
    }

    /**
     * 保存视频源配置
     */
    saveVideoSource(source) {
        return this.setItem(this.keys.videoSource, source);
    }

    /**
     * 获取视频源配置
     */
    getVideoSource() {
        return this.getItem(this.keys.videoSource, null);
    }

    /**
     * 保存应用设置
     */
    saveSettings(settings) {
        const currentSettings = this.getSettings();
        const mergedSettings = { ...currentSettings, ...settings };
        return this.setItem(this.keys.settings, mergedSettings);
    }

    /**
     * 获取应用设置
     */
    getSettings() {
        return this.getItem(this.keys.settings, {
            theme: 'dark',
            language: 'zh-CN',
            autoStart: false,
            notifications: true,
            voiceEnabled: true,
            alertSound: true
        });
    }

    /**
     * 保存语音启用状态
     */
    saveVoiceEnabled(enabled) {
        return this.setItem(this.keys.voiceEnabled, enabled);
    }

    /**
     * 获取语音启用状态
     */
    getVoiceEnabled() {
        return this.getItem(this.keys.voiceEnabled, true);
    }

    /**
     * 保存自定义模型
     */
    saveCustomModels(models) {
        return this.setItem(this.keys.customModels, models);
    }

    /**
     * 获取自定义模型
     */
    getCustomModels() {
        return this.getItem(this.keys.customModels, []);
    }

    /**
     * 添加自定义模型
     */
    addCustomModel(model) {
        const models = this.getCustomModels();
        models.push(model);
        return this.saveCustomModels(models);
    }

    /**
     * 删除自定义模型
     */
    removeCustomModel(modelId) {
        const models = this.getCustomModels();
        const filteredModels = models.filter(model => model.id !== modelId);
        return this.saveCustomModels(filteredModels);
    }

    /**
     * 保存对话历史
     */
    saveConversationHistory(history) {
        return this.setItem(this.keys.conversationHistory, history);
    }

    /**
     * 获取对话历史
     */
    getConversationHistory() {
        return this.getItem(this.keys.conversationHistory, []);
    }

    /**
     * 添加对话记录
     */
    addConversation(conversation) {
        const history = this.getConversationHistory();
        history.push({
            ...conversation,
            timestamp: new Date().toISOString()
        });
        
        // 限制历史记录数量
        if (history.length > 100) {
            history.splice(0, history.length - 100);
        }
        
        return this.saveConversationHistory(history);
    }

    /**
     * 清空对话历史
     */
    clearConversationHistory() {
        return this.removeItem(this.keys.conversationHistory);
    }

    /**
     * 导出所有数据
     */
    exportData() {
        const data = {};
        Object.values(this.keys).forEach(key => {
            data[key] = this.getItem(key);
        });
        return data;
    }

    /**
     * 导入数据
     */
    importData(data) {
        try {
            Object.entries(data).forEach(([key, value]) => {
                if (Object.values(this.keys).includes(key)) {
                    this.setItem(key, value);
                }
            });
            return true;
        } catch (error) {
            console.error('导入数据失败:', error);
            return false;
        }
    }

    /**
     * 获取存储使用情况
     */
    getStorageInfo() {
        try {
            let totalSize = 0;
            const items = {};
            
            Object.keys(localStorage).forEach(key => {
                if (key.startsWith(this.prefix)) {
                    const value = localStorage.getItem(key);
                    const size = new Blob([value]).size;
                    totalSize += size;
                    items[key.replace(this.prefix, '')] = {
                        size: size,
                        sizeFormatted: this.formatBytes(size)
                    };
                }
            });
            
            return {
                totalSize: totalSize,
                totalSizeFormatted: this.formatBytes(totalSize),
                items: items,
                available: this.getAvailableStorage()
            };
        } catch (error) {
            console.error('获取存储信息失败:', error);
            return null;
        }
    }

    /**
     * 获取可用存储空间
     */
    getAvailableStorage() {
        try {
            // 尝试估算可用空间
            let testKey = 'test_storage_size';
            let testData = '0';
            let currentSize = 0;
            
            // 二分查找最大可用空间
            let low = 0;
            let high = 10 * 1024 * 1024; // 10MB
            
            while (low < high) {
                let mid = Math.floor((low + high) / 2);
                try {
                    localStorage.setItem(testKey, testData.repeat(mid));
                    low = mid + 1;
                    currentSize = mid;
                } catch (e) {
                    high = mid;
                }
            }
            
            localStorage.removeItem(testKey);
            return this.formatBytes(currentSize);
        } catch (error) {
            return '未知';
        }
    }

    /**
     * 格式化字节大小
     */
    formatBytes(bytes) {
        if (bytes === 0) return '0 Bytes';
        
        const k = 1024;
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    /**
     * 检查存储是否可用
     */
    isStorageAvailable() {
        try {
            const testKey = 'test_storage';
            localStorage.setItem(testKey, 'test');
            localStorage.removeItem(testKey);
            return true;
        } catch (error) {
            return false;
        }
    }
}

// 创建全局存储管理器实例
window.storageManager = new StorageManager();

// 页面加载完成后初始化存储
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化存储管理器...');
    
    // 检查存储可用性
    if (!window.storageManager.isStorageAvailable()) {
        console.warn('本地存储不可用，某些功能可能受限');
        return;
    }
    
    // 恢复保存的设置
    restoreSavedData();
    
    console.log('存储管理器初始化完成');
});

/**
 * 恢复保存的数据
 */
function restoreSavedData() {
    try {
        // 恢复语音设置
        const voiceEnabled = window.storageManager.getVoiceEnabled();
        const voiceToggle = document.getElementById('voiceControlToggle');
        if (voiceToggle) {
            voiceToggle.checked = voiceEnabled;
        }
        
        // 恢复选择的算法
        const algorithms = window.storageManager.getSelectedAlgorithms();
        if (algorithms.length > 0 && window.appState) {
            window.appState.selectedAlgorithms = algorithms;
            
            // 更新UI显示
            if (typeof updateAlgorithmDisplay === 'function') {
                updateAlgorithmDisplay(algorithms);
            }
        }
        
        // 恢复应用设置
        const settings = window.storageManager.getSettings();
        applySettings(settings);
        
        console.log('已恢复保存的数据');
    } catch (error) {
        console.error('恢复保存数据失败:', error);
    }
}

/**
 * 应用设置
 */
function applySettings(settings) {
    // 应用主题
    if (settings.theme) {
        document.body.setAttribute('data-theme', settings.theme);
    }
    
    // 应用其他设置...
}

// 导出存储管理器
window.StorageManager = StorageManager;
