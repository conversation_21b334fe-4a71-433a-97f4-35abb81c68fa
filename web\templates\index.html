<!-- ai_security_monitor/frontend/index.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>AI安全监控系统</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <link href="/static/css/main.css" rel="stylesheet">
</head>
<body class="text-gray-200">
  <div id="particles-js"></div>

  <div class="container mx-auto px-4 py-8">
    <header class="mb-8">
      <div class="flex justify-between items-center">
        <div class="text-center flex-1">
          <h1 class="text-4xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
            AI安全监控系统
          </h1>
          <p class="text-gray-400">实时视频分析 · 异常行为检测 · 智能预警推送</p>
        </div>
        <div class="flex space-x-2">
          <a href="assistant.html" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition flex items-center">
            <i class="fas fa-history mr-2"></i>查看历史预警
          </a>
          <a href="/config" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition flex items-center">
            <i class="fas fa-cog mr-2"></i>系统配置
          </a>
        </div>
      </div>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-8 gap-6">
      <!-- 语音控制模块 - 左侧 -->
      <div class="lg:col-span-1">
        <div class="bg-gray-800 rounded-xl p-4 flex flex-col">
          <h3 class="text-lg font-semibold mb-4 flex items-center text-blue-400">
            <i class="fas fa-microphone mr-2"></i>
            语音控制
          </h3>

          <!-- 语音助手状态 -->
          <div class="mb-4">
            <div class="space-y-2 text-sm">
              <div class="flex justify-between items-center">
                <span class="text-gray-300">助手:</span>
                <span id="assistantStatus" class="px-2 py-1 rounded text-xs bg-red-600">未启动</span>
              </div>
            </div>
          </div>

          <!-- 语音波形显示 -->
          <div id="voiceWave" class="hidden mb-4 flex justify-center">
            <div class="voice-wave w-8 h-8 bg-blue-500 rounded-full"></div>
          </div>

          <!-- 控制按钮 -->
          <div class="space-y-2 mb-4">
            <button id="startVoiceBtn" onclick="startVoiceAssistant()"
                    class="w-full bg-green-600 hover:bg-green-700 py-2 rounded-lg text-sm font-medium">
              <i class="fas fa-play mr-2"></i>启动语音助手
            </button>

            <button id="stopVoiceBtn" onclick="stopVoiceAssistant()"
                    class="w-full bg-red-600 hover:bg-red-700 py-2 rounded-lg text-sm font-medium" disabled>
              <i class="fas fa-stop mr-2"></i>停止语音助手
            </button>
          </div>

          <!-- 快捷操作 -->
          <div class="mb-4">
            <h4 class="text-sm font-medium mb-2 text-gray-300">快捷操作</h4>
            <div class="space-y-2">
              <button onclick="clearConversation()"
                      class="w-full bg-orange-600 hover:bg-orange-700 py-2 rounded text-sm">
                <i class="fas fa-trash mr-2"></i>清空对话历史
              </button>
            </div>
          </div>

          <!-- 对话记录 -->
          <div>
            <div class="flex items-center justify-between mb-2">
              <h4 class="text-sm font-medium text-gray-300">对话记录</h4>
              <div id="conversationStats" class="text-xs text-gray-400">
                <span id="conversationCount">0</span> 条对话
              </div>
            </div>
            <div id="conversationHistory" class="bg-gray-700 rounded p-3 overflow-y-auto text-xs" style="height: 300px;">
              <div class="text-gray-400">暂无对话记录</div>
            </div>
          </div>
        </div>
      </div>

      <!-- 视频区域 -->
      <div class="lg:col-span-5">
        <div class="video-container rounded-xl overflow-hidden bg-gray-900">
          <div id="video-container" autoplay muted class="w-full h-auto max-h-[500px] object-cover">
              <img id="videoFeed" alt="实时视频流">
          </div>
          <div class="p-4 bg-gray-800">
            <!-- 视频状态和源信息 -->
            <div class="flex justify-between items-center mb-3">
              <div class="flex items-center">
                <span id="videoStatus" class="inline-block w-3 h-3 rounded-full bg-red-500 mr-2"></span>
                <span id="videoSourceDisplay" class="text-sm text-gray-300">未连接</span>
              </div>
              <div class="text-xs text-gray-400" id="currentVideoSource">
                加载中...
              </div>
            </div>

            <!-- 简化的视频源配置 -->
            <div class="bg-gray-700 rounded-lg p-3">
              <!-- 类型选择和RTSP应用按钮 -->
              <div class="flex items-center justify-between mb-3">
                <div class="flex items-center space-x-6">
                  <label class="flex items-center cursor-pointer">
                    <input type="radio" name="videoSourceType" value="rtsp" class="mr-2" checked>
                    <span class="text-sm">RTSP流</span>
                  </label>
                  <label class="flex items-center cursor-pointer">
                    <input type="radio" name="videoSourceType" value="file" class="mr-2">
                    <span class="text-sm">本地文件</span>
                  </label>
                </div>
                <!-- RTSP应用按钮 - 右上角 -->
                <button id="applyVideoSource" class="px-3 py-1 bg-green-600 hover:bg-green-700 rounded-md text-xs transition">
                  <i class="fas fa-check mr-1"></i>应用RTSP源
                </button>
              </div>

              <!-- RTSP输入 -->
              <div id="rtspSection">
                <input type="text" id="rtspUrl" placeholder="rtsp://192.168.1.100:554/stream"
                       class="w-full bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-sm">
              </div>

              <!-- 文件上传 -->
              <div id="fileUploadSection" class="hidden">
                <input type="file" id="videoFileInput" accept="video/*" class="hidden">
                <div class="flex justify-center space-x-2">
                  <button id="selectFileBtn" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md text-sm transition">
                    <i class="fas fa-upload mr-2"></i>选择视频文件
                  </button>
                  <!-- 文件应用按钮 - 与选择按钮并列居中 -->
                  <button id="applyFileSource" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md text-sm transition hidden">
                    <i class="fas fa-check mr-2"></i>应用视频文件
                  </button>
                </div>
                <div id="selectedFileName" class="text-xs text-gray-400 text-center">未选择文件</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 分析控制面板 -->
                <!-- 分析控制面板 -->
        <div class="mt-6 bg-gray-800 rounded-xl p-6">
          <h3 class="text-xl font-semibold mb-4">分析控制面板</h3>
          <!-- 选中的算法标签显示区域 -->
          <div class="grid grid-cols-1 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">已选择的检测模型</label>
              <div id="selectedAlgorithmTags" class="min-h-[60px] bg-gray-700 border border-gray-600 rounded-md p-3 flex flex-wrap gap-2">
                <div class="text-sm text-gray-400 flex items-center" id="noAlgorithmSelected">
                  <i class="fas fa-info-circle mr-2"></i>
                  请点击"高级设置"选择检测算法
                </div>
              </div>
            </div>
          </div>

          <div class="mt-6 flex justify-center space-x-4">
            <button id="startAnalysis" class="px-6 py-3 bg-green-600 hover:bg-green-700 rounded-md font-medium transition">
              <i class="fas fa-play mr-2"></i>开始分析
            </button>
            <button id="advancedSettings" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-md font-medium transition">
              <i class="fas fa-cogs mr-2"></i>高级设置
            </button>
          </div>
        </div>
      </div>

      <!-- 预警信息区域 -->
      <div class="lg:col-span-2">
        <!-- 实时预警 -->
        <div class="bg-gray-800 rounded-xl p-6 h-full flex flex-col">
          <div class="flex justify-between items-center mb-4">
            <h3 class="text-xl font-semibold">实时预警</h3>
            <span id="alertCount" class="bg-red-500 text-white text-xs font-bold px-2 py-1 rounded-full">0</span>
          </div>

             <div id="alertList" class="space-y-3" style="height: 400px; overflow-y: auto; padding-right: 8px;">
                <div id="alerts-list" class="space-y-3">
                    <!-- 警报将在这里显示 -->
                    <div class="text-center text-gray-400 text-sm py-8">
                        <i class="fas fa-shield-alt text-2xl mb-2"></i>
                        <p>暂无警报信息</p>
                    </div>
                </div>
          </div>

          <!-- 警报控制 -->
          <div class="mt-4 flex justify-between items-center">
            <div class="flex space-x-2">
              <button id="clearAlertsBtn" class="px-3 py-1 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                <i class="fas fa-trash mr-1"></i>清除警报
              </button>
            </div>
            <div class="flex items-center">
              <input type="checkbox" id="voiceControlToggle" checked class="mr-2 w-4 h-4 text-blue-600 bg-gray-700 border-gray-600 rounded focus:ring-blue-500 focus:ring-2">
              <label for="voiceControlToggle" class="text-sm text-gray-300 hover:text-white cursor-pointer">
                <i class="fas fa-volume-up mr-1"></i>启用语音播报
              </label>
            </div>
          </div>

          <!-- 知识库智能问答模块 -->
          <div class="mt-6 pt-4 border-t border-gray-700 flex-1 flex flex-col">
            <div class="flex items-center mb-4">
              <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3">
                <i class="fas fa-robot text-white"></i>
              </div>
              <h2 class="text-xl font-semibold">智能问答助手</h2>
            </div>

            <div class="flex-1 overflow-y-auto mb-4 space-y-3" id="chatContainer">
              <div class="chat-message rounded-lg p-3 max-w-xs">
                <p class="text-sm">您好！我是AI安全监控助手小凯，请问有什么可以帮您？</p>
                <p class="text-xs text-gray-400 mt-1 text-right">系统消息</p>
              </div>
            </div>

            <div class="flex">
              <input type="text" id="questionInput" placeholder="输入您的问题..."
                     class="flex-1 bg-gray-700 border border-gray-600 rounded-l-md px-3 py-2 focus:outline-none focus:ring-2 focus:ring-purple-500">

              <button id="sendQuestion" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded-r-md transition">
                <i class="fas fa-paper-plane"></i>
              </button>
            </div>
          </div>


        </div>
      </div>
    </div>
  </div>

  <!-- 上传视频模态框 -->
  <div id="uploadModal" class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center hidden">
    <div class="bg-gray-800 rounded-xl p-6 w-full max-w-md">
      <div class="flex justify-between items-center mb-4">
        <h3 class="text-xl font-semibold">上传视频分析</h3>
        <button id="closeUploadModal" class="text-gray-400 hover:text-white">
          <i class="fas fa-times"></i>
        </button>
      </div>
      <div class="border-2 border-dashed border-gray-600 rounded-lg p-8 text-center">
        <i class="fas fa-cloud-upload-alt text-4xl text-blue-500 mb-3"></i>
        <p class="mb-4">拖放视频文件到此处或点击选择</p>
        <input type="file" id="videoFile" accept="video/*" class="hidden">
        <button id="selectVideo" class="px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded-md transition">
          选择文件
        </button>
      </div>
      <div class="mt-4 flex justify-end space-x-3">
        <button id="cancelUpload" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md transition">
          取消
        </button>
        <button id="confirmUpload" class="px-4 py-2 bg-green-600 hover:bg-green-700 rounded-md transition" disabled>
          开始分析
        </button>
      </div>
    </div>
  </div>

  <!-- 脚本引入 -->
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>

  <!-- 核心脚本 -->
  <script src="/static/js/storage.js"></script>
  <script src="/static/js/websocket.js"></script>
  <script src="/static/js/main.js"></script>

  <!-- 功能模块脚本 -->
  <script src="/static/js/particles.js"></script>
  <script src="/static/js/voice.js"></script>
  <script src="/static/js/video.js"></script>
  <script src="/static/js/alerts.js"></script>
  <script src="/static/js/video-config.js"></script>
  <script src="/static/js/voice-assistant.js"></script>
  <script src="/static/js/advanced-settings.js"></script>
  <script src="/static/js/chat.js"></script>
  <script src="/static/js/utils.js"></script>























    <!-- <script>
     window.difyChatbotConfig = {
      token: 'UaIvYCE9BRHuDdhG',
      baseUrl: 'http://localhost',
      systemVariables: {
        // user_id: 'YOU CAN DEFINE USER ID HERE',
        // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',
      },
     }
    </script>
    <script
     src="http://localhost/embed.min.js"
     id="UaIvYCE9BRHuDdhG"
     defer>
    </script>
    <style>
      #dify-chatbot-bubble-button {
        background-color: #16213e !important;
      }
      #dify-chatbot-bubble-window {
        width: 24rem !important;
        height: 40rem !important;
      }
    </style> -->
      <!-- 高级设置弹框 -->
  <div id="advancedModal" class="fixed inset-0 bg-black bg-opacity-50 hidden z-50 flex items-center justify-center">
    <div class="bg-gray-800 rounded-xl p-6 max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="flex justify-between items-center mb-6">
        <h2 class="text-2xl font-bold text-white">选择算法</h2>
        <button id="closeModal" class="text-gray-400 hover:text-white text-2xl">
          <i class="fas fa-times"></i>
        </button>
      </div>

      <!-- 场景选择 -->
      <div class="mb-6">
        <div class="flex flex-wrap gap-2 mb-4">
          <button class="scenario-tab active px-4 py-2 bg-blue-600 text-white rounded-md text-sm" data-scenario="all">全部监测模型</button>
          <button class="scenario-tab px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 text-sm" data-scenario="industrial">工业生产监测</button>
          <button class="scenario-tab px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 text-sm" data-scenario="childcare">母婴育儿监测</button>
          <button class="scenario-tab px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 text-sm" data-scenario="elderly">养老监测</button>
          <button class="scenario-tab px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 text-sm" data-scenario="pet">宠物监测</button>
          <button class="scenario-tab px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 text-sm" data-scenario="non-camera">非摄像头场景监测</button>
          <button class="scenario-tab px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-500 text-sm" data-scenario="custom">自定义模型</button>
        </div>
      </div>

      <!-- 算法选择网格 -->
      <div class="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-3 mb-6" id="algorithmGrid">
        <!-- 安全防护类 -->
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="safety">
          <input type="checkbox" name="algorithms" value="helmet" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">安全帽检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="safety">
          <input type="checkbox" name="algorithms" value="uniform" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">工作服检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="safety">
          <input type="checkbox" name="algorithms" value="reflective" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">反光衣识别</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="safety">
          <input type="checkbox" name="algorithms" value="chef-hat" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">厨师帽识别</span>
        </label>

        <!-- 行为检测类 -->
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="behavior">
          <input type="checkbox" name="algorithms" value="smoking" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">吸烟检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="behavior">
          <input type="checkbox" name="algorithms" value="phone" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">打电话检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="behavior">
          <input type="checkbox" name="algorithms" value="fighting" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">打架识别</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="behavior">
          <input type="checkbox" name="algorithms" value="weapon" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">持械识别</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="behavior">
          <input type="checkbox" name="algorithms" value="fall" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">跌倒检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="behavior">
          <input type="checkbox" name="algorithms" value="sleep" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">睡岗检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="behavior">
          <input type="checkbox" name="algorithms" value="leave" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">离岗检测</span>
        </label>

        <!-- 区域监控类 -->
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="area">
          <input type="checkbox" name="algorithms" value="perimeter" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">周界入侵检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="area">
          <input type="checkbox" name="algorithms" value="intrusion" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">区域入侵检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="area">
          <input type="checkbox" name="algorithms" value="fence" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">电子围栏</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="area">
          <input type="checkbox" name="algorithms" value="loitering" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">徘徊检测</span>
        </label>

        <!-- 计数统计类 -->
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="counting">
          <input type="checkbox" name="algorithms" value="counting" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">计数检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="counting">
          <input type="checkbox" name="algorithms" value="line-crossing" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">越线计数</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="counting">
          <input type="checkbox" name="algorithms" value="overcrowd" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">人员超限检测</span>
        </label>

        <!-- 识别检测类 -->
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="recognition">
          <input type="checkbox" name="algorithms" value="face" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">人脸识别</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="recognition">
          <input type="checkbox" name="algorithms" value="license-plate" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">车牌识别</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="recognition">
          <input type="checkbox" name="algorithms" value="stranger" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">陌生人告警</span>
        </label>

        <!-- 环境安全类 -->
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="environment">
          <input type="checkbox" name="algorithms" value="fire" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">火焰烟火检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="environment">
          <input type="checkbox" name="algorithms" value="open-fire" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">明火检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="environment">
          <input type="checkbox" name="algorithms" value="dust" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">烟尘检测</span>
        </label>
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="environment">
          <input type="checkbox" name="algorithms" value="fire-exit" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">消防通道阻塞识别</span>
        </label>

        <!-- 作业规范类 -->
        <label class="algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition" data-category="operation">
          <input type="checkbox" name="algorithms" value="standard-operation" class="mr-3 accent-blue-500">
          <span class="text-sm text-white">规范作业检测</span>
        </label>
      </div>

      <!-- 自定义模型添加区域 -->
      <div id="customModelSection" class="mb-6 hidden">
        <div class="bg-gray-700 rounded-lg p-4">
          <h3 class="text-lg font-medium text-white mb-4">添加自定义模型</h3>
          <div class="flex gap-3 mb-3">
            <input type="text" id="customModelName" placeholder="输入模型名称，如：口罩检测、温度检测等"
                   class="flex-1 bg-gray-600 border border-gray-500 rounded-md px-3 py-2 text-white placeholder-gray-400">
            <button id="addCustomModel" class="px-4 py-2 bg-green-600 hover:bg-green-700 text-white rounded-md transition">
              <i class="fas fa-plus mr-1"></i>添加
            </button>
          </div>
          <p class="text-xs text-gray-400">您可以添加系统中没有的检测模型，如特定行业的专用检测算法</p>

          <!-- 已添加的自定义模型列表 -->
          <div id="customModelList" class="mt-4 space-y-2">
            <!-- 动态添加的自定义模型会显示在这里 -->
          </div>
        </div>
      </div>

      <!-- 确认按钮 -->
      <div class="flex justify-end space-x-4">
        <button id="cancelSettings" class="px-6 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded-md transition">
          取消
        </button>
        <button id="confirmSettings" class="px-6 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md transition">
          确认
        </button>
      </div>
    </div>
  </div>
</body>
</html>