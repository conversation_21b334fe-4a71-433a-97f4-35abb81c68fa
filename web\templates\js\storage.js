
// ai_security_monitor/frontend/js/storage.js
class AlertStorage {
  constructor() {
    this.storageKey = 'ai_security_alerts';
    this.rulesKey = 'ai_custom_rules';
    this.initStorage();
  }

  initStorage() {
    if (!localStorage.getItem(this.storageKey)) {
      localStorage.setItem(this.storageKey, JSON.stringify([]));
    }
    if (!localStorage.getItem(this.rulesKey)) {
      localStorage.setItem(this.rulesKey, JSON.stringify([]));
    }
  }

  // 保存预警信息
  saveAlert(alertData) {
    const alerts = this.getAllAlerts();
    alertData.id = Date.now().toString();
    alerts.unshift(alertData);
    localStorage.setItem(this.storageKey, JSON.stringify(alerts));
    return alertData.id;
  }

  // 获取所有预警信息
  getAllAlerts() {
    return JSON.parse(localStorage.getItem(this.storageKey));
  }

  // 根据ID获取预警信息
  getAlertById(id) {
    const alerts = this.getAllAlerts();
    return alerts.find(alert => alert.id === id);
  }

  // 删除预警信息
  deleteAlert(id) {
    let alerts = this.getAllAlerts();
    alerts = alerts.filter(alert => alert.id !== id);
    localStorage.setItem(this.storageKey, JSON.stringify(alerts));
    return true;
  }

  // 清空所有预警信息
  clearAllAlerts() {
    localStorage.setItem(this.storageKey, JSON.stringify([]));
    return true;
  }

  // 保存自定义规则
  saveCustomRule(rule) {
    const rules = this.getAllCustomRules();
    rule.id = Date.now().toString();
    rules.push(rule);
    localStorage.setItem(this.rulesKey, JSON.stringify(rules));
    return rule.id;
  }

  // 获取所有自定义规则
  getAllCustomRules() {
    return JSON.parse(localStorage.getItem(this.rulesKey));
  }

  // 根据ID获取自定义规则
  getCustomRuleById(id) {
    const rules = this.getAllCustomRules();
    return rules.find(rule => rule.id === id);
  }

  // 更新自定义规则
  updateCustomRule(id, updatedRule) {
    let rules = this.getAllCustomRules();
    rules = rules.map(rule => {
      if (rule.id === id) {
        return { ...rule, ...updatedRule };
      }
      return rule;
    });
    localStorage.setItem(this.rulesKey, JSON.stringify(rules));
    return true;
  }

  // 删除自定义规则
  deleteCustomRule(id) {
    let rules = this.getAllCustomRules();
    rules = rules.filter(rule => rule.id !== id);
    localStorage.setItem(this.rulesKey, JSON.stringify(rules));
    return true;
  }

  // 获取分页预警信息
  getAlertsByPage(page = 1, pageSize = 10) {
    const alerts = this.getAllAlerts();
    const startIndex = (page - 1) * pageSize;
    const endIndex = startIndex + pageSize;
    return {
      data: alerts.slice(startIndex, endIndex),
      total: alerts.length,
      page,
      pageSize
    };
  }

  // 根据类型筛选预警信息
  getAlertsByType(type) {
    const alerts = this.getAllAlerts();
    return alerts.filter(alert => alert.type === type);
  }

  // 根据时间范围筛选预警信息
  getAlertsByTimeRange(startTime, endTime) {
    const alerts = this.getAllAlerts();
    return alerts.filter(alert => {
      const alertTime = new Date(alert.timestamp).getTime();
      return alertTime >= new Date(startTime).getTime() && 
             alertTime <= new Date(endTime).getTime();
    });
  }
}

// 全局存储实例
const alertStorage = new AlertStorage();

// 导出存储方法
export { alertStorage };
