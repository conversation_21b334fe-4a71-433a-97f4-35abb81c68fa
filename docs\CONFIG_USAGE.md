# AI安全监控系统 - 配置管理功能使用说明

## 概述

本系统现在支持通过前端界面动态配置各种参数，包括视频源、模型参数和系统设置，无需重启服务即可生效。

## 功能特性

### 1. 动态视频源配置
- **文件上传**: 支持上传本地视频文件（MP4, AVI, MOV等格式）
- **RTSP流**: 支持配置网络摄像头的RTSP地址
- **实时切换**: 无需重启服务即可切换视频源

### 2. 视频处理参数配置
- **视频分段时长**: 控制视频归档的分段间隔
- **分析间隔**: 设置异常检测的触发频率
- **缓冲时长**: 配置视频分析的缓冲区大小
- **JPEG质量**: 调整视频流的压缩质量

### 3. API配置管理
- **Qwen API**: 配置通义千问API的地址和模型
- **Moonshot API**: 配置月之暗面API的相关参数
- **请求参数**: 设置超时时间、温度参数等

### 4. RAG系统配置
- **Milvus数据库**: 配置向量数据库连接参数
- **向量API**: 设置嵌入向量生成服务
- **历史记录**: 配置历史数据存储方式

## 使用方法

### 访问配置页面
1. 启动系统后，访问主页面
2. 点击右上角的"系统配置"按钮
3. 或直接访问 `http://localhost:16532/config`

### 配置视频源

#### 方法1: 上传本地视频文件
1. 在配置页面选择"本地文件"选项
2. 点击上传区域或拖拽视频文件到上传区域
3. 等待上传完成
4. 点击"应用设置"按钮

#### 方法2: 配置RTSP流
1. 选择"RTSP流"选项
2. 输入RTSP地址，例如：`rtsp://admin:password@*************:554/stream`
3. 点击"应用设置"按钮

### 配置系统参数
1. 在相应的配置卡片中修改参数值
2. 点击对应的"保存配置"按钮
3. 系统会自动应用新的配置

## API接口

### 获取当前配置
```http
GET /api/config
```

### 更新配置
```http
POST /api/config
Content-Type: application/json

{
  "video_interval": 1800,
  "analysis_interval": 10,
  "buffer_duration": 11,
  "jpeg_quality": 70
}
```

### 上传视频文件
```http
POST /api/upload-video
Content-Type: multipart/form-data

file: [视频文件]
```

### 设置视频源
```http
POST /api/set-video-source
Content-Type: application/json

{
  "video_source": "rtsp://*************:554/stream"
}
```

## 配置参数说明

### 视频处理配置
- `video_interval`: 视频分段时长（秒），默认1800（30分钟）
- `analysis_interval`: 分析间隔（秒），默认10秒
- `buffer_duration`: 缓冲时长（秒），默认11秒
- `jpeg_quality`: JPEG质量（1-100），默认70

### API配置
- `qwen_api_url`: 通义千问API地址
- `qwen_model`: 使用的Qwen模型名称
- `moonshot_api_url`: Moonshot API地址
- `moonshot_model`: 使用的Moonshot模型名称
- `request_timeout`: 请求超时时间（秒）
- `temperature`: 模型温度参数（0-2）

### RAG配置
- `enable_rag`: 是否启用RAG系统
- `milvus_host`: Milvus数据库主机地址
- `milvus_port`: Milvus数据库端口
- `collection_name`: 向量集合名称
- `vector_api_url`: 向量生成API地址
- `history_file`: 历史记录文件路径

## 注意事项

1. **视频源切换**: 切换视频源时会短暂中断视频流，这是正常现象
2. **文件格式**: 上传的视频文件必须是系统支持的格式（MP4, AVI, MOV等）
3. **RTSP地址**: 确保RTSP地址格式正确且网络可达
4. **参数范围**: 请确保配置参数在合理范围内，系统会进行基本验证
5. **权限要求**: 某些配置可能需要管理员权限

## 故障排除

### 视频源无法切换
- 检查视频文件格式是否支持
- 验证RTSP地址是否正确
- 查看浏览器控制台的错误信息

### 配置保存失败
- 检查网络连接
- 验证参数值是否在有效范围内
- 查看服务器日志获取详细错误信息

### 上传文件失败
- 检查文件大小是否超出限制
- 确认文件类型为视频格式
- 检查磁盘空间是否充足

## 技术实现

### 前端技术
- HTML5 + CSS3 + JavaScript
- Tailwind CSS框架
- 拖拽上传功能
- 实时配置更新

### 后端技术
- FastAPI框架
- 异步视频源切换
- 配置热更新
- 文件上传处理

### 核心特性
- 无需重启的动态配置
- 线程安全的视频源切换
- 实时配置验证
- 用户友好的界面设计
