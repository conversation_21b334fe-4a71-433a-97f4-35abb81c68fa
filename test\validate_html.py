#!/usr/bin/env python3
"""
HTML结构验证脚本
检查div标签是否正确配对
"""

def validate_html_structure(file_path):
    """验证HTML文件中的div标签配对"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 简单的div标签配对检查
        stack = []
        lines = content.split('\n')
        
        for line_num, line in enumerate(lines, 1):
            line = line.strip()
            
            # 查找开始标签
            if '<div' in line and not line.endswith('/>'):
                # 计算这一行有多少个开始div标签
                open_count = line.count('<div')
                close_count = line.count('</div>')
                
                for _ in range(open_count):
                    stack.append(line_num)
                for _ in range(close_count):
                    if stack:
                        stack.pop()
                    else:
                        print(f"❌ 第{line_num}行: 多余的闭合标签 </div>")
                        print(f"   内容: {line}")
                        return False
            
            # 查找闭合标签
            elif '</div>' in line:
                close_count = line.count('</div>')
                for _ in range(close_count):
                    if stack:
                        stack.pop()
                    else:
                        print(f"❌ 第{line_num}行: 多余的闭合标签 </div>")
                        print(f"   内容: {line}")
                        return False
        
        if stack:
            print(f"❌ 有 {len(stack)} 个未闭合的 <div> 标签")
            print(f"   未闭合的标签位于行: {stack}")
            return False
        
        print("✅ HTML div标签结构验证通过")
        return True
        
    except Exception as e:
        print(f"❌ 验证过程中出错: {e}")
        return False

if __name__ == "__main__":
    file_path = "templates/index.html"
    print("🔍 开始验证HTML结构...")
    print("=" * 50)
    
    result = validate_html_structure(file_path)
    
    print("=" * 50)
    if result:
        print("🎉 验证完成: HTML结构正确")
    else:
        print("⚠️  验证完成: 发现结构问题")
