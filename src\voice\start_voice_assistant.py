#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音助手启动脚本
功能：启动集成了FunASR的语音助手系统
"""

import asyncio
import logging
import sys
import os

# 添加项目根目录到Python路径
project_root = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
sys.path.insert(0, project_root)

from src.voice.voice_controller import VoiceAssistantController

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.StreamHandler(),
        logging.FileHandler('voice_assistant.log', encoding='utf-8')
    ]
)

logger = logging.getLogger(__name__)

def print_banner():
    """打印启动横幅"""
    print("\n" + "=" * 60)
    print("🎙️ AI安全监控系统 - 语音助手")
    print("=" * 60)
    print("🔧 集成FunASR语音识别")
    print("🎯 支持唤醒词: 小凯小凯、你好小凯、小凯同学")
    print("🌐 FunASR服务器: ws://192.168.3.95:10096/")
    print("=" * 60)
    print("💡 使用说明:")
    print("   1. 说出唤醒词激活语音助手")
    print("   2. 听到提示音后说出指令")
    print("   3. 按 Ctrl+C 退出程序")
    print("=" * 60)

def print_commands():
    """打印可用命令"""
    print("\n📋 可用语音指令:")
    print("   • 查询今天的警报")
    print("   • 显示统计信息")
    print("   • 控制监控系统")
    print("   • 帮助")
    print("   • 退出")
    print()

async def main():
    """主函数"""
    print_banner()
    print_commands()
    
    # 检查依赖
    try:
        import websockets
        print("✅ WebSocket支持已启用 (FunASR)")
    except ImportError:
        print("⚠️ WebSocket支持未启用，将使用Whisper备用")
    
    try:
        import whisper
        print("✅ Whisper支持已启用")
    except ImportError:
        print("⚠️ Whisper支持未启用，将使用模拟识别")
    
    try:
        import sounddevice
        print("✅ 音频设备支持已启用")
    except ImportError:
        print("❌ 音频设备支持未启用，无法录音")
        return
    
    print("\n🚀 启动语音助手...")
    
    # 创建语音助手控制器
    controller = VoiceAssistantController()
    
    try:
        # 启动语音助手
        await controller.start()
        
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，正在关闭语音助手...")
        
    except Exception as e:
        logger.error(f"语音助手运行异常: {e}")
        print(f"\n❌ 语音助手异常: {e}")
        
    finally:
        # 清理资源
        try:
            await controller.stop()
            print("✅ 语音助手已安全关闭")
        except:
            pass

if __name__ == "__main__":
    print("🎙️ 正在启动AI安全监控语音助手...")
    
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        # Python 3.6兼容性
        loop = asyncio.get_event_loop()
        try:
            loop.run_until_complete(main())
        finally:
            loop.close()
    except KeyboardInterrupt:
        print("\n👋 再见！")
    except Exception as e:
        print(f"\n❌ 启动失败: {e}")
        import traceback
        traceback.print_exc()
