# AI Security Monitoring System

An intelligent security monitoring system with AI-powered anomaly detection, voice assistant, and real-time alert capabilities.

## Features

### 🎥 Video Monitoring
- Real-time video stream processing
- Multiple video source support (file upload, RTSP streams)
- Intelligent frame analysis and anomaly detection
- Automatic video segmentation and archiving

### 🤖 AI-Powered Detection
- Multi-modal anomaly detection
- Custom model integration
- Configurable detection confidence levels
- Real-time alert generation

### 🎙️ Voice Assistant
- Voice wake-up functionality
- Speech recognition and synthesis
- Natural language command processing
- System control through voice commands

### 📊 Statistics and Analytics
- Real-time alert statistics
- Historical data analysis
- Customizable dashboards
- Export capabilities

### ⚙️ Configuration Management
- Dynamic configuration updates
- Web-based settings interface
- API configuration support
- Hot-reload capabilities

## Technology Stack

- **Backend**: FastAPI, Python 3.6+
- **Frontend**: HTML5, CSS3, JavaScript, Tailwind CSS
- **AI/ML**: OpenCV, Custom detection models
- **Audio**: SoundDevice, Whisper, Edge-TTS
- **Database**: JSON-based storage, Milvus (optional)
- **Communication**: WebSocket, HTTP APIs

## Installation

### Prerequisites
- Python 3.6 or higher
- Audio devices (microphone and speakers for voice features)
- Camera or video source

### Dependencies
```bash
pip install -r requirements.txt
```

### Core Dependencies
- fastapi==0.115.5
- uvicorn==0.32.1
- opencv-python==*********
- numpy==2.2.2
- websockets==12.0
- httpx==0.27.2

### Optional Dependencies
```bash
# For voice features
pip install sounddevice
pip install openai-whisper
pip install edge-tts
pip install pygame
```

## Quick Start

1. **Clone the repository**
```bash
git clone <repository-url>
cd aiWatchdog0703
```

2. **Install dependencies**
```bash
pip install -r requirements.txt
```

3. **Start the server**
```bash
python video_server.py
```

4. **Access the web interface**
Open your browser and navigate to `http://localhost:25521`

## Configuration

### Basic Configuration
Edit `config.py` to modify system settings:

```python
# Server configuration
HOST = "0.0.0.0"
PORT = 25521

# Detection settings
ANOMALY_SURVEILLANCE = False
DETECTION_CONFIDENCE = 0.5

# Voice settings
VOICE_ENABLED = True
SAMPLE_RATE = 16000
```

### Web Configuration
Access the configuration page at `http://localhost:25521/config` to:
- Upload video files or configure RTSP streams
- Adjust detection parameters
- Configure API settings
- Manage custom models

## API Documentation

### Video Source Management
```http
POST /api/set-video-source
Content-Type: application/json

{
  "video_source": "path/to/video.mp4"
}
```

### Voice Control
```http
POST /api/voice/control
Content-Type: application/json

{
  "action": "start"
}
```

### Alert Statistics
```http
GET /api/alert-statistics?days=7
```

## Voice Assistant Commands

### Query Commands
- "Query today's alerts"
- "Show statistics"
- "Display system status"

### Control Commands
- "Start monitoring"
- "Stop monitoring"
- "Switch video source"

### Help Commands
- "Help"
- "Show functions"
- "Usage instructions"

## Embedded Device Integration

The system supports integration with embedded devices featuring:
- Camera modules
- Microphone and speaker
- Network connectivity

### Device Configuration
1. Configure network connection (WiFi/Ethernet)
2. Set up video streaming (RTMP/HTTP)
3. Configure audio streaming (WebSocket)
4. Deploy auto-start services

### Streaming Protocols
- **Video**: RTMP, HTTP POST
- **Audio**: WebSocket with base64 encoding
- **Control**: HTTP API calls

## Troubleshooting

### Audio Issues
If you encounter audio device errors:
1. The system automatically falls back to SoundDevice
2. If no audio devices are available, mock mode is used
3. Check Windows audio settings and microphone permissions

### Video Source Issues
- Ensure video file formats are supported (MP4, AVI, MOV)
- Verify RTSP stream URLs are correct and accessible
- Check network connectivity for remote streams

### Performance Issues
- Adjust detection intervals in configuration
- Reduce video quality if needed
- Monitor system resources

## Development

### Project Structure
```
aiWatchdog0703/
├── video_server.py          # Main server application
├── multi_modal_analyzer.py  # AI detection engine
├── voice_assistant.py       # Voice functionality
├── voice_controller.py      # Voice control logic
├── alert_statistics.py     # Statistics management
├── config.py               # System configuration
├── templates/              # Web interface templates
├── static/                 # Static assets
└── uploads/               # Uploaded files
```

### Adding Custom Models
1. Implement detection logic in `multi_modal_analyzer.py`
2. Add model configuration to `custom_models.json`
3. Update the web interface for model management

### Extending Voice Commands
1. Add command patterns to `voice_controller.py`
2. Implement command handlers
3. Update voice recognition logic

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## License

This project is licensed under the MIT License - see the LICENSE file for details.

## Support

For support and questions:
- Check the troubleshooting section
- Review the configuration documentation
- Submit issues on the project repository

## Changelog

### v1.2.0
- Added multi-audio backend support
- Implemented automatic device detection
- Added mock mode fallback mechanism
- Improved error handling and fault tolerance

### v1.1.0
- Integrated Whisper speech recognition
- Added Edge-TTS speech synthesis
- Implemented multi-turn conversation

### v1.0.0
- Basic voice wake-up functionality
- Simple command recognition
- Basic voice announcements
