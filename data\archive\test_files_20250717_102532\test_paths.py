#!/usr/bin/env python3
"""
测试路径修复是否成功
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_video_warning_paths():
    """测试video_warning相关路径"""
    print("测试video_warning路径修复...")
    
    # 测试目录是否存在
    video_warning_dir = "data/video_warning"
    if os.path.exists(video_warning_dir):
        print(f"✅ 目录存在: {video_warning_dir}")
    else:
        print(f"❌ 目录不存在: {video_warning_dir}")
        os.makedirs(video_warning_dir, exist_ok=True)
        print(f"🆕 已创建目录: {video_warning_dir}")
    
    # 测试创建输出文件
    output_file = os.path.join(video_warning_dir, "output.mp4")
    try:
        # 创建一个空的测试文件
        with open(output_file, 'w') as f:
            f.write("")
        print(f"✅ 可以创建文件: {output_file}")
        
        # 删除测试文件
        os.remove(output_file)
        print(f"✅ 文件操作正常")
        
    except Exception as e:
        print(f"❌ 文件操作失败: {e}")

def test_config_paths():
    """测试配置文件中的路径"""
    print("\n测试配置文件路径...")
    
    try:
        from src.core.config import VIDEO_WARNING_DIR, VIDEO_SOURCE
        print(f"✅ VIDEO_WARNING_DIR: {VIDEO_WARNING_DIR}")
        print(f"✅ VIDEO_SOURCE: {VIDEO_SOURCE}")
        
        # 检查视频源是否存在
        if os.path.exists(VIDEO_SOURCE):
            print(f"✅ 视频源文件存在: {VIDEO_SOURCE}")
        else:
            print(f"❌ 视频源文件不存在: {VIDEO_SOURCE}")
            
    except Exception as e:
        print(f"❌ 配置导入失败: {e}")

def test_utility_paths():
    """测试utility.py中的路径"""
    print("\n测试utility.py路径...")
    
    try:
        # 检查utility.py文件内容
        with open("utils/utility.py", 'r', encoding='utf-8') as f:
            content = f.read()
            
        if "data/video_warning/output.mp4" in content:
            print("✅ utility.py 路径已正确更新")
        else:
            print("❌ utility.py 路径未更新")
            
        if "./video_warning/" in content:
            print("❌ utility.py 仍包含旧路径")
        else:
            print("✅ utility.py 不包含旧路径")
            
    except Exception as e:
        print(f"❌ 检查utility.py失败: {e}")

def main():
    print("=" * 50)
    print("路径修复测试")
    print("=" * 50)
    
    test_video_warning_paths()
    test_config_paths()
    test_utility_paths()
    
    print("\n" + "=" * 50)
    print("测试完成!")
    print("如果所有测试都通过，video_warning路径错误应该已修复")

if __name__ == "__main__":
    main()
