#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR调试脚本
功能：详细诊断FunASR识别失败的原因并提供解决方案
"""

import asyncio
import json
import logging
import wave
import base64
import sounddevice as sd
import numpy as np
import os
import io

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装")

# 配置详细日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# FunASR配置
FUNASR_WS_URL = "ws://192.168.3.95:10096/"
FUNASR_TIMEOUT = 15
SAMPLE_RATE = 16000

class FunASRDebugger:
    """FunASR调试器"""
    
    def __init__(self):
        self.ws_url = FUNASR_WS_URL
        self.timeout = FUNASR_TIMEOUT
    
    def print_banner(self):
        """打印调试横幅"""
        print("\n" + "=" * 60)
        print("🔍 FunASR详细调试分析")
        print("=" * 60)
        print(f"服务器地址: {self.ws_url}")
        print(f"超时设置: {self.timeout}秒")
        print("=" * 60)
    
    def analyze_audio_file(self, audio_file):
        """分析音频文件属性"""
        print(f"\n📊 分析音频文件: {audio_file}")
        
        try:
            # 文件大小
            file_size = os.path.getsize(audio_file)
            print(f"文件大小: {file_size} 字节")
            
            # WAV文件分析
            with wave.open(audio_file, 'rb') as wav_file:
                frames = wav_file.getnframes()
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                duration = frames / sample_rate
                
                print(f"采样率: {sample_rate} Hz")
                print(f"声道数: {channels}")
                print(f"采样位数: {sample_width * 8} bit")
                print(f"总帧数: {frames}")
                print(f"时长: {duration:.2f} 秒")
                
                # 读取音频数据
                audio_data = wav_file.readframes(frames)
                print(f"音频数据长度: {len(audio_data)} 字节")
                
                # 分析音频强度
                audio_array = np.frombuffer(audio_data, dtype=np.int16)
                max_amplitude = np.max(np.abs(audio_array))
                rms = np.sqrt(np.mean(audio_array.astype(np.float64) ** 2))
                
                print(f"最大振幅: {max_amplitude}")
                print(f"RMS值: {rms:.2f}")
                
                if max_amplitude < 1000:
                    print("⚠️ 音频信号可能太弱")
                elif max_amplitude > 30000:
                    print("⚠️ 音频信号可能过强")
                else:
                    print("✅ 音频信号强度正常")
                
                return {
                    'file_size': file_size,
                    'sample_rate': sample_rate,
                    'channels': channels,
                    'sample_width': sample_width,
                    'duration': duration,
                    'max_amplitude': max_amplitude,
                    'rms': rms,
                    'audio_data': audio_data
                }
                
        except Exception as e:
            print(f"❌ 音频文件分析失败: {e}")
            return None
    
    def convert_to_pcm_detailed(self, audio_data):
        """详细的PCM转换过程"""
        print("\n🔄 详细PCM转换过程...")
        
        try:
            if audio_data.startswith(b'RIFF'):
                print("✅ 检测到WAV格式")
                
                with io.BytesIO(audio_data) as wav_io:
                    with wave.open(wav_io, 'rb') as wav_file:
                        # 获取WAV文件信息
                        sample_rate = wav_file.getframerate()
                        channels = wav_file.getnchannels()
                        sample_width = wav_file.getsampwidth()
                        frames = wav_file.getnframes()
                        
                        print(f"WAV信息: {sample_rate}Hz, {channels}ch, {sample_width*8}bit")
                        
                        # 检查是否符合FunASR要求
                        if sample_rate != 16000:
                            print(f"⚠️ 采样率不匹配，期望16000Hz，实际{sample_rate}Hz")
                        if channels != 1:
                            print(f"⚠️ 声道数不匹配，期望1声道，实际{channels}声道")
                        if sample_width != 2:
                            print(f"⚠️ 采样位数不匹配，期望16bit，实际{sample_width*8}bit")
                        
                        # 提取PCM数据
                        pcm_data = wav_file.readframes(frames)
                        print(f"✅ PCM数据提取成功，长度: {len(pcm_data)} 字节")
                        
                        # 验证PCM数据
                        if len(pcm_data) == 0:
                            print("❌ PCM数据为空")
                            return None
                        
                        # 分析PCM数据
                        pcm_array = np.frombuffer(pcm_data, dtype=np.int16)
                        print(f"PCM数组长度: {len(pcm_array)}")
                        print(f"PCM最大值: {np.max(pcm_array)}")
                        print(f"PCM最小值: {np.min(pcm_array)}")
                        
                        return pcm_data
            else:
                print("⚠️ 不是WAV格式，直接使用原始数据")
                return audio_data
                
        except Exception as e:
            print(f"❌ PCM转换失败: {e}")
            import traceback
            traceback.print_exc()
            return audio_data
    
    async def test_funasr_with_debug(self, audio_file):
        """带详细调试的FunASR测试"""
        print(f"\n🧠 详细调试FunASR识别: {audio_file}")
        
        # 1. 分析音频文件
        audio_info = self.analyze_audio_file(audio_file)
        if not audio_info:
            return None
        
        try:
            # 2. 读取音频文件
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
            
            # 3. 详细PCM转换
            pcm_data = self.convert_to_pcm_detailed(audio_data)
            if not pcm_data:
                print("❌ PCM转换失败")
                return None
            
            # 4. Base64编码
            b64_data = base64.b64encode(pcm_data).decode()
            print(f"Base64编码长度: {len(b64_data)}")
            
            # 5. 获取文件名
            wav_name = os.path.splitext(os.path.basename(audio_file))[0]
            print(f"使用文件名: {wav_name}")
            
            # 6. 连接WebSocket
            print(f"\n🔗 连接FunASR服务器...")
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:
                print("✅ WebSocket连接成功")
                
                # 7. 发送开始信号
                start_msg = {
                    "mode": "offline",
                    "chunk_size": [5, 10, 5],
                    "chunk_interval": 10,
                    "wav_name": wav_name
                }
                start_json = json.dumps(start_msg)
                await websocket.send(start_json)
                print(f"📤 发送开始信号: {start_json}")
                
                # 8. 发送音频数据
                audio_msg = {
                    "audio": b64_data,
                    "is_speaking": True,
                    "wav_format": "pcm"
                }
                audio_json = json.dumps(audio_msg)
                await websocket.send(audio_json)
                print(f"📤 发送音频数据 (长度: {len(audio_json)})")
                
                # 9. 发送结束信号
                end_msg = {"is_speaking": False}
                end_json = json.dumps(end_msg)
                await websocket.send(end_json)
                print(f"📤 发送结束信号: {end_json}")
                
                # 10. 接收响应
                print("\n📥 等待FunASR响应...")
                responses = []
                final_result = None
                
                try:
                    response_count = 0
                    while True:
                        response = await asyncio.wait_for(
                            websocket.recv(),
                            timeout=8
                        )
                        response_count += 1
                        responses.append(response)
                        print(f"📥 响应 {response_count}: {response}")
                        
                        # 解析响应
                        if isinstance(response, str):
                            try:
                                result_data = json.loads(response)
                                text = result_data.get('text', '').strip()
                                is_final = result_data.get('is_final', False)
                                wav_name_resp = result_data.get('wav_name', '')
                                
                                print(f"   解析结果:")
                                print(f"   - text: '{text}'")
                                print(f"   - is_final: {is_final}")
                                print(f"   - wav_name: '{wav_name_resp}'")
                                
                                if text and is_final:
                                    final_result = text
                                    print(f"✅ 找到最终结果: {text}")
                                    break
                                elif text:
                                    print(f"🔄 中间结果: {text}")
                                elif is_final:
                                    print("⚠️ 最终响应但文本为空")
                                    break
                                    
                            except json.JSONDecodeError as e:
                                print(f"❌ JSON解析失败: {e}")
                                print(f"   原始响应: {response}")
                        
                except asyncio.TimeoutError:
                    print(f"⏰ 响应接收超时，共收到 {len(responses)} 个响应")
                
                # 11. 分析结果
                print(f"\n📊 结果分析:")
                print(f"总响应数: {len(responses)}")
                
                if final_result:
                    print(f"✅ 识别成功: '{final_result}'")
                    return final_result
                else:
                    print("❌ 未获得有效识别结果")
                    
                    # 尝试从所有响应中提取文本
                    for i, response in enumerate(responses):
                        if isinstance(response, str):
                            try:
                                result_data = json.loads(response)
                                text = result_data.get('text', '').strip()
                                if text:
                                    print(f"   响应 {i+1} 包含文本: '{text}'")
                                    final_result = text
                            except:
                                continue
                    
                    if final_result:
                        print(f"✅ 从响应中提取到结果: '{final_result}'")
                        return final_result
                    else:
                        print("❌ 所有响应都没有有效文本")
                        return None
                
        except Exception as e:
            print(f"❌ FunASR调试测试失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def record_test_audio(self, duration=3):
        """录制测试音频"""
        print(f"\n🎤 录制测试音频 ({duration}秒)...")
        print("请清晰地说话...")
        
        try:
            # 录制音频
            audio_data = sd.rec(
                int(duration * SAMPLE_RATE),
                samplerate=SAMPLE_RATE,
                channels=1,
                dtype='int16'
            )
            sd.wait()
            
            # 保存为WAV文件
            filename = "debug_test_audio.wav"
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(SAMPLE_RATE)
                wf.writeframes(audio_data.tobytes())
            
            print(f"✅ 音频已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 录制失败: {e}")
            return None
    
    async def run_debug_session(self):
        """运行调试会话"""
        self.print_banner()
        
        if not WEBSOCKETS_AVAILABLE:
            print("❌ websockets库不可用")
            return
        
        # 录制测试音频
        audio_file = self.record_test_audio(duration=3)
        if not audio_file:
            return
        
        # 详细调试测试
        result = await self.test_funasr_with_debug(audio_file)
        
        print("\n" + "=" * 60)
        if result:
            print(f"🎉 调试成功，识别结果: '{result}'")
        else:
            print("❌ 调试完成，但识别失败")
            print("\n💡 可能的解决方案:")
            print("1. 检查音频质量 - 确保声音清晰、音量适中")
            print("2. 检查网络连接 - 确保与FunASR服务器连接稳定")
            print("3. 检查服务器状态 - 确认FunASR服务正常运行")
            print("4. 调整超时时间 - 增加FUNASR_TIMEOUT值")
            print("5. 检查音频格式 - 确保16kHz, 16bit, 单声道")
        print("=" * 60)

async def main():
    """主函数"""
    debugger = FunASRDebugger()
    
    try:
        await debugger.run_debug_session()
    except KeyboardInterrupt:
        print("\n调试被中断")
    except Exception as e:
        print(f"\n调试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
