<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ChangeListManager">
    <list default="true" id="297f5d82-30ed-4eac-b12a-d1b789e4aa2f" name="Default Changelist" comment="" />
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="HTML File" />
        <option value="Python Script" />
      </list>
    </option>
  </component>
  <component name="ProjectId" id="2yFrURYxfWOQjj4VlYXqwnlGSeW" />
  <component name="ProjectLevelVcsManager" settingsEditedManually="true" />
  <component name="ProjectViewState">
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">
    <property name="DefaultHtmlFileTemplate" value="HTML File" />
    <property name="RunOnceActivity.OpenProjectViewOnStart" value="true" />
    <property name="last_opened_file_path" value="$PROJECT_DIR$/../aiWatchdog0703 - 副本" />
    <property name="settings.editor.selected.configurable" value="com.jetbrains.python.configuration.PyActiveSdkModuleConfigurable" />
  </component>
  <component name="RunManager" selected="Python.main">
    <configuration name="main" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="aiWatchdog0606" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/main.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="start_voice_system" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="aiWatchdog0606" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/start_voice_system.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="video_server" type="PythonConfigurationType" factoryName="Python" temporary="true" nameIsGenerated="true">
      <module name="aiWatchdog0606" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <envs>
        <env name="PYTHONUNBUFFERED" value="1" />
      </envs>
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="SCRIPT_NAME" value="$PROJECT_DIR$/video_server.py" />
      <option name="PARAMETERS" value="" />
      <option name="SHOW_COMMAND_LINE" value="false" />
      <option name="EMULATE_TERMINAL" value="false" />
      <option name="MODULE_MODE" value="false" />
      <option name="REDIRECT_INPUT" value="false" />
      <option name="INPUT_FILE" value="" />
      <method v="2" />
    </configuration>
    <configuration name="pytest for test_multi_algorithm.test_analyzer_dynamic_config" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="aiWatchdog0606" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;test_multi_algorithm.test_analyzer_dynamic_config&quot;" />
      <option name="_new_targetType" value="&quot;PYTHON&quot;" />
      <method v="2" />
    </configuration>
    <configuration name="pytest in video_server.py" type="tests" factoryName="py.test" temporary="true" nameIsGenerated="true">
      <module name="aiWatchdog0606" />
      <option name="INTERPRETER_OPTIONS" value="" />
      <option name="PARENT_ENVS" value="true" />
      <option name="SDK_HOME" value="" />
      <option name="WORKING_DIRECTORY" value="$PROJECT_DIR$/src/video" />
      <option name="IS_MODULE_SDK" value="true" />
      <option name="ADD_CONTENT_ROOTS" value="true" />
      <option name="ADD_SOURCE_ROOTS" value="true" />
      <option name="_new_keywords" value="&quot;&quot;" />
      <option name="_new_parameters" value="&quot;&quot;" />
      <option name="_new_additionalArguments" value="&quot;&quot;" />
      <option name="_new_target" value="&quot;$PROJECT_DIR$/src/video/video_server.py&quot;" />
      <option name="_new_targetType" value="&quot;PATH&quot;" />
      <method v="2" />
    </configuration>
    <recent_temporary>
      <list>
        <item itemvalue="Python.main" />
        <item itemvalue="Python tests.pytest in video_server.py" />
        <item itemvalue="Python.video_server" />
        <item itemvalue="Python.start_voice_system" />
        <item itemvalue="Python tests.pytest for test_multi_algorithm.test_analyzer_dynamic_config" />
      </list>
    </recent_temporary>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="297f5d82-30ed-4eac-b12a-d1b789e4aa2f" name="Default Changelist" comment="" />
      <created>1749441178753</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1749441178753</updated>
    </task>
    <servers />
  </component>
  <component name="WindowStateProjectService">
    <state x="1048" y="300" key="#com.intellij.fileTypes.FileTypeChooser" timestamp="1752819663841">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="1048" y="300" key="#com.intellij.fileTypes.FileTypeChooser/0.0.1920.1032@0.0.1920.1032" timestamp="1752819663841" />
    <state x="1123" y="246" key="#com.intellij.refactoring.safeDelete.UnsafeUsagesDialog" timestamp="1752566045448">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="1123" y="246" key="#com.intellij.refactoring.safeDelete.UnsafeUsagesDialog/0.0.1920.1032@0.0.1920.1032" timestamp="1752566045448" />
    <state x="-172" y="198" width="1720" height="821" key="DiffContextDialog" timestamp="1752627551241">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="-172" y="198" width="1720" height="821" key="DiffContextDialog/0.0.1920.1032@0.0.1920.1032" timestamp="1752627551241" />
    <state x="806" y="272" key="FileChooserDialogImpl" timestamp="1752828109510">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="806" y="272" key="FileChooserDialogImpl/0.0.1920.1032@0.0.1920.1032" timestamp="1752828109510" />
    <state width="1899" height="403" key="GridCell.Tab.0.bottom" timestamp="1752828262507">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="403" key="GridCell.Tab.0.bottom/0.0.1920.1032@0.0.1920.1032" timestamp="1752828262507" />
    <state width="1899" height="403" key="GridCell.Tab.0.center" timestamp="1752828262507">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="403" key="GridCell.Tab.0.center/0.0.1920.1032@0.0.1920.1032" timestamp="1752828262507" />
    <state width="1899" height="403" key="GridCell.Tab.0.left" timestamp="1752828262507">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="403" key="GridCell.Tab.0.left/0.0.1920.1032@0.0.1920.1032" timestamp="1752828262507" />
    <state width="1899" height="403" key="GridCell.Tab.0.right" timestamp="1752828262507">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="403" key="GridCell.Tab.0.right/0.0.1920.1032@0.0.1920.1032" timestamp="1752828262507" />
    <state width="1899" height="308" key="GridCell.Tab.1.bottom" timestamp="1751273976652">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="308" key="GridCell.Tab.1.bottom/0.0.1920.1032@0.0.1920.1032" timestamp="1751273976652" />
    <state width="1899" height="308" key="GridCell.Tab.1.center" timestamp="1751273976652">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="308" key="GridCell.Tab.1.center/0.0.1920.1032@0.0.1920.1032" timestamp="1751273976652" />
    <state width="1899" height="308" key="GridCell.Tab.1.left" timestamp="1751273976652">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="308" key="GridCell.Tab.1.left/0.0.1920.1032@0.0.1920.1032" timestamp="1751273976652" />
    <state width="1899" height="308" key="GridCell.Tab.1.right" timestamp="1751273976652">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state width="1899" height="308" key="GridCell.Tab.1.right/0.0.1920.1032@0.0.1920.1032" timestamp="1751273976652" />
    <state x="461" y="160" key="SettingsEditor" timestamp="1752741368830">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="461" y="160" key="SettingsEditor/0.0.1920.1032@0.0.1920.1032" timestamp="1752741368830" />
    <state x="55" y="110" width="1736" height="851" key="com.intellij.history.integration.ui.views.DirectoryHistoryDialog" timestamp="1752822378794">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="55" y="110" width="1736" height="851" key="com.intellij.history.integration.ui.views.DirectoryHistoryDialog/0.0.1920.1032@0.0.1920.1032" timestamp="1752822378794" />
    <state x="-120" y="364" width="1720" height="821" key="com.intellij.history.integration.ui.views.FileHistoryDialog" timestamp="1752724179383">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="-120" y="364" width="1720" height="821" key="com.intellij.history.integration.ui.views.FileHistoryDialog/0.0.1920.1032@0.0.1920.1032" timestamp="1752724179383" />
    <state x="734" y="281" width="598" height="536" key="find.popup" timestamp="1752455269474">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="734" y="281" width="598" height="536" key="find.popup/0.0.1920.1032@0.0.1920.1032" timestamp="1752455269474" />
    <state x="752" y="453" key="vcs.readOnlyHandler.ReadOnlyStatusDialog" timestamp="1752128024721">
      <screen x="0" y="0" width="1920" height="1032" />
    </state>
    <state x="752" y="453" key="vcs.readOnlyHandler.ReadOnlyStatusDialog/0.0.1920.1032@0.0.1920.1032" timestamp="1752128024721" />
  </component>
  <component name="XDebuggerManager">
    <breakpoint-manager>
      <breakpoints>
        <line-breakpoint enabled="true" suspend="THREAD" type="python-line">
          <url>file://$PROJECT_DIR$/aivoice.py</url>
          <line>126</line>
          <option name="timeStamp" value="4" />
        </line-breakpoint>
      </breakpoints>
    </breakpoint-manager>
  </component>
</project>