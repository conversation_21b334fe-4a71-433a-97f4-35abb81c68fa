# AI安全监控系统依赖包

# Web框架
fastapi>=0.68.0
uvicorn>=0.15.0
jinja2>=3.0.0
python-multipart>=0.0.5

# 音频处理
sounddevice>=0.4.0
soundfile>=0.10.0
pyaudio>=0.2.11
wave

# 语音识别和TTS
openai-whisper>=20230314
edge-tts>=6.1.0

# WebSocket支持 (FunASR)
websockets>=10.0

# AI和机器学习
torch>=1.9.0
torchvision>=0.10.0
transformers>=4.20.0
sentence-transformers>=2.2.0
numpy>=1.21.0
opencv-python>=4.5.0

# 数据处理
pandas>=1.3.0
pillow>=8.3.0
requests>=2.25.0

# 异步支持
asyncio
aiofiles>=0.7.0

# 日志和配置
python-dotenv>=0.19.0
pyyaml>=5.4.0

# 数据库 (如果需要)
sqlite3

# 工具库
python-dateutil>=2.8.0
typing-extensions>=3.10.0
