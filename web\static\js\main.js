/**
 * 主要功能初始化脚本
 * 负责页面的基础功能和全局事件处理
 */

// 全局变量
window.appState = {
    isAnalysisRunning: false,
    selectedAlgorithms: [],
    videoSource: null,
    alertCount: 0,
    backendConnected: false,
    demoMode: false
};

// DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('主脚本初始化开始...');

    // 初始化各个模块
    initializeApp();

    // 检查是否为演示模式
    setTimeout(() => {
        if (!window.appState.backendConnected) {
            showNotification('后端服务未连接，当前为演示模式', 'info');
            initDemoMode();
        }

        // 定时刷新对话历史（每30秒）
        setInterval(() => {
            if (typeof loadConversationHistory === 'function') {
                loadConversationHistory();
            }
        }, 30000);

        // 定时刷新语音状态（每10秒）
        setInterval(() => {
            if (typeof refreshVoiceStatus === 'function') {
                refreshVoiceStatus();
            }
        }, 10000);
    }, 2000);

    console.log('主脚本初始化完成');
});

/**
 * 加载自定义模型到算法网格
 */
async function loadCustomModelsToGrid() {
    try {
        const response = await fetch('/api/custom-models');
        const result = await response.json();

        if (result.status === 'success' && result.models) {
            const algorithmGrid = document.getElementById('algorithmGrid');
            if (!algorithmGrid) return;

            // 为每个自定义模型添加到网格
            Object.entries(result.models).forEach(([englishName, modelInfo]) => {
                addCustomModelToMainGrid(englishName, modelInfo.chinese_name);
            });

            console.log(`已加载 ${Object.keys(result.models).length} 个自定义模型到算法网格`);
        }
    } catch (error) {
        console.error('加载自定义模型失败:', error);
        // 不显示错误通知，因为可能是后端未启动
    }
}

/**
 * 添加自定义模型到主页面算法网格
 */
function addCustomModelToMainGrid(englishName, chineseName) {
    const algorithmGrid = document.getElementById('algorithmGrid');
    if (!algorithmGrid) return;

    // 检查是否已存在
    const existingItem = algorithmGrid.querySelector(`input[value="${englishName}"]`);
    if (existingItem) return;

    // 创建新的算法项
    const algorithmItem = document.createElement('label');
    algorithmItem.className = 'algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition';
    algorithmItem.setAttribute('data-category', 'custom');
    algorithmItem.setAttribute('data-custom', 'true');

    algorithmItem.innerHTML = `
      <input type="checkbox" name="algorithms" value="${englishName}" class="mr-3 accent-blue-500">
      <span class="text-sm text-white">${chineseName}</span>
      <span class="ml-2 text-xs text-blue-400">[自定义]</span>
    `;

    // 添加到网格末尾
    algorithmGrid.appendChild(algorithmItem);
}

/**
 * 应用初始化
 */
function initializeApp() {
    // 初始化UI组件
    initializeUI();

    // 初始化事件监听器
    initializeEventListeners();

    // 加载自定义模型到算法网格
    loadCustomModelsToGrid();

    // 加载保存的设置
    loadSavedSettings();

    // 初始化状态检查
    checkSystemStatus();

    // 加载对话历史
    if (typeof loadConversationHistory === 'function') {
        loadConversationHistory();
    }

    // 刷新语音助手状态
    if (typeof refreshVoiceStatus === 'function') {
        refreshVoiceStatus();
    }
}

/**
 * 初始化UI组件
 */
function initializeUI() {
    // 初始化视频源类型切换
    const videoSourceRadios = document.querySelectorAll('input[name="videoSourceType"]');
    videoSourceRadios.forEach(radio => {
        radio.addEventListener('change', handleVideoSourceTypeChange);
    });
    
    // 初始化文件选择
    const fileInput = document.getElementById('videoFileInput');
    const selectFileBtn = document.getElementById('selectFileBtn');
    
    if (selectFileBtn && fileInput) {
        selectFileBtn.addEventListener('click', () => fileInput.click());
        fileInput.addEventListener('change', handleFileSelection);
    }
    
    // 初始化模态框
    initializeModals();
}

/**
 * 初始化事件监听器
 */
function initializeEventListeners() {
    // 视频源应用按钮
    const applyVideoBtn = document.getElementById('applyVideoSource');
    if (applyVideoBtn) {
        applyVideoBtn.addEventListener('click', applyVideoSource);
    }
    
    const applyFileBtn = document.getElementById('applyFileSource');
    if (applyFileBtn) {
        applyFileBtn.addEventListener('click', applyFileSource);
    }
    
    // 分析控制按钮
    const startAnalysisBtn = document.getElementById('startAnalysis');
    if (startAnalysisBtn) {
        startAnalysisBtn.addEventListener('click', toggleAnalysis);
    }
    
    // 高级设置按钮
    const advancedBtn = document.getElementById('advancedSettings');
    if (advancedBtn) {
        advancedBtn.addEventListener('click', openAdvancedSettings);
    }
}

/**
 * 初始化模态框
 */
function initializeModals() {
    // 高级设置模态框
    const modal = document.getElementById('advancedModal');
    const closeBtn = document.getElementById('closeModal');
    const cancelBtn = document.getElementById('cancelSettings');
    const confirmBtn = document.getElementById('confirmSettings');
    
    if (closeBtn) closeBtn.addEventListener('click', closeAdvancedSettings);
    if (cancelBtn) cancelBtn.addEventListener('click', closeAdvancedSettings);
    if (confirmBtn) confirmBtn.addEventListener('click', confirmAdvancedSettings);
    
    // 点击背景关闭模态框
    if (modal) {
        modal.addEventListener('click', function(e) {
            if (e.target === modal) {
                closeAdvancedSettings();
            }
        });
    }
}

/**
 * 处理视频源类型切换
 */
function handleVideoSourceTypeChange(event) {
    const sourceType = event.target.value;
    const rtspSection = document.getElementById('rtspSection');
    const fileSection = document.getElementById('fileUploadSection');
    const applyRtspBtn = document.getElementById('applyVideoSource');
    
    if (sourceType === 'rtsp') {
        rtspSection.classList.remove('hidden');
        fileSection.classList.add('hidden');
        applyRtspBtn.innerHTML = '<i class="fas fa-check mr-1"></i>应用RTSP源';
    } else {
        rtspSection.classList.add('hidden');
        fileSection.classList.remove('hidden');
        applyRtspBtn.innerHTML = '<i class="fas fa-check mr-1"></i>应用视频文件';
    }
}

/**
 * 处理文件选择
 */
function handleFileSelection(event) {
    const file = event.target.files[0];
    const fileNameDisplay = document.getElementById('selectedFileName');
    const applyFileBtn = document.getElementById('applyFileSource');
    
    if (file) {
        fileNameDisplay.textContent = file.name;
        applyFileBtn.classList.remove('hidden');
    } else {
        fileNameDisplay.textContent = '未选择文件';
        applyFileBtn.classList.add('hidden');
    }
}

/**
 * 应用RTSP视频源
 */
async function applyVideoSource() {
    const rtspUrl = document.getElementById('rtspUrl').value.trim();

    if (!rtspUrl) {
        showNotification('请输入RTSP地址', 'error');
        return;
    }

    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/set-video-source`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                video_source: rtspUrl
            })
        });

        if (!response.ok) {
            throw new Error(`服务器响应错误: ${response.status}`);
        }

        const result = await response.json();

        if (result.status === 'success') {
            showNotification('RTSP源配置已保存', 'success');
            updateVideoSourceDisplay(rtspUrl);
        } else {
            throw new Error(result.message || '应用失败');
        }
    } catch (error) {
        console.warn('无法连接到后端服务:', error.message);
        // 在前端保存配置
        localStorage.setItem('videoSource', JSON.stringify({
            type: 'rtsp',
            source: rtspUrl
        }));
        showNotification('RTSP源配置已保存到本地', 'info');
        updateVideoSourceDisplay(rtspUrl);
    }
}

/**
 * 应用文件视频源
 */
async function applyFileSource() {
    const fileInput = document.getElementById('videoFileInput');
    const file = fileInput.files[0];
    
    if (!file) {
        showNotification('请选择视频文件', 'error');
        return;
    }
    
    const formData = new FormData();
    formData.append('video', file);
    
    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/upload-video`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData
        });

        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('视频文件上传成功', 'success');
            updateVideoSourceDisplay(file.name);
        } else {
            throw new Error(result.message || '上传失败');
        }
    } catch (error) {
        console.error('上传视频文件失败:', error);
        showNotification(`上传失败: ${error.message}`, 'error');
    }
}

/**
 * 更新视频源显示
 */
function updateVideoSourceDisplay(source) {
    const sourceDisplay = document.getElementById('videoSourceDisplay');
    const currentSource = document.getElementById('currentVideoSource');
    const videoStatus = document.getElementById('videoStatus');
    
    if (sourceDisplay) {
        sourceDisplay.textContent = '已连接';
    }
    
    if (currentSource) {
        currentSource.textContent = source;
    }
    
    if (videoStatus) {
        videoStatus.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
    }
}

/**
 * 切换分析状态
 */
async function toggleAnalysis() {
    if (window.appState.isAnalysisRunning) {
        await stopAnalysis();
    } else {
        await startAnalysis();
    }
}

/**
 * 开始分析
 */
async function startAnalysis() {
    const algorithms = getSelectedAlgorithms();

    if (algorithms.length === 0) {
        showNotification('请先选择检测算法', 'error');
        openAdvancedSettings();
        return;
    }

    try {
        if (window.appState.backendConnected) {
            // 调用后端启动分析API
            console.log('调用后端启动分析API');

            const host = window.location.hostname;
            const port = '16532'; // 后端服务端口
            const apiUrl = `http://${host}:${port}/api/analysis/start`;

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    algorithms: algorithms
                })
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.status === 'success') {
                // 更新本地状态
                localStorage.setItem('analysisRunning', 'true');
                localStorage.setItem('selectedAlgorithms', JSON.stringify(algorithms));

                window.appState.isAnalysisRunning = true;
                updateAnalysisButton();

                const algorithmNames = algorithms.map(alg => alg.name).join('、');
                showNotification(`分析已开始，算法：${algorithmNames}`, 'success');
                console.log('分析已开始（后端API调用成功）');
            } else {
                throw new Error(result.message || '启动分析失败');
            }
        } else {
            // 演示模式
            window.appState.isAnalysisRunning = true;
            updateAnalysisButton();
            showNotification('演示模式：分析已开始', 'info');
        }
    } catch (error) {
        console.error('启动分析失败:', error);
        showNotification(`启动失败: ${error.message}`, 'error');
    }
}

/**
 * 停止分析
 */
async function stopAnalysis() {
    try {
        if (window.appState.backendConnected) {
            // 调用后端停止分析API
            console.log('调用后端停止分析API');

            const host = window.location.hostname;
            const port = '16532'; // 后端服务端口
            const apiUrl = `http://${host}:${port}/api/analysis/stop`;

            const response = await fetch(apiUrl, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`);
            }

            const result = await response.json();

            if (result.status === 'success') {
                // 更新本地状态
                localStorage.setItem('analysisRunning', 'false');
                window.appState.isAnalysisRunning = false;
                updateAnalysisButton();
                showNotification('分析已停止', 'success');
                console.log('分析已停止（后端API调用成功）');
            } else {
                throw new Error(result.message || '停止分析失败');
            }
        } else {
            // 演示模式
            window.appState.isAnalysisRunning = false;
            updateAnalysisButton();
            showNotification('演示模式：分析已停止', 'info');
        }
    } catch (error) {
        console.error('停止分析失败:', error);
        showNotification(`停止失败: ${error.message}`, 'error');
    }
}

/**
 * 更新分析按钮状态
 */
function updateAnalysisButton() {
    const btn = document.getElementById('startAnalysis');
    if (!btn) return;
    
    if (window.appState.isAnalysisRunning) {
        btn.innerHTML = '<i class="fas fa-stop mr-2"></i>停止分析';
        btn.className = 'px-6 py-3 bg-red-600 hover:bg-red-700 rounded-md font-medium transition';
    } else {
        btn.innerHTML = '<i class="fas fa-play mr-2"></i>开始分析';
        btn.className = 'px-6 py-3 bg-green-600 hover:bg-green-700 rounded-md font-medium transition';
    }
}

/**
 * 打开高级设置
 */
function openAdvancedSettings() {
    const modal = document.getElementById('advancedModal');
    if (modal) {
        modal.classList.remove('hidden');
    }
}

/**
 * 关闭高级设置
 */
function closeAdvancedSettings() {
    const modal = document.getElementById('advancedModal');
    if (modal) {
        modal.classList.add('hidden');
    }
}

/**
 * 确认高级设置
 */
function confirmAdvancedSettings() {
    const selectedAlgorithms = [];
    const checkboxes = document.querySelectorAll('input[name="algorithms"]:checked');
    
    checkboxes.forEach(checkbox => {
        selectedAlgorithms.push({
            value: checkbox.value,
            name: checkbox.parentElement.querySelector('span').textContent
        });
    });
    
    if (selectedAlgorithms.length === 0) {
        showNotification('请至少选择一个算法', 'error');
        return;
    }
    
    // 保存选择的算法
    window.appState.selectedAlgorithms = selectedAlgorithms;
    localStorage.setItem('selectedAlgorithms', JSON.stringify(selectedAlgorithms));
    
    // 更新显示
    updateAlgorithmDisplay(selectedAlgorithms);
    
    // 关闭模态框
    closeAdvancedSettings();
    
    showNotification(`已选择 ${selectedAlgorithms.length} 个算法`, 'success');
}

/**
 * 更新算法显示
 */
function updateAlgorithmDisplay(algorithms) {
    const container = document.getElementById('selectedAlgorithmTags');
    if (!container) return;
    
    container.innerHTML = '';
    
    if (algorithms.length === 0) {
        container.innerHTML = `
            <div class="text-sm text-gray-400 flex items-center">
                <i class="fas fa-info-circle mr-2"></i>
                请点击"高级设置"选择检测算法
            </div>
        `;
    } else {
        algorithms.forEach(alg => {
            const tag = document.createElement('div');
            tag.className = 'bg-blue-600 text-white px-3 py-1 rounded-full text-sm flex items-center';
            tag.innerHTML = `
                <span>${alg.name}</span>
                <button class="ml-2 text-white hover:text-red-300" onclick="removeAlgorithm('${alg.value}')">
                    <i class="fas fa-times text-xs"></i>
                </button>
            `;
            container.appendChild(tag);
        });
    }
}

/**
 * 移除算法
 */
function removeAlgorithm(algorithmValue) {
    window.appState.selectedAlgorithms = window.appState.selectedAlgorithms.filter(
        alg => alg.value !== algorithmValue
    );
    
    localStorage.setItem('selectedAlgorithms', JSON.stringify(window.appState.selectedAlgorithms));
    updateAlgorithmDisplay(window.appState.selectedAlgorithms);
    
    // 更新复选框状态
    const checkbox = document.querySelector(`input[value="${algorithmValue}"]`);
    if (checkbox) {
        checkbox.checked = false;
    }
    
    showNotification('已移除算法', 'success');
}

/**
 * 获取选择的算法
 */
function getSelectedAlgorithms() {
    return window.appState.selectedAlgorithms || [];
}

/**
 * 加载保存的设置
 */
function loadSavedSettings() {
    const saved = localStorage.getItem('selectedAlgorithms');
    if (saved) {
        try {
            const algorithms = JSON.parse(saved);
            window.appState.selectedAlgorithms = algorithms;
            updateAlgorithmDisplay(algorithms);
            
            // 更新复选框状态
            algorithms.forEach(alg => {
                const checkbox = document.querySelector(`input[value="${alg.value}"]`);
                if (checkbox) {
                    checkbox.checked = true;
                }
            });
        } catch (error) {
            console.error('加载保存的设置失败:', error);
        }
    }
}

/**
 * 检查系统状态
 */
async function checkSystemStatus() {
    try {
        // 使用现有的API接口来检测后端连接状态
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口

        // 尝试调用智能问答接口来验证后端可用性
        const testApiUrl = `http://${host}:${port}/api/ask`;
        const testResponse = await fetch(testApiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ question: 'test' })
        });

        if (testResponse.ok) {
            console.log('后端服务连接正常');
            window.appState.backendConnected = true;

            // 由于后端没有分析状态接口，使用本地状态管理
            const savedAnalysisState = localStorage.getItem('analysisRunning');
            window.appState.isAnalysisRunning = savedAnalysisState === 'true';
            updateAnalysisButton();

            console.log('系统状态检查完成');
        } else {
            throw new Error(`HTTP ${testResponse.status}`);
        }
    } catch (error) {
        console.warn('无法连接到后端服务，启用演示模式');
        // 设置默认状态
        window.appState.isAnalysisRunning = false;
        window.appState.backendConnected = false;
        updateAnalysisButton();
    }
}

/**
 * 初始化演示模式
 */
function initDemoMode() {
    console.log('初始化演示模式...');
    window.appState.demoMode = true;

    // 模拟一些演示数据
    const demoAlgorithms = [
        { value: 'helmet', name: '安全帽检测' },
        { value: 'smoking', name: '吸烟检测' }
    ];

    // 如果没有选择算法，设置默认演示算法
    if (window.appState.selectedAlgorithms.length === 0) {
        window.appState.selectedAlgorithms = demoAlgorithms;
        updateAlgorithmDisplay(demoAlgorithms);

        // 更新复选框状态
        demoAlgorithms.forEach(alg => {
            const checkbox = document.querySelector(`input[value="${alg.value}"]`);
            if (checkbox) {
                checkbox.checked = true;
            }
        });
    }

    // 设置演示视频源
    updateVideoSourceDisplay('演示模式');

    // 不自动生成警报，只有在用户启动分析时才生成
    console.log('演示模式已初始化，警报将在启动分析后生成');
}

/**
 * 模拟演示警报
 */
function simulateDemoAlert() {
    // 只有在演示模式且分析正在运行时才生成警报
    if (!window.appState.demoMode || !window.appState.isAnalysisRunning) return;

    const alertTypes = [
        {
            alert: '检测到未佩戴安全帽',
            alert_type: '安全帽检测',
            alert_level: 'danger'
        },
        {
            alert: '检测到吸烟行为',
            alert_type: '吸烟检测',
            alert_level: 'warning'
        }
    ];

    const randomAlert = alertTypes[Math.floor(Math.random() * alertTypes.length)];
    const demoAlert = {
        ...randomAlert,
        location: '演示区域',
        timestamp: new Date().toISOString(),
        description: '这是演示模式生成的模拟警报'
    };

    // 触发警报事件
    const alertEvent = new CustomEvent('newAlert', {
        detail: demoAlert
    });
    document.dispatchEvent(alertEvent);

    // 定期生成演示警报（只有在分析运行时）
    setTimeout(() => {
        if (window.appState.demoMode && window.appState.isAnalysisRunning) {
            simulateDemoAlert();
        }
    }, 20000 + Math.random() * 15000); // 20-35秒随机间隔
}

// 导出全局函数
window.removeAlgorithm = removeAlgorithm;
