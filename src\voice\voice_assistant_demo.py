#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音助手功能演示脚本
展示完善后的voice_assistant模块的各项功能
"""

import asyncio
import time
from datetime import datetime
import logging

# 导入语音助手模块
from voice_assistant import (
    VoiceConfig,
    WakeWordDetector,
    SpeechRecognizer,
    TextToSpeech,
    DialogueManager,
    VoiceAssistantMonitor,
    VoiceFileManager,
    get_file_manager,
    get_performance_monitor
)

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceAssistantDemo:
    """语音助手功能演示"""
    
    def __init__(self):
        print("\n" + "=" * 80)
        print("🎙️  语音助手功能演示 - 展示完善后的功能")
        print("=" * 80)
    
    async def demo_configuration(self):
        """演示配置功能"""
        print("\n📋 1. 配置管理演示")
        print("-" * 40)
        
        print(f"🔧 音频配置:")
        print(f"   采样率: {VoiceConfig.SAMPLE_RATE} Hz")
        print(f"   声道数: {VoiceConfig.CHANNELS}")
        print(f"   缓冲区大小: {VoiceConfig.CHUNK_SIZE}")
        
        print(f"\n🎯 唤醒词配置:")
        print(f"   唤醒词列表: {VoiceConfig.WAKE_WORDS}")
        print(f"   检测阈值: {VoiceConfig.WAKE_THRESHOLD}")
        print(f"   确认回复: {VoiceConfig.WAKE_CONFIRMATION_SOUNDS}")
        
        print(f"\n🌐 FunASR配置:")
        print(f"   启用状态: {'✅ 已启用' if VoiceConfig.FUNASR_ENABLED else '❌ 已禁用'}")
        print(f"   服务器地址: {VoiceConfig.FUNASR_WS_URL}")
        print(f"   连接超时: {VoiceConfig.FUNASR_TIMEOUT} 秒")
        print(f"   重试次数: {VoiceConfig.FUNASR_RETRY_COUNT}")
        
        print(f"\n💬 对话管理配置:")
        print(f"   对话超时: {VoiceConfig.CONVERSATION_TIMEOUT} 秒")
        print(f"   最大对话轮数: {VoiceConfig.MAX_CONVERSATION_TURNS}")
        print(f"   上下文记忆: {VoiceConfig.CONTEXT_MEMORY_SIZE} 轮")
        
        print(f"\n📁 文件管理配置:")
        print(f"   语音目录: {VoiceConfig.VOICE_DIR}")
        print(f"   临时目录: {VoiceConfig.TEMP_AUDIO_DIR}")
        print(f"   TTS缓存目录: {VoiceConfig.TTS_CACHE_DIR}")
        print(f"   最大临时文件: {VoiceConfig.MAX_TEMP_FILES}")
    
    async def demo_file_manager(self):
        """演示文件管理功能"""
        print("\n📁 2. 文件管理演示")
        print("-" * 40)
        
        # 获取文件管理器实例
        file_mgr = get_file_manager()

        # 获取临时文件路径
        temp_file = file_mgr.get_temp_audio_file("demo")
        print(f"📄 临时音频文件路径: {temp_file}")

        # 获取TTS缓存文件路径
        cache_file = file_mgr.get_tts_cache_file("你好，这是测试", "zh-CN-XiaoxiaoNeural")
        print(f"💾 TTS缓存文件路径: {cache_file}")

        # 模拟保存对话日志
        conversation_data = {
            "session_id": "demo-session-001",
            "messages": [
                {"role": "user", "content": "你好", "timestamp": datetime.now().isoformat()},
                {"role": "assistant", "content": "您好，有什么可以帮您的吗？", "timestamp": datetime.now().isoformat()}
            ]
        }
        file_mgr.save_conversation_log(conversation_data)
        print("💬 对话日志已保存")

        # 清理演示
        file_mgr.cleanup_temp_files()
        print("🗑️ 临时文件清理完成")
    
    async def demo_wake_detector(self):
        """演示唤醒词检测功能"""
        print("\n🎯 3. 唤醒词检测演示")
        print("-" * 40)
        
        detector = WakeWordDetector(VoiceConfig.WAKE_WORDS)
        
        # 模拟检测记录
        detector.record_detection("小凯小凯", 0.95)
        detector.record_detection("你好小凯", 0.88)
        detector.record_detection("小凯同学", 0.92)
        
        # 获取随机确认回复
        confirmation = detector.get_random_confirmation()
        print(f"🔊 随机确认回复: {confirmation}")
        
        # 获取检测统计
        stats = detector.get_detection_stats()
        print(f"📊 检测统计:")
        print(f"   总检测次数: {stats['total_detections']}")
        print(f"   最近检测次数: {stats['recent_detections']}")
        print(f"   检测频率: {stats['detection_rate']:.2f} 次/分钟")
        print(f"   最常用唤醒词: {stats['most_common_wake_word']}")
    
    async def demo_speech_recognizer(self):
        """演示语音识别功能"""
        print("\n🎤 4. 语音识别演示")
        print("-" * 40)
        
        recognizer = SpeechRecognizer()
        
        print(f"🔧 识别器状态:")
        print(f"   FunASR可用: {'✅' if recognizer.use_funasr else '❌'}")
        print(f"   Whisper可用: {'✅' if recognizer.whisper_model else '❌'}")
        
        # 模拟识别过程
        print("\n🔄 模拟语音识别过程...")
        start_time = time.time()
        
        # 演示模式：不使用模拟识别
        print("   演示模式：跳过语音识别测试")
        mock_result = "演示模式 - 语音识别已禁用"
        
        recognition_time = time.time() - start_time
        print(f"✅ 识别结果: '{mock_result}'")
        print(f"⏱️ 识别耗时: {recognition_time:.3f} 秒")
        
        # 记录到性能监控
        monitor = get_performance_monitor()
        monitor.record_recognition_attempt("mock", True, recognition_time)
    
    async def demo_tts(self):
        """演示语音合成功能"""
        print("\n🔊 5. 语音合成演示")
        print("-" * 40)
        
        tts = TextToSpeech()
        
        print(f"🔧 TTS状态:")
        print(f"   Pygame可用: {'✅' if tts.use_pygame else '❌'}")
        print(f"   缓存启用: {'✅' if tts.cache_enabled else '❌'}")
        print(f"   播放队列: {tts.play_queue.qsize()} 个任务")
        
        # 模拟TTS过程
        test_text = "这是语音合成功能演示，系统工作正常"
        print(f"\n🎵 合成文本: '{test_text}'")
        
        start_time = time.time()
        await tts.speak(test_text)
        tts_time = time.time() - start_time
        
        print(f"⏱️ 合成耗时: {tts_time:.3f} 秒")
        
        # 记录到性能监控
        monitor = get_performance_monitor()
        monitor.record_tts_attempt(True, tts_time)
    
    async def demo_dialogue_manager(self):
        """演示对话管理功能"""
        print("\n💬 6. 对话管理演示")
        print("-" * 40)
        
        dialogue = DialogueManager()
        
        print(f"🆔 会话ID: {dialogue.session_id}")
        print(f"⏰ 会话开始时间: {dialogue.session_start_time.strftime('%H:%M:%S')}")
        
        # 模拟对话
        dialogue.add_message("user", "你好，我想查询今天的警报", {"confidence": 0.95})
        dialogue.add_message("assistant", "您好！今天共有3个警报事件，都已处理完毕", {"response_time": 1.2})
        dialogue.add_message("user", "能详细说明一下吗？", {"confidence": 0.88})
        dialogue.add_message("assistant", "当然可以。第一个是入侵检测警报...", {"response_time": 0.8})
        
        # 获取对话上下文
        context = dialogue.get_context()
        print(f"\n📝 对话上下文:")
        for line in context.split('\n'):
            print(f"   {line}")
        
        # 获取对话摘要
        summary = dialogue.get_conversation_summary()
        print(f"\n📊 对话摘要:")
        print(f"   总轮数: {summary['total_turns']}")
        print(f"   用户消息: {summary['user_messages']}")
        print(f"   助手消息: {summary['assistant_messages']}")
        print(f"   用户情绪: {summary['user_mood']}")
        print(f"   对话主题: {summary['conversation_topic']}")
        
        # 获取对话统计
        stats = dialogue.get_conversation_stats()
        print(f"\n📈 对话统计:")
        print(f"   会话持续时间: {stats['session_duration']:.1f} 秒")
        print(f"   平均响应时间: {stats['avg_response_time']:.2f} 秒")
    
    async def demo_performance_monitor(self):
        """演示性能监控功能"""
        print("\n📊 7. 性能监控演示")
        print("-" * 40)
        
        monitor = get_performance_monitor()
        
        # 模拟一些性能数据
        monitor.record_recognition_attempt("funasr", True, 1.2)
        monitor.record_recognition_attempt("funasr", True, 0.8)
        monitor.record_recognition_attempt("whisper", True, 2.1)
        monitor.record_tts_attempt(True, 0.5)
        monitor.record_wake_detection()
        monitor.record_error("demo_error", "这是一个演示错误")
        
        # 获取性能报告
        report = monitor.get_performance_report()
        print(f"📈 性能报告:")
        print(f"   运行时间: {report['uptime_formatted']}")
        print(f"   总请求数: {report['total_requests']}")
        print(f"   识别成功率: {report['recognition_success_rate']}%")
        print(f"   FunASR成功率: {report['funasr_success_rate']}%")
        print(f"   Whisper成功率: {report['whisper_success_rate']}%")
        print(f"   TTS成功率: {report['tts_success_rate']}%")
        print(f"   平均识别时间: {report['avg_recognition_time']:.3f}s")
        print(f"   平均TTS时间: {report['avg_tts_time']:.3f}s")
        print(f"   唤醒词检测: {report['wake_word_detections']} 次")
        print(f"   系统错误: {report['system_errors']} 次")
        print(f"   请求频率: {report['requests_per_minute']:.1f} 次/分钟")
        
        # 获取最近错误
        recent_errors = monitor.get_recent_errors(5)
        if recent_errors:
            print(f"\n🚨 最近错误:")
            for error in recent_errors:
                print(f"   [{error['timestamp'][:19]}] {error['type']}: {error['message']}")
    
    async def demo_integration(self):
        """演示系统集成功能"""
        print("\n🔗 8. 系统集成演示")
        print("-" * 40)
        
        print("🎯 完整语音交互流程演示:")
        print("   1. 唤醒词检测 → 2. 语音识别 → 3. 对话管理 → 4. 语音合成")
        
        # 模拟完整流程
        detector = WakeWordDetector(VoiceConfig.WAKE_WORDS)
        recognizer = SpeechRecognizer()
        dialogue = DialogueManager()
        tts = TextToSpeech()
        
        # 1. 唤醒词检测
        print("\n🎯 步骤1: 唤醒词检测")
        detector.record_detection("小凯小凯", 0.92)
        confirmation = detector.get_random_confirmation()
        print(f"   检测到唤醒词，回复: {confirmation}")
        
        # 2. 语音识别
        print("\n🎤 步骤2: 语音识别")
        print("   演示模式：跳过语音识别")
        user_input = "演示模式 - 语音识别已禁用"
        print(f"   识别结果: {user_input}")
        
        # 3. 对话管理
        print("\n💬 步骤3: 对话管理")
        dialogue.add_message("user", user_input)
        context = dialogue.get_context()
        print(f"   对话上下文已更新")
        
        # 4. 生成回复
        response = f"我理解您的请求：{user_input}。系统正在处理中..."
        dialogue.add_message("assistant", response)
        
        # 5. 语音合成
        print("\n🔊 步骤4: 语音合成")
        await tts.speak(response)
        print(f"   语音回复: {response}")
        
        print("\n✅ 完整交互流程演示完成！")
    
    async def run_demo(self):
        """运行完整演示"""
        await self.demo_configuration()
        await self.demo_file_manager()
        await self.demo_wake_detector()
        await self.demo_speech_recognizer()
        await self.demo_tts()
        await self.demo_dialogue_manager()
        await self.demo_performance_monitor()
        await self.demo_integration()
        
        print("\n" + "=" * 80)
        print("🎉 语音助手功能演示完成！")
        print("=" * 80)
        print("💡 主要改进:")
        print("   • 完善的配置管理系统")
        print("   • 智能文件管理和缓存")
        print("   • 增强的唤醒词检测")
        print("   • 高效的语音识别集成")
        print("   • 智能对话管理和上下文记忆")
        print("   • 实时性能监控和错误追踪")
        print("   • 模块化设计和易于扩展")
        print("=" * 80)

async def main():
    """主函数"""
    demo = VoiceAssistantDemo()
    await demo.run_demo()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
