#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR连接测试脚本
功能：测试与FunASR WebSocket服务器的连接和语音识别功能
"""

import asyncio
import json
import logging
import wave
import numpy as np
import sounddevice as sd

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装，请运行: pip install websockets")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FunASR配置
FUNASR_WS_URL = "ws://************:10096/"
FUNASR_TIMEOUT = 10
SAMPLE_RATE = 16000

class FunASRTester:
    """FunASR测试器"""
    
    def __init__(self):
        self.ws_url = FUNASR_WS_URL
        self.timeout = FUNASR_TIMEOUT
    
    def print_banner(self):
        """打印测试横幅"""
        print("\n" + "=" * 60)
        print("🎙️ FunASR语音识别测试")
        print("=" * 60)
        print(f"服务器地址: {self.ws_url}")
        print(f"连接超时: {self.timeout}秒")
        print("=" * 60)
    
    async def test_connection(self):
        """测试WebSocket连接"""
        print("\n🔗 测试WebSocket连接...")
        
        try:
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:
                print("✅ WebSocket连接成功")
                
                # 发送测试消息
                test_message = {"type": "test", "message": "hello"}
                await websocket.send(json.dumps(test_message))
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(
                        websocket.recv(),
                        timeout=5
                    )
                    print(f"✅ 收到响应: {response}")
                    return True
                except asyncio.TimeoutError:
                    print("⚠️ 未收到响应，但连接正常")
                    return True
                    
        except Exception as e:
            print(f"❌ 连接失败: {e}")
            return False
    
    def record_audio(self, duration=3):
        """录制音频"""
        print(f"\n🎤 开始录制音频 ({duration}秒)...")
        print("请对着麦克风说话...")
        
        try:
            # 录制音频
            audio_data = sd.rec(
                int(duration * SAMPLE_RATE),
                samplerate=SAMPLE_RATE,
                channels=1,
                dtype='int16'
            )
            sd.wait()  # 等待录制完成
            
            print("✅ 录制完成")
            return audio_data
            
        except Exception as e:
            print(f"❌ 录制失败: {e}")
            return None
    
    def save_audio_to_wav(self, audio_data, filename="test_audio.wav"):
        """保存音频为WAV文件"""
        try:
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)  # 单声道
                wf.setsampwidth(2)  # 16位
                wf.setframerate(SAMPLE_RATE)
                wf.writeframes(audio_data.tobytes())
            
            print(f"✅ 音频已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 保存音频失败: {e}")
            return None
    
    async def test_recognition_with_audio_data(self, audio_data):
        """使用音频数据测试识别"""
        print("\n🧠 测试语音识别...")

        try:
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:

                # 发送音频数据 (尝试不同格式)
                print("📤 发送音频数据...")

                # 方式1: 直接发送字节数据
                await websocket.send(audio_data.tobytes())

                # 等待多个响应
                responses = []
                try:
                    while True:
                        response = await asyncio.wait_for(
                            websocket.recv(),
                            timeout=3  # 较短的超时时间
                        )
                        responses.append(response)
                        print(f"📥 收到响应 {len(responses)}: {response}")

                        # 尝试解析每个响应
                        if isinstance(response, str):
                            try:
                                result_data = json.loads(response)
                                text = result_data.get('text', '').strip()
                                if text:
                                    print(f"✅ 识别结果: {text}")
                                    return text
                            except json.JSONDecodeError:
                                # 如果不是JSON格式，直接作为文本处理
                                text = response.strip()
                                if text and not text.startswith('{'):
                                    print(f"✅ 识别结果: {text}")
                                    return text

                except asyncio.TimeoutError:
                    print(f"⏰ 接收完成，共收到 {len(responses)} 个响应")

                # 如果有响应但没有识别结果
                if responses:
                    print("⚠️ 收到响应但未找到识别结果")
                    for i, resp in enumerate(responses):
                        print(f"响应 {i+1}: {resp}")
                    return None
                else:
                    print("⚠️ 未收到任何响应")
                    return None

        except Exception as e:
            print(f"❌ 识别失败: {e}")
            return None
    
    async def test_recognition_with_file(self, audio_file):
        """使用音频文件测试识别"""
        print(f"\n📁 使用文件测试识别: {audio_file}")
        
        try:
            # 读取音频文件
            with open(audio_file, 'rb') as f:
                audio_data = f.read()
            
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:
                
                # 发送音频数据
                await websocket.send(audio_data)
                
                # 接收识别结果
                response = await asyncio.wait_for(
                    websocket.recv(),
                    timeout=self.timeout
                )
                
                # 解析响应
                if isinstance(response, str):
                    try:
                        result_data = json.loads(response)
                        text = result_data.get('text', '').strip()
                        if text:
                            print(f"✅ 识别结果: {text}")
                            return text
                        else:
                            print("⚠️ 识别结果为空")
                            print(f"原始响应: {response}")
                            return None
                    except json.JSONDecodeError:
                        # 如果不是JSON格式，直接作为文本处理
                        text = response.strip()
                        if text:
                            print(f"✅ 识别结果: {text}")
                            return text
                        else:
                            print("⚠️ 识别结果为空")
                            return None
                else:
                    print(f"⚠️ 收到非文本响应: {type(response)}")
                    return None
                    
        except asyncio.TimeoutError:
            print("❌ 识别超时")
            return None
        except Exception as e:
            print(f"❌ 识别失败: {e}")
            return None
    
    async def run_full_test(self):
        """运行完整测试"""
        self.print_banner()
        
        if not WEBSOCKETS_AVAILABLE:
            print("❌ websockets库不可用，无法进行测试")
            return False
        
        # 1. 测试连接
        if not await self.test_connection():
            print("\n❌ 连接测试失败，无法继续")
            return False
        
        # 2. 录制音频并测试识别
        audio_data = self.record_audio(duration=3)
        if audio_data is not None:
            # 保存音频文件
            audio_file = self.save_audio_to_wav(audio_data)
            
            # 测试识别
            result1 = await self.test_recognition_with_audio_data(audio_data)
            
            if audio_file:
                result2 = await self.test_recognition_with_file(audio_file)
        
        print("\n" + "=" * 60)
        print("🎉 测试完成")
        print("=" * 60)
        
        return True

async def main():
    """主函数"""
    tester = FunASRTester()
    
    try:
        await tester.run_full_test()
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        # Python 3.6兼容性
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
