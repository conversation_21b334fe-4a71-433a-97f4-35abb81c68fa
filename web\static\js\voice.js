// 语音播报队列管理
const voiceQueue = {
  queue: [],
  isSpeaking: false,
  voiceEnabled: true,

  addToQueue: function(text) {
    if (!this.voiceEnabled || !text) return;

    // 净化文本：移除HTML标签和多余空格
    const cleanText = text.replace(/<[^>]*>/g, '')
                         .replace(/\n+/g, ' ')
                         .replace(/\s+/g, ' ')
                         .trim();

    if (!cleanText || cleanText === 'undefined') return;

    this.queue.push(cleanText);
    if (!this.isSpeaking) {
      this.processQueue();
    }
  },

  processQueue: function() {
    if (this.queue.length === 0) {
      this.isSpeaking = false;
      return;
    }

    // 检查语音合成API是否可用
    if(!window.speechSynthesis) {
      console.warn('语音合成API不可用');
      this.queue = [];
      this.isSpeaking = false;
      return;
    }

    this.isSpeaking = true;
    const text = this.queue.shift();

    try {
      this.speak(text);
    } catch(error) {
      console.error('语音播报异常:', error);
      this.processQueue();
    }
  },

  speak: function(text) {
    window.speechSynthesis.cancel();

    const utterance = new SpeechSynthesisUtterance(text);
    Object.assign(utterance, {
      rate: 1.2,
      pitch: 0.9,
      volume: 1,
      lang: 'zh-CN'
    });

    utterance.onend = () => {
      setTimeout(() => {
        this.processQueue();
      }, 500);
    };

    utterance.onerror = (event) => {
      console.error('语音播报错误:', event.error);
      this.processQueue();
    };

    window.speechSynthesis.speak(utterance);
  }
};
