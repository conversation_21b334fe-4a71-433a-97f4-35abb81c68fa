# 语音助手配置文件

# FunASR语音识别配置
funasr:
  # WebSocket服务地址
  websocket_url: "ws://192.168.3.95:10096/"
  
  # 音频配置
  audio:
    sample_rate: 16000
    channels: 1
    chunk_size: 1024
    format: "wav"
  
  # 识别参数
  recognition:
    language: "zh-cn"
    enable_vad: true
    vad_threshold: 0.5
    max_silence_duration: 2.0

# 唤醒词配置
wake_words:
  - "小凯小凯"
  - "你好小凯"
  - "小凯同学"

# TTS配置
tts:
  engine: "edge-tts"  # 可选: edge-tts, azure, local
  voice: "zh-CN-XiaoxiaoNeural"
  rate: "+0%"
  volume: "+0%"
  
# 对话配置
conversation:
  # 会话超时时间（秒）
  session_timeout: 300
  
  # 最大对话轮数
  max_turns: 10
  
  # 睡眠模式配置
  sleep_mode:
    enable: true
    idle_timeout: 600  # 10分钟无活动后进入睡眠
    
  # 对话历史保存
  history:
    enable: true
    save_path: "data/voice/conversations"
    max_history_files: 100

# 音频文件管理
audio_files:
  # 临时文件存储路径
  temp_path: "data/voice/temp"
  
  # 缓存路径
  cache_path: "data/voice/tts_cache"
  
  # 自动清理配置
  auto_cleanup:
    enable: true
    temp_file_lifetime: 3600  # 临时文件保留时间（秒）
    cache_max_size: 1000      # 缓存最大文件数

# 硬件配置
hardware:
  # 麦克风设备
  microphone:
    device_index: null  # null表示使用默认设备
    
  # 扬声器设备
  speaker:
    device_index: null  # null表示使用默认设备
    volume: 0.8
    
  # 嵌入式板配置
  embedded_board:
    enable: false
    camera_module: true
    voice_module: true
    speaker: true
