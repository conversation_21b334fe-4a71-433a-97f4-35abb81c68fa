#!/usr/bin/env python3
"""
测试语音文件路径是否正确
"""

import os
import sys
import time
import wave
import numpy as np

# 添加src目录到路径
sys.path.append('src')

from voice.voice_assistant import VoiceConfig, AudioRecorder

def test_voice_paths():
    """测试语音文件路径配置"""
    print("🔧 测试语音文件路径配置")
    print(f"VOICE_DIR: {VoiceConfig.VOICE_DIR}")
    print(f"TEMP_AUDIO_DIR: {VoiceConfig.TEMP_AUDIO_DIR}")
    print(f"CONVERSATION_LOG_DIR: {VoiceConfig.CONVERSATION_LOG_DIR}")
    
    # 检查目录是否存在
    for dir_name, dir_path in [
        ("语音目录", VoiceConfig.VOICE_DIR),
        ("临时音频目录", VoiceConfig.TEMP_AUDIO_DIR),
        ("对话日志目录", VoiceConfig.CONVERSATION_LOG_DIR)
    ]:
        if os.path.exists(dir_path):
            print(f"✅ {dir_name} 存在: {dir_path}")
        else:
            print(f"❌ {dir_name} 不存在: {dir_path}")
            # 创建目录
            os.makedirs(dir_path, exist_ok=True)
            print(f"✅ 已创建 {dir_name}: {dir_path}")

def test_audio_recording():
    """测试音频录制路径"""
    print("\n🎤 测试音频录制路径")
    
    try:
        # 创建录音器实例
        recorder = AudioRecorder()
        
        # 模拟录制一个音频文件
        print("开始模拟录制...")
        audio_file = recorder._record_command_mock(2)  # 录制2秒
        
        if audio_file:
            print(f"✅ 音频文件已生成: {audio_file}")
            
            # 检查文件是否在正确的目录中
            if audio_file.startswith(VoiceConfig.TEMP_AUDIO_DIR):
                print("✅ 文件路径正确，在临时音频目录中")
            else:
                print(f"❌ 文件路径错误，应该在 {VoiceConfig.TEMP_AUDIO_DIR} 中")
            
            # 检查文件是否存在
            if os.path.exists(audio_file):
                print("✅ 文件确实存在")
                file_size = os.path.getsize(audio_file)
                print(f"📁 文件大小: {file_size} 字节")
                
                # 清理测试文件
                os.remove(audio_file)
                print("🗑️ 测试文件已清理")
            else:
                print("❌ 文件不存在")
        else:
            print("❌ 录制失败，没有生成文件")
            
    except Exception as e:
        print(f"❌ 测试录制失败: {e}")

def test_tts_path():
    """测试TTS文件路径"""
    print("\n🔊 测试TTS文件路径")
    
    # 模拟TTS文件生成
    timestamp = int(time.time())
    filename = f"tts_{timestamp}.mp3"
    tts_file = os.path.join(VoiceConfig.VOICE_DIR, filename)
    
    print(f"TTS文件路径: {tts_file}")
    
    # 检查目录是否存在
    tts_dir = os.path.dirname(tts_file)
    if os.path.exists(tts_dir):
        print(f"✅ TTS目录存在: {tts_dir}")
    else:
        print(f"❌ TTS目录不存在: {tts_dir}")
        os.makedirs(tts_dir, exist_ok=True)
        print(f"✅ 已创建TTS目录: {tts_dir}")
    
    # 创建一个测试文件
    try:
        with open(tts_file, 'w') as f:
            f.write("test tts file")
        print(f"✅ 测试TTS文件创建成功: {tts_file}")
        
        # 清理测试文件
        os.remove(tts_file)
        print("🗑️ 测试TTS文件已清理")
    except Exception as e:
        print(f"❌ 创建测试TTS文件失败: {e}")

def list_voice_files():
    """列出语音相关文件"""
    print("\n📂 当前语音文件分布:")
    
    # 检查项目根目录的音频文件
    root_audio_files = []
    for file in os.listdir('.'):
        if file.endswith(('.wav', '.mp3')) and ('temp_command' in file or 'tts_' in file):
            root_audio_files.append(file)
    
    if root_audio_files:
        print("❌ 项目根目录中的音频文件:")
        for file in root_audio_files:
            print(f"   - {file}")
    else:
        print("✅ 项目根目录中没有音频文件")
    
    # 检查data/voice目录的文件
    voice_dir = VoiceConfig.VOICE_DIR
    if os.path.exists(voice_dir):
        voice_files = []
        for root, dirs, files in os.walk(voice_dir):
            for file in files:
                if file.endswith(('.wav', '.mp3')):
                    voice_files.append(os.path.join(root, file))
        
        if voice_files:
            print(f"✅ {voice_dir} 目录中的音频文件:")
            for file in voice_files:
                print(f"   - {file}")
        else:
            print(f"📁 {voice_dir} 目录为空")
    else:
        print(f"❌ {voice_dir} 目录不存在")

def main():
    """主函数"""
    print("🎵 语音文件路径测试工具")
    print("=" * 50)
    
    test_voice_paths()
    test_audio_recording()
    test_tts_path()
    list_voice_files()
    
    print("\n" + "=" * 50)
    print("✅ 测试完成")

if __name__ == "__main__":
    main()
