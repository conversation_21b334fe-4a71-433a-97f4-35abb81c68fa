/**
 * 系统配置页面JavaScript
 * 处理配置的加载、保存和重置功能
 */

// 配置API基础URL
const CONFIG_API_BASE = (() => {
    const host = window.location.hostname;
    const port = '16532'; // 后端服务端口
    return `http://${host}:${port}`;
})();

// 默认配置值
const DEFAULT_CONFIG = {
    video: {
        analysis_interval: 10,
        buffer_duration: 11,
        jpeg_quality: 70
    },
    audio: {
        enabled: true,
        sample_rate: 16000,
        channels: 1
    },
    wake_word: {
        wake_words: ["小凯小凯", "你好小凯", "小凯同学"],
        sensitivity: 0.8
    },
    storage: {
        video_path: 'data/video_warning',
        audio_path: 'data/voice',
        max_storage_days: 30
    },
    api: {
        qwen: {
            api_url: 'http://************:11434/v1/chat/completions',
            model: 'gemma3:4b'
        },
        moonshot: {
            api_url: 'http://************:11434/v1/chat/completions',
            model: 'qwen3:4b'
        },
        timeout: 60,
        temperature: 0.5,
        max_tokens: 2000,
        top_p: 0.01,
        top_k: 20,
        repetition_penalty: 1.05
    },
    rag: {
        milvus: {
            host: 'localhost',
            port: 19530,
            embedding_dim: 768
        },
        vector_api_url: 'http://************:11434/api/embeddings',
        history_file: 'video_histroy_info.txt'
    }
};

/**
 * 显示通知消息
 */
function showNotification(message, type = 'info') {
    // 创建通知元素
    const notification = document.createElement('div');
    notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
        type === 'success' ? 'bg-green-600' : 
        type === 'error' ? 'bg-red-600' : 
        type === 'warning' ? 'bg-yellow-600' : 'bg-blue-600'
    } text-white`;
    
    notification.innerHTML = `
        <div class="flex items-center">
            <i class="fas fa-${
                type === 'success' ? 'check-circle' : 
                type === 'error' ? 'exclamation-circle' : 
                type === 'warning' ? 'exclamation-triangle' : 'info-circle'
            } mr-2"></i>
            <span>${message}</span>
        </div>
    `;
    
    document.body.appendChild(notification);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 3000);
}

/**
 * 加载系统配置
 */
async function loadConfig() {
    try {
        showNotification('正在加载配置...', 'info');
        
        const response = await fetch(`${CONFIG_API_BASE}/api/config`);
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.status === 'success') {
            // 更新界面配置值
            updateConfigUI(result.config);
            showNotification('配置加载成功', 'success');
        } else {
            throw new Error(result.message || '加载配置失败');
        }
        
    } catch (error) {
        console.error('加载配置失败:', error);
        showNotification(`加载配置失败: ${error.message}`, 'error');
    }
}

/**
 * 更新配置界面
 */
function updateConfigUI(config) {
    // 视频配置
    if (config.video_source) {
        const videoSourceElement = document.getElementById('currentVideoSource');
        if (videoSourceElement) {
            videoSourceElement.textContent = config.video_source;
        }

        const videoSourceUrlElement = document.getElementById('videoSourceUrl');
        if (videoSourceUrlElement) {
            videoSourceUrlElement.value = config.video_source;
        }
    }

    // 视频处理配置
    if (config.video_config) {
        const vc = config.video_config;

        // 更新视频处理参数
        updateElementValue('analysisInterval', vc.analysis_interval);
        updateElementValue('bufferDuration', vc.buffer_duration);
        updateElementValue('videoQuality', vc.jpeg_quality);
        updateElementValue('videoInterval', vc.video_interval);
        updateElementValue('wsRetryInterval', vc.ws_retry_interval);
        updateElementValue('maxWsQueue', vc.max_ws_queue);
        updateElementValue('videoWarningDir', vc.video_storage_path);

        // 更新质量显示值
        const qualityValueElement = document.getElementById('videoQualityValue');
        if (qualityValueElement && vc.jpeg_quality) {
            qualityValueElement.textContent = vc.jpeg_quality;
        }
    }

    // 模型配置
    if (config.current_model) {
        const modelElement = document.getElementById('currentModel');
        if (modelElement) {
            modelElement.textContent = config.current_model.name || '未设置';
        }
    }

    // 其他配置项可以根据需要添加
    console.log('配置已更新:', config);
}

/**
 * 辅助函数：更新元素值
 */
function updateElementValue(elementId, value) {
    const element = document.getElementById(elementId);
    if (element && value !== undefined && value !== null) {
        if (element.type === 'checkbox') {
            element.checked = Boolean(value);
        } else {
            element.value = value;
        }
    }
}

/**
 * 保存配置
 */
async function saveConfig(configData) {
    try {
        showNotification('正在保存配置...', 'info');
        
        const response = await fetch(`${CONFIG_API_BASE}/api/config`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(configData)
        });
        
        if (!response.ok) {
            throw new Error(`HTTP ${response.status}: ${response.statusText}`);
        }
        
        const result = await response.json();
        
        if (result.status === 'success') {
            showNotification('配置保存成功', 'success');
        } else {
            throw new Error(result.message || '保存配置失败');
        }
        
    } catch (error) {
        console.error('保存配置失败:', error);
        showNotification(`保存配置失败: ${error.message}`, 'error');
    }
}

/**
 * 重置配置为默认值
 */
async function resetConfig() {
    if (confirm('确定要重置所有配置为默认值吗？此操作不可撤销。')) {
        try {
            showNotification('正在重置配置...', 'info');

            // 重置所有配置到默认值
            await resetToDefaults();

            // 直接更新界面到默认值，而不是重新加载
            updateUIToDefaults();

            showNotification('配置已重置为默认值', 'success');
        } catch (error) {
            console.error('重置配置失败:', error);
            showNotification(`重置配置失败: ${error.message}`, 'error');
        }
    }
}

/**
 * 将界面更新为默认值
 */
function updateUIToDefaults() {
    // 视频处理配置
    updateElementValue('analysisInterval', DEFAULT_CONFIG.video.analysis_interval);
    updateElementValue('bufferDuration', DEFAULT_CONFIG.video.buffer_duration);
    updateElementValue('videoQuality', DEFAULT_CONFIG.video.jpeg_quality);

    // 更新质量显示值
    const qualityValueElement = document.getElementById('videoQualityValue');
    if (qualityValueElement) {
        qualityValueElement.textContent = DEFAULT_CONFIG.video.jpeg_quality;
    }

    // 存储配置
    updateElementValue('videoWarningDir', DEFAULT_CONFIG.storage.video_path);
    updateElementValue('voiceDir', DEFAULT_CONFIG.storage.audio_path);

    // 唤醒词配置
    updateElementValue('wakeWord1', DEFAULT_CONFIG.wake_word.wake_words[0] || '');
    updateElementValue('wakeWord2', DEFAULT_CONFIG.wake_word.wake_words[1] || '');
    updateElementValue('wakeWord3', DEFAULT_CONFIG.wake_word.wake_words[2] || '');
    updateElementValue('wakeWord4', '');

    // API配置
    updateElementValue('qwenApiUrl', DEFAULT_CONFIG.api.qwen.api_url);
    updateElementValue('qwenModel', DEFAULT_CONFIG.api.qwen.model);
    updateElementValue('moonshotApiUrl', DEFAULT_CONFIG.api.moonshot.api_url);
    updateElementValue('moonshotModel', DEFAULT_CONFIG.api.moonshot.model);
    updateElementValue('requestTimeout', DEFAULT_CONFIG.api.timeout);
    updateElementValue('temperature', DEFAULT_CONFIG.api.temperature);
    updateElementValue('topP', DEFAULT_CONFIG.api.top_p);
    updateElementValue('topK', DEFAULT_CONFIG.api.top_k);
    updateElementValue('repetitionPenalty', DEFAULT_CONFIG.api.repetition_penalty);

    // RAG配置
    updateElementValue('vectorApiUrl', DEFAULT_CONFIG.rag.vector_api_url);
    updateElementValue('historyFile', DEFAULT_CONFIG.rag.history_file);
    updateElementValue('milvusHost', DEFAULT_CONFIG.rag.milvus.host);
    updateElementValue('milvusPort', DEFAULT_CONFIG.rag.milvus.port);
    updateElementValue('embeddingDim', DEFAULT_CONFIG.rag.milvus.embedding_dim);

    // 音频配置
    updateElementValue('audioSampleRate', DEFAULT_CONFIG.audio.sample_rate);
    updateElementValue('audioChannels', DEFAULT_CONFIG.audio.channels);
}

/**
 * 重置所有配置到默认值
 */
async function resetToDefaults() {
    // 构建完整的默认配置对象，使用后端支持的参数名称
    const fullDefaultConfig = {
        // 视频处理配置 (根据update_config函数支持的参数)
        analysis_interval: DEFAULT_CONFIG.video.analysis_interval,
        buffer_duration: DEFAULT_CONFIG.video.buffer_duration,
        jpeg_quality: DEFAULT_CONFIG.video.jpeg_quality,

        // 存储配置
        video_storage_path: DEFAULT_CONFIG.storage.video_path,

        // API配置 (根据update_config函数支持的参数)
        qwen_api_url: DEFAULT_CONFIG.api.qwen.api_url,
        qwen_model: DEFAULT_CONFIG.api.qwen.model,
        moonshot_api_url: DEFAULT_CONFIG.api.moonshot.api_url,
        moonshot_model: DEFAULT_CONFIG.api.moonshot.model,
        request_timeout: DEFAULT_CONFIG.api.timeout,
        temperature: DEFAULT_CONFIG.api.temperature,
        top_p: DEFAULT_CONFIG.api.top_p,
        top_k: DEFAULT_CONFIG.api.top_k,
        repetition_penalty: DEFAULT_CONFIG.api.repetition_penalty,

        // RAG配置 (根据update_config函数支持的参数)
        vector_api_url: DEFAULT_CONFIG.rag.vector_api_url,
        history_file: DEFAULT_CONFIG.rag.history_file
    };

    // 一次性重置所有配置
    const response = await fetch(`${CONFIG_API_BASE}/api/config`, {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json'
        },
        body: JSON.stringify(fullDefaultConfig)
    });

    if (!response.ok) {
        throw new Error(`重置配置失败: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    if (result.status !== 'success') {
        throw new Error(result.message || '重置配置失败');
    }
}

/**
 * 应用视频配置
 */
async function applyVideoConfig() {
    const videoConfig = {
        analysis_interval: document.getElementById('analysisInterval')?.value || 10,
        buffer_duration: document.getElementById('bufferDuration')?.value || 11,
        jpeg_quality: document.getElementById('jpegQuality')?.value || 70
    };
    
    await saveConfig({ video: videoConfig });
}

/**
 * 应用音频配置
 */
async function applyAudioConfig() {
    const audioConfig = {
        enabled: document.getElementById('audioEnabled')?.checked || false,
        sample_rate: document.getElementById('sampleRate')?.value || 16000,
        channels: document.getElementById('channels')?.value || 1
    };
    
    await saveConfig({ audio: audioConfig });
}

/**
 * 应用唤醒词配置
 */
async function applyWakeWordConfig() {
    const wakeWords = document.getElementById('wakeWords')?.value?.split(',').map(w => w.trim()) || [];
    const wakeWordConfig = {
        wake_words: wakeWords,
        sensitivity: document.getElementById('wakeWordSensitivity')?.value || 0.8
    };
    
    await saveConfig({ wake_word: wakeWordConfig });
}

/**
 * 应用存储配置
 */
async function applyStorageConfig() {
    const storageConfig = {
        video_path: document.getElementById('videoPath')?.value || 'data/video_warning',
        audio_path: document.getElementById('audioPath')?.value || 'data/voice',
        max_storage_days: document.getElementById('maxStorageDays')?.value || 30
    };
    
    await saveConfig({ storage: storageConfig });
}

/**
 * 应用API配置
 */
async function applyApiConfig() {
    const apiConfig = {
        qwen: {
            api_url: document.getElementById('qwenApiUrl')?.value || '',
            model: document.getElementById('qwenModel')?.value || ''
        },
        moonshot: {
            api_url: document.getElementById('moonshotApiUrl')?.value || '',
            model: document.getElementById('moonshotModel')?.value || ''
        },
        timeout: parseFloat(document.getElementById('requestTimeout')?.value || 60),
        temperature: parseFloat(document.getElementById('temperature')?.value || 0.5)
    };
    
    await saveConfig({ api: apiConfig });
}

/**
 * 应用RAG配置
 */
async function applyRagConfig() {
    const ragConfig = {
        milvus: {
            host: document.getElementById('milvusHost')?.value || 'localhost',
            port: parseInt(document.getElementById('milvusPort')?.value || 19530),
            embedding_dim: parseInt(document.getElementById('embeddingDim')?.value || 768)
        },
        vector_api_url: document.getElementById('vectorApiUrl')?.value || '',
        history_file: document.getElementById('historyFile')?.value || ''
    };
    
    await saveConfig({ rag: ragConfig });
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    console.log('配置页面初始化...');
    
    // 绑定按钮事件
    const loadConfigBtn = document.getElementById('loadConfig');
    if (loadConfigBtn) {
        loadConfigBtn.addEventListener('click', loadConfig);
    }
    
    const resetConfigBtn = document.getElementById('resetConfig');
    if (resetConfigBtn) {
        resetConfigBtn.addEventListener('click', resetConfig);
    }
    
    // 绑定各个配置保存按钮
    const configButtons = [
        { id: 'applyVideoConfig', handler: applyVideoConfig },
        { id: 'applyAudioConfig', handler: applyAudioConfig },
        { id: 'applyWakeWordConfig', handler: applyWakeWordConfig },
        { id: 'applyStorageConfig', handler: applyStorageConfig },
        { id: 'applyApiConfig', handler: applyApiConfig },
        { id: 'applyRagConfig', handler: applyRagConfig }
    ];
    
    configButtons.forEach(({ id, handler }) => {
        const button = document.getElementById(id);
        if (button) {
            button.addEventListener('click', handler);
        }
    });
    
    // 自动加载当前配置
    loadConfig();
    
    console.log('配置页面初始化完成');
});
