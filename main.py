#!/usr/bin/env python3
"""
AI安全监控系统主启动文件
"""

import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

# 确保工作目录是项目根目录
os.chdir(project_root)

from src.core.config_loader import config_loader
import uvicorn


def setup_logging():
    """设置日志配置"""
    log_config = config_loader.get_logging_config()
    
    # 创建logs目录
    os.makedirs("logs", exist_ok=True)
    
    # 配置日志
    logging.basicConfig(
        level=getattr(logging, log_config['level']),
        format=log_config['format'],
        handlers=[
            logging.FileHandler(log_config['handlers'][0]['filename']),
            logging.StreamHandler()
        ]
    )


def main():
    """主函数"""
    print("=" * 50)
    print("AI安全监控系统启动中...")
    print("=" * 50)

    # 确保必要的目录存在
    import os
    os.makedirs("logs", exist_ok=True)
    os.makedirs("data/video_warning", exist_ok=True)
    os.makedirs("data/uploads", exist_ok=True)
    os.makedirs("data/voice", exist_ok=True)

    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # 启动视频服务器
        logger.info("启动视频监控服务...")

        # 显示当前配置
        video_source = config_loader.get_video_source()
        current_model = config_loader.get_current_model()
        logger.info(f"视频源: {video_source}")
        logger.info(f"检测模型: {current_model['name']}")

        # 获取服务器配置
        server_config = config_loader.get_server_config()

        # 启动uvicorn服务器
        uvicorn.run(
            app="src.video.video_server:app",
            host=server_config['host'],
            port=server_config['port'],
            reload=server_config['reload'],
            workers=server_config['workers']
        )

    except KeyboardInterrupt:
        logger.info("用户中断，系统正在关闭...")
    except Exception as e:
        logger.error(f"系统启动失败: {e}")
        raise
    finally:
        logger.info("AI安全监控系统已关闭")


if __name__ == "__main__":
    main()
