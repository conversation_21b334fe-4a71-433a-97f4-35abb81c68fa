#!/usr/bin/env python3
"""
测试导入是否正常
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

print("项目根目录:", project_root)
print("当前工作目录:", os.getcwd())
print("Python路径:", sys.path[:3])

try:
    print("\n测试核心配置导入...")
    from src.core.config_adapter import VideoConfig, VIDEO_SOURCE
    print("✅ 核心配置导入成功")
    print("视频源:", VIDEO_SOURCE)
    
    print("\n测试工具模块导入...")
    from utils.alert_statistics import query_recent_alert_counts
    print("✅ 警报统计导入成功")
    
    print("\n测试模型模块导入...")
    from src.models.multi_modal_analyzer import MultiModalAnalyzer
    print("✅ 多模态分析器导入成功")
    
    print("\n测试API模块导入...")
    from src.api.rag_query import rag_query
    print("✅ RAG查询导入成功")
    
    print("\n测试语音模块导入...")
    from src.voice.voice_assistant import VoiceConfig
    print("✅ 语音助手导入成功")
    
    print("\n测试视频文件是否存在...")
    if os.path.exists(VIDEO_SOURCE):
        print(f"✅ 视频文件存在: {VIDEO_SOURCE}")
    else:
        print(f"❌ 视频文件不存在: {VIDEO_SOURCE}")
        
    print("\n所有导入测试完成！")
    
except ImportError as e:
    print(f"❌ 导入错误: {e}")
    import traceback
    traceback.print_exc()
except Exception as e:
    print(f"❌ 其他错误: {e}")
    import traceback
    traceback.print_exc()
