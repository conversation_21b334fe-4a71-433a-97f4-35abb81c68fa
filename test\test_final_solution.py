#!/usr/bin/env python3
"""
测试最终解决方案
"""

import sys
import os
import time
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def create_test_conversation():
    """创建测试对话数据"""
    print("创建测试对话数据...")
    
    try:
        from src.voice.conversation_manager import add_conversation_message, clear_conversation_history
        
        # 清空现有历史
        clear_conversation_history()
        
        # 添加测试对话
        add_conversation_message("user", "你好，我想查询今天的警报", {"confidence": 0.95})
        time.sleep(0.1)
        add_conversation_message("assistant", "您好！今天共有3个警报事件，都已处理完毕", {"response_time": 1.2})
        time.sleep(0.1)
        add_conversation_message("user", "能详细说明一下吗？", {"confidence": 0.88})
        time.sleep(0.1)
        add_conversation_message("assistant", "当然可以。第一个是入侵检测警报，发生在上午10:30；第二个是异常行为检测，发生在下午2:15；第三个是设备状态异常，已自动修复。", {"response_time": 0.8})
        time.sleep(0.1)
        add_conversation_message("user", "系统运行状态如何？", {"confidence": 0.92})
        time.sleep(0.1)
        add_conversation_message("assistant", "系统运行正常，所有监控设备在线，检测准确率98.5%", {"response_time": 1.0})
        
        print("✅ 测试对话数据创建完成")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        return False

def verify_file_exists():
    """验证对话历史文件是否存在"""
    print("\n验证对话历史文件...")
    
    try:
        import json
        
        history_file = "data/voice/conversation_history.json"
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"✅ 文件存在: {history_file}")
            print(f"✅ 包含 {len(data)} 条对话记录")
            
            # 显示对话内容
            print("对话内容预览:")
            for i, msg in enumerate(data):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                timestamp = msg.get('timestamp', '')[:19]
                print(f"   {i+1}. [{role}] {content[:40]}... ({timestamp})")
            
            return True
        else:
            print(f"❌ 文件不存在: {history_file}")
            return False
            
    except Exception as e:
        print(f"❌ 验证失败: {e}")
        return False

def test_web_access():
    """测试Web访问"""
    print("\n测试Web访问...")
    
    try:
        import requests
        
        # 测试静态文件访问
        response = requests.get("http://127.0.0.1:16532/data/voice/conversation_history.json", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 静态文件访问成功")
            print(f"✅ 获取到 {len(data)} 条对话记录")
            return True
        else:
            print(f"❌ 静态文件访问失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def simulate_voice_conversation():
    """模拟语音对话"""
    print("\n模拟语音对话...")
    
    try:
        from src.voice.conversation_manager import add_conversation_message
        
        # 模拟实时对话
        conversations = [
            ("user", "检查一下监控系统状态", {"confidence": 0.93}),
            ("assistant", "正在检查监控系统状态...所有摄像头正常工作，检测算法运行稳定", {"response_time": 1.5}),
            ("user", "有没有新的异常事件？", {"confidence": 0.89}),
            ("assistant", "最近1小时内没有检测到异常事件，系统运行正常", {"response_time": 1.1}),
        ]
        
        for role, content, metadata in conversations:
            add_conversation_message(role, content, metadata)
            print(f"   添加: [{role}] {content[:30]}...")
            time.sleep(0.5)  # 模拟对话间隔
        
        print("✅ 模拟对话完成")
        return True
        
    except Exception as e:
        print(f"❌ 模拟对话失败: {e}")
        return False

def main():
    print("语音助手对话记录显示 - 最终解决方案测试")
    print("=" * 80)
    
    # 步骤1: 创建测试对话
    step1 = create_test_conversation()
    
    # 步骤2: 验证文件存在
    step2 = verify_file_exists()
    
    # 步骤3: 模拟更多对话
    step3 = simulate_voice_conversation()
    
    # 步骤4: 测试Web访问
    step4 = test_web_access()
    
    print("\n" + "=" * 80)
    
    if step1 and step2 and step3:
        print("🎉 基础功能测试全部通过!")
        
        if step4:
            print("🎉 Web访问测试也通过!")
            print("\n✅ 完整解决方案测试成功!")
            
            print("\n📋 现在您可以:")
            print("1. 启动主服务器: python main.py")
            print("2. 访问: http://127.0.0.1:16532")
            print("3. 与语音助手对话")
            print("4. 在对话记录区域查看实时对话内容")
            
            print("\n🔧 技术实现:")
            print("- 全局对话管理器: 统一管理所有对话历史")
            print("- 文件持久化: 对话自动保存到JSON文件")
            print("- 多重获取: API -> 文件 -> 模拟数据")
            print("- 静态文件服务: 前端可直接访问对话文件")
            print("- 实时更新: 每2秒自动检查新对话")
            
        else:
            print("⚠️ Web访问测试失败，但基础功能正常")
            print("请确保主服务器正在运行并且端口16532可访问")
    else:
        print("❌ 部分测试失败")
        print("请检查实现细节")
    
    print(f"\n📁 对话历史文件位置: {os.path.abspath('data/voice/conversation_history.json')}")

if __name__ == "__main__":
    main()
