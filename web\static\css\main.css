/* AI安全监控系统 - 主样式文件 */

/* 基础样式 */
body {
  font-family: 'Noto Sans SC', sans-serif;
  background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  min-height: 100vh;
}

/* 视频容器样式 */
.video-container {
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

/* 视频容器动效 */
#video-container {
  background: var(--bg-dark);
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  transition: transform 0.3s ease;
  position: relative;
  width: 100%;
  height: 0;
  padding-top: 56.25%; /* 16:9 比例 */
}

#videoFeed {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
}

#video-container:hover {
  transform: translateY(-2px);
}

/* 警报相关样式 */
.alert-item {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  transition: all 0.3s ease;
}

.alert-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

/* 警报信息面板 */
#alerts-panel {
  flex: 1;
  background: #2d2d2d;
  border-radius: 8px;
  padding: 15px;
  color: white;
  overflow-y: auto;
}

/* 警报条目样式 */
.alert-item {
  background: #3a3a3a;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 4px;
  animation: fadeIn 0.5s;
}

/* 粒子效果 */
#particles-js {
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 动画效果 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 语音播报动画 */
.animate-pulse {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0%, 100% { transform: scale(1); }
  50% { transform: scale(1.2); }
}

/* 滚动条样式 */
#alertList::-webkit-scrollbar {
  width: 6px;
}

#alertList::-webkit-scrollbar-track {
  background: #2d3748;
  border-radius: 3px;
}

#alertList::-webkit-scrollbar-thumb {
  background-color: #4a5568;
  border-radius: 3px;
}

/* 算法标签样式 */
.algorithm-tag {
  background: linear-gradient(135deg, #3b82f6 0%, #1d4ed8 100%);
  border: 1px solid rgba(59, 130, 246, 0.3);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
}

.algorithm-tag:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.algorithm-tag .remove-btn {
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.algorithm-tag:hover .remove-btn {
  opacity: 1;
}

#selectedAlgorithmTags {
  min-height: 60px;
  max-height: 120px;
  overflow-y: auto;
}

#selectedAlgorithmTags::-webkit-scrollbar {
  width: 4px;
}

#selectedAlgorithmTags::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 2px;
}

#selectedAlgorithmTags::-webkit-scrollbar-thumb {
  background-color: #6b7280;
  border-radius: 2px;
}

/* 语音助手对话区域滚动条样式 */
#conversationArea::-webkit-scrollbar {
  width: 4px;
}

#conversationArea::-webkit-scrollbar-track {
  background: #374151;
  border-radius: 2px;
}

#conversationArea::-webkit-scrollbar-thumb {
  background-color: #6b7280;
  border-radius: 2px;
}
