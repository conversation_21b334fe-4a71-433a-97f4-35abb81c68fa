
// ai_security_monitor/frontend/js/websocket.js
class WebSocketClient {
  constructor() {
    this.socket = null;
    this.reconnectAttempts = 0;
    this.maxReconnectAttempts = 5;
    this.reconnectInterval = 3000;
    this.connected = false;
    this.messageHandlers = [];
    this.serverUrl = 'ws://localhost:16532';
  }

  connect() {
    if (this.connected) return;

    this.socket = new WebSocket(this.serverUrl);

    this.socket.onopen = () => {
      console.log('WebSocket连接已建立');
      this.connected = true;
      this.reconnectAttempts = 0;
      this.notifyConnectionStatus(true);
    };

    this.socket.onmessage = (event) => {
      try {
        const message = JSON.parse(event.data);
        this.handleMessage(message);
      } catch (error) {
        console.error('消息解析失败:', error);
      }
    };

    this.socket.onclose = () => {
      console.log('WebSocket连接已关闭');
      this.connected = false;
      this.notifyConnectionStatus(false);
      this.attemptReconnect();
    };

    this.socket.onerror = (error) => {
      console.error('WebSocket错误:', error);
      this.connected = false;
      this.notifyConnectionStatus(false);
    };
  }

  attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`尝试重新连接 (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      setTimeout(() => this.connect(), this.reconnectInterval);
    }
  }

  disconnect() {
    if (this.socket) {
      this.socket.close();
      this.socket = null;
      this.connected = false;
    }
  }

  send(message) {
    if (this.connected && this.socket) {
      this.socket.send(JSON.stringify(message));
    } else {
      console.error('无法发送消息: WebSocket未连接');
    }
  }

  addMessageHandler(handler) {
    this.messageHandlers.push(handler);
  }

  removeMessageHandler(handler) {
    this.messageHandlers = this.messageHandlers.filter(h => h !== handler);
  }

  handleMessage(message) {
    this.messageHandlers.forEach(handler => {
      try {
        handler(message);
      } catch (error) {
        console.error('消息处理失败:', error);
      }
    });
  }

  notifyConnectionStatus(connected) {
    const statusElement = document.getElementById('videoStatus');
    const sourceElement = document.getElementById('videoSource');
    
    if (connected) {
      statusElement.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
      sourceElement.textContent = '服务器连接正常';
    } else {
      statusElement.className = 'inline-block w-3 h-3 rounded-full bg-red-500 mr-2';
      sourceElement.textContent = '服务器连接断开';
    }
  }
}

// 全局WebSocket实例
const wsClient = new WebSocketClient();

// 初始化WebSocket连接
document.addEventListener('DOMContentLoaded', () => {
  wsClient.connect();
  
  // 添加预警消息处理器
  wsClient.addMessageHandler((message) => {
    if (message.type === 'alert') {
      addAlertToUI(message.data);
    }
  });
});

// 添加预警到UI
function addAlertToUI(alertData) {
  const alertList = document.getElementById('alertList');
  const alertCount = document.getElementById('alertCount');
  
  const alertItem = document.createElement('div');
  alertItem.className = `alert-item rounded-lg p-4 border-l-4 border-${alertData.type === 'danger' ? 'red' : 'yellow'}-500`;
  
  alertItem.innerHTML = `
    <div class="flex justify-between items-start">
      <div>
        <h4 class="font-medium">${alertData.title}</h4>
        <p class="text-sm text-gray-400 mt-1">${alertData.timestamp}</p>
      </div>
      <button class="text-gray-400 hover:text-white">
        <i class="fas fa-ellipsis-v"></i>
      </button>
    </div>
    <p class="text-sm mt-2">${alertData.description}</p>
  `;
  
  alertList.insertBefore(alertItem, alertList.firstChild);
  alertCount.textContent = parseInt(alertCount.textContent) + 1;
  
  // 播放预警音效
  playAlertSound();
}

// 播放预警音效
function playAlertSound() {
  const audio = new Audio('/static/alert.mp3');
  audio.volume = 0.5;
  audio.play().catch(e => {
    console.error('音效播放失败:', e);
    showSystemMessage('警报音效加载失败，请检查声音文件');
  });
}

// 发送视频帧进行分析
function sendVideoFrame(frameData) {
  if (wsClient.connected) {
    wsClient.send({
      type: 'video_frame',
      data: frameData,
      timestamp: new Date().toISOString(),
      model: document.getElementById('detectionModel').value,
      sensitivity: document.getElementById('sensitivity').value
    });
  }
}

// 导出WebSocket客户端
export { wsClient, sendVideoFrame };
