#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音助手控制器
功能：统一管理语音交互流程
"""

import asyncio
import json
import re
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional
import logging

from src.voice.voice_assistant import (
    WakeWordDetector, SpeechRecognizer, TextToSpeech,
    AudioRecorder, DialogueManager, VoiceConfig
)
from src.voice.conversation_manager import add_conversation_message

# 使用try-except避免依赖问题
try:
    from src.api.rag_query import rag_query
    RAG_AVAILABLE = True
except ImportError as e:
    print(f"RAG查询不可用，尝试简化版: {e}")
    try:
        from src.api.rag_query_simple import rag_query
        RAG_AVAILABLE = True
        print("使用简化版RAG查询")
    except ImportError as e2:
        print(f"简化版RAG查询也不可用: {e2}")
        RAG_AVAILABLE = False
        async def rag_query(query):
            return f"模拟回复: {query}"

try:
    from alert_statistics import query_recent_alert_counts, query_recent_alerts
    ALERT_STATS_AVAILABLE = True
except ImportError as e:
    print(f"警报统计不可用: {e}")
    ALERT_STATS_AVAILABLE = False
    def query_recent_alert_counts():
        return ["模拟警报"], [1]
    def query_recent_alerts():
        return ["模拟警报"], [1]

logger = logging.getLogger(__name__)

class VoiceAssistantController:
    """语音助手控制器"""
    
    def __init__(self):
        # 初始化组件
        self.wake_detector = WakeWordDetector(VoiceConfig.WAKE_WORDS)
        self.speech_recognizer = SpeechRecognizer()
        self.tts = TextToSpeech()
        self.recorder = AudioRecorder()
        self.dialogue_manager = DialogueManager()

        # 状态管理
        self.is_active = False
        self.is_listening = False
        self.conversation_active = False

        # 休眠管理
        self.last_interaction_time = time.time()
        self.sleep_timeout = 300  # 5分钟无交互后进入休眠
        self.is_sleeping = False
        
        # 注册命令处理器
        self.command_handlers = {
            "查询": self._handle_query_command,
            "统计": self._handle_statistics_command,
            "控制": self._handle_control_command,
            "帮助": self._handle_help_command,
            "退出": self._handle_exit_command,
        }
    
    async def start(self):
        """启动语音助手"""
        logger.info("🎙️ 语音助手启动中...")
        
        self.is_active = True
        
        # 播放启动提示
        await self.tts.speak("语音助手已启动，请说唤醒词开始对话")
        
        # 开始连续录音和唤醒词检测
        self.recorder.start_continuous_recording()
        
        # 启动主循环
        await self._main_loop()
    
    async def stop(self):
        """停止语音助手"""
        logger.info("🎙️ 语音助手停止中...")
        
        self.is_active = False
        self.recorder.stop_recording()
        
        await self.tts.speak("语音助手已关闭")
    
    async def _main_loop(self):
        """主循环：监听唤醒词和处理对话"""
        logger.info("🎙️ 开始监听唤醒词...")
        
        wake_audio_buffer = []
        buffer_size = int(VoiceConfig.SAMPLE_RATE * 2)  # 2秒音频缓冲
        
        while self.is_active:
            try:
                # 检查是否需要进入休眠
                current_time = time.time()
                if current_time - self.last_interaction_time > self.sleep_timeout:
                    if not self.is_sleeping:
                        await self._enter_sleep_mode()

                # 如果在休眠状态，降低检测频率
                if self.is_sleeping:
                    await asyncio.sleep(1)  # 休眠时每秒检查一次
                else:
                    await asyncio.sleep(0.1)  # 正常时每0.1秒检查一次

                # 获取音频数据
                if not self.recorder.audio_queue.empty():
                    audio_chunk = self.recorder.audio_queue.get()
                    wake_audio_buffer.extend(audio_chunk)

                    # 保持缓冲区大小
                    if len(wake_audio_buffer) > buffer_size:
                        wake_audio_buffer = wake_audio_buffer[-buffer_size:]

                    # 检测唤醒词（每0.5秒检测一次）
                    if len(wake_audio_buffer) >= buffer_size // 4:
                        if self.wake_detector.detect(wake_audio_buffer[-buffer_size // 4:]):
                            # 如果在休眠状态，先唤醒
                            if self.is_sleeping:
                                await self._wake_from_sleep()
                            await self._handle_wake_up()

            except Exception as e:
                logger.error(f"主循环错误: {e}")
                await asyncio.sleep(1)
    
    async def _enter_sleep_mode(self):
        """进入休眠模式"""
        self.is_sleeping = True
        logger.info("😴 语音助手进入休眠模式")
        await self.tts.speak("长时间无交互，进入休眠模式")

    async def _wake_from_sleep(self):
        """从休眠模式唤醒"""
        self.is_sleeping = False
        self.last_interaction_time = time.time()
        logger.info("🌅 语音助手从休眠模式唤醒")
        await self.tts.speak("已唤醒，小凯为您服务")

    async def _handle_wake_up(self):
        """处理唤醒事件"""
        logger.info("🎙️ 检测到唤醒词，开始对话...")

        # 更新交互时间
        self.last_interaction_time = time.time()
        self.conversation_active = True

        # 播放唤醒提示音
        await self.tts.speak("我在，请说")
        
        # 开始对话循环
        failed_attempts = 0  # 连续失败次数
        max_failed_attempts = 3  # 最大连续失败次数

        while self.conversation_active and self.is_active:
            try:
                # 检查对话超时
                if self.dialogue_manager.is_conversation_timeout():
                    logger.info("对话超时，结束当前会话")
                    await self.tts.speak("对话超时，我将结束当前会话")
                    self.conversation_active = False
                    break

                # 录制用户命令
                audio_file = self.recorder.record_command()
                if not audio_file:
                    failed_attempts += 1
                    if failed_attempts >= max_failed_attempts:
                        logger.info("连续多次未检测到语音，结束对话")
                        await self.tts.speak("长时间未听到您的声音，结束对话")
                        self.conversation_active = False
                        break
                    await self.tts.speak("没有听到您的声音，请重新说话")
                    continue

                # 语音识别
                user_text = await self.speech_recognizer.recognize(audio_file)
                if not user_text:
                    failed_attempts += 1
                    if failed_attempts >= max_failed_attempts:
                        logger.info("连续多次语音识别失败，结束对话")
                        await self.tts.speak("长时间未能识别您的语音，结束对话")
                        self.conversation_active = False
                        break
                    await self.tts.speak("抱歉，我没有听清楚，请重新说话")
                    continue

                # 重置失败计数
                failed_attempts = 0

                logger.info(f"用户说: {user_text}")

                # 更新交互时间
                self.last_interaction_time = time.time()

                # 添加到对话历史
                self.dialogue_manager.add_message("user", user_text)
                # 同时添加到全局对话管理器
                add_conversation_message("user", user_text)

                # 处理命令
                response = await self._process_command(user_text)

                # 语音回复
                await self.tts.speak(response)

                # 添加到对话历史
                self.dialogue_manager.add_message("assistant", response)
                # 同时添加到全局对话管理器
                add_conversation_message("assistant", response)

                # 检查是否继续对话
                if "退出" in user_text or "再见" in user_text or "结束" in user_text:
                    self.conversation_active = False

            except Exception as e:
                logger.error(f"对话处理错误: {e}")
                failed_attempts += 1
                if failed_attempts >= max_failed_attempts:
                    logger.info("连续多次处理错误，结束对话")
                    await self.tts.speak("系统出现问题，结束对话")
                    self.conversation_active = False
                    break
                await self.tts.speak("抱歉，处理您的请求时出现了错误")
        
        logger.info("🎙️ 对话结束，返回监听模式")
    
    async def _process_command(self, user_text: str) -> str:
        """处理用户命令"""
        try:
            # 命令分类和路由
            command_type = self._classify_command(user_text)
            
            if command_type in self.command_handlers:
                return await self.command_handlers[command_type](user_text)
            else:
                # 使用RAG进行通用查询
                return await self._handle_general_query(user_text)
                
        except Exception as e:
            logger.error(f"命令处理错误: {e}")
            return "抱歉，处理您的请求时出现了错误，请重试"
    
    def _classify_command(self, text: str) -> str:
        """分类用户命令"""
        # 查询类命令
        if any(word in text for word in ["查询", "查看", "显示", "告诉我"]):
            return "查询"
        
        # 统计类命令
        if any(word in text for word in ["统计", "数量", "多少", "趋势"]):
            return "统计"
        
        # 控制类命令
        if any(word in text for word in ["开始", "停止", "暂停", "设置"]):
            return "控制"
        
        # 帮助命令
        if any(word in text for word in ["帮助", "功能", "能做什么"]):
            return "帮助"
        
        # 退出命令
        if any(word in text for word in ["退出", "再见", "结束", "关闭"]):
            return "退出"
        
        return "通用"
    
    async def _handle_query_command(self, text: str) -> str:
        """处理查询命令"""
        try:
            # 提取查询内容
            if "最近" in text and "警报" in text:
                # 查询最近警报
                try:
                    labels, data = query_recent_alerts()
                    if labels and data:
                        total_alerts = sum(data)
                        top_types = sorted(zip(labels, data), key=lambda x: x[1], reverse=True)[:3]
                        
                        response = f"最近共有{total_alerts}个警报。"
                        if top_types:
                            response += "主要类型包括："
                            for alert_type, count in top_types:
                                response += f"{alert_type}{count}个，"
                            response = response.rstrip("，")
                        
                        return response
                    else:
                        return "最近没有警报记录"
                except Exception as e:
                    logger.error(f"查询警报失败: {e}")
                    return "查询警报信息时出现错误"
            
            elif "系统状态" in text:
                # 查询系统状态
                return "系统运行正常，所有模块工作正常"
            
            else:
                # 使用RAG进行通用查询
                return await rag_query(text)
                
        except Exception as e:
            logger.error(f"查询命令处理错误: {e}")
            return "查询失败，请重试"
    
    async def _handle_statistics_command(self, text: str) -> str:
        """处理统计命令"""
        try:
            dates, counts = query_recent_alert_counts()
            if dates and counts:
                total = sum(counts)
                avg = total / len(counts) if counts else 0
                
                return f"最近{len(dates)}天共有{total}个警报，平均每天{avg:.1f}个"
            else:
                return "暂无统计数据"
                
        except Exception as e:
            logger.error(f"统计命令处理错误: {e}")
            return "统计查询失败，请重试"
    
    async def _handle_control_command(self, text: str) -> str:
        """处理控制命令"""
        if "开始分析" in text:
            return "已开始视频分析"
        elif "停止分析" in text:
            return "已停止视频分析"
        elif "清空对话" in text:
            self.dialogue_manager.clear_context()
            return "对话历史已清空"
        else:
            return "控制命令已执行"
    
    async def _handle_help_command(self, text: str) -> str:
        """处理帮助命令"""
        help_text = """
        我可以帮您：
        1. 查询最近的警报信息，比如说"查询最近的警报"
        2. 统计警报数据，比如说"统计警报数量"
        3. 控制系统功能，比如说"开始分析"
        4. 回答关于监控系统的问题
        5. 说"退出"可以结束对话
        """
        return help_text.strip()
    
    async def _handle_exit_command(self, text: str) -> str:
        """处理退出命令"""
        self.conversation_active = False
        return "好的，再见！"
    
    async def _handle_general_query(self, text: str) -> str:
        """处理通用查询"""
        try:
            # 添加对话上下文
            context = self.dialogue_manager.get_context()
            if context:
                enhanced_query = f"对话历史：\n{context}\n\n当前问题：{text}"
            else:
                enhanced_query = text
            
            response = await rag_query(enhanced_query)
            return response if response else "抱歉，我无法理解您的问题，请重新表述"
            
        except Exception as e:
            logger.error(f"通用查询错误: {e}")
            return "抱歉，查询时出现错误，请重试"

# 全局语音助手实例
voice_assistant = None

async def start_voice_assistant():
    """启动语音助手"""
    global voice_assistant
    
    if voice_assistant is None:
        voice_assistant = VoiceAssistantController()
    
    await voice_assistant.start()

async def stop_voice_assistant():
    """停止语音助手"""
    global voice_assistant
    
    if voice_assistant:
        await voice_assistant.stop()

async def trigger_voice_alert(alert_text: str):
    """触发语音警报播报"""
    global voice_assistant
    
    if voice_assistant and voice_assistant.is_active:
        try:
            # 播放警报提示音
            await voice_assistant.tts.speak(f"警报提醒：{alert_text}")
            logger.info(f"语音播报警报: {alert_text}")
        except Exception as e:
            logger.error(f"语音播报失败: {e}")

if __name__ == "__main__":
    # 测试运行
    async def main():
        try:
            print("=" * 50)
            print("🎙️ 语音对话系统独立测试")
            print("=" * 50)
            print("正在启动语音助手...")
            print("支持的唤醒词: 小助手、AI助手、语音助手、智能助手")
            print("按 Ctrl+C 退出")
            print("=" * 50)

            await start_voice_assistant()
        except KeyboardInterrupt:
            print("\n正在停止语音助手...")
            await stop_voice_assistant()
            print("语音助手已停止")
        except Exception as e:
            print(f"启动失败: {e}")
            import traceback
            traceback.print_exc()

    # 兼容Python 3.6及以下版本
    if hasattr(asyncio, 'run'):
        asyncio.run(main())
    else:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
