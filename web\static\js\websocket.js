/**
 * WebSocket连接管理
 * 负责视频流和警报的WebSocket连接
 */

// WebSocket连接管理器
class WebSocketManager {
    constructor() {
        this.videoSocket = null;
        this.alertSocket = null;
        this.reconnectAttempts = 0;
        this.maxReconnectAttempts = 5;
        this.reconnectDelay = 3000;
        this.isConnecting = false;
    }

    /**
     * 初始化WebSocket连接
     */
    initialize() {
        this.connectVideoStream();
        this.connectAlertStream();
    }

    /**
     * 连接视频流WebSocket
     */
    connectVideoStream() {
        if (this.isConnecting) return;

        this.isConnecting = true;

        // 检查是否有可用的WebSocket服务
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        let wsUrl;

        // 使用正确的WebSocket路径
        // 后端服务运行在16532端口，WebSocket路径是/video_feed
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        wsUrl = `${protocol}//${host}:${port}/video_feed`;

        console.log('尝试连接视频流WebSocket:', wsUrl);

        try {
            this.videoSocket = new WebSocket(wsUrl);
            
            this.videoSocket.onopen = () => {
                console.log('视频流WebSocket连接成功');
                this.reconnectAttempts = 0;
                this.isConnecting = false;
                this.updateVideoStatus('connected');
            };

            this.videoSocket.onmessage = (event) => {
                this.handleVideoMessage(event);
            };

            this.videoSocket.onclose = (event) => {
                console.log('视频流WebSocket连接关闭:', event.code, event.reason);
                this.isConnecting = false;
                this.updateVideoStatus('disconnected');

                // 只有在非正常关闭时才重连
                if (event.code !== 1000) {
                    this.scheduleVideoReconnect();
                }
            };

            this.videoSocket.onerror = (error) => {
                console.warn('视频流WebSocket连接失败，这是正常的（如果没有视频服务）');
                this.isConnecting = false;
                this.updateVideoStatus('no_service');
            };

        } catch (error) {
            console.error('创建视频流WebSocket失败:', error);
            this.isConnecting = false;
            this.updateVideoStatus('error');
        }
    }

    /**
     * 连接警报WebSocket
     */
    connectAlertStream() {
        const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
        // 使用正确的WebSocket路径
        // 后端服务运行在16532端口，WebSocket路径是/alerts
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const wsUrl = `${protocol}//${host}:${port}/alerts`;

        console.log('尝试连接警报WebSocket:', wsUrl);

        try {
            this.alertSocket = new WebSocket(wsUrl);

            this.alertSocket.onopen = () => {
                console.log('警报WebSocket连接成功');
            };

            this.alertSocket.onmessage = (event) => {
                this.handleAlertMessage(event);
            };

            this.alertSocket.onclose = (event) => {
                console.log('警报WebSocket连接关闭:', event.code, event.reason);
                // 只有在非正常关闭时才重连
                if (event.code !== 1000) {
                    this.scheduleAlertReconnect();
                }
            };

            this.alertSocket.onerror = (error) => {
                console.warn('警报WebSocket连接失败，这是正常的（如果没有警报服务）');
            };

        } catch (error) {
            console.warn('创建警报WebSocket失败，这是正常的（如果没有警报服务）');
        }
    }

    /**
     * 处理视频消息
     */
    handleVideoMessage(event) {
        try {
            if (event.data instanceof Blob) {
                // 处理二进制视频数据
                const url = URL.createObjectURL(event.data);
                const videoFeed = document.getElementById('videoFeed');
                if (videoFeed) {
                    videoFeed.src = url;
                    // 清理旧的URL对象
                    if (videoFeed.previousSrc) {
                        URL.revokeObjectURL(videoFeed.previousSrc);
                    }
                    videoFeed.previousSrc = url;
                }
            } else {
                // 处理JSON消息
                const data = JSON.parse(event.data);
                this.handleVideoData(data);
            }
        } catch (error) {
            console.error('处理视频消息失败:', error);
        }
    }

    /**
     * 处理视频数据
     */
    handleVideoData(data) {
        if (data.type === 'frame') {
            // 处理视频帧
            const videoFeed = document.getElementById('videoFeed');
            if (videoFeed && data.image) {
                videoFeed.src = `data:image/jpeg;base64,${data.image}`;
            }
        } else if (data.type === 'status') {
            // 处理状态更新
            this.updateVideoStatus(data.status);
        }
    }

    /**
     * 处理警报消息
     */
    handleAlertMessage(event) {
        try {
            const data = JSON.parse(event.data);
            console.log('收到WebSocket消息:', data);

            if (data.type === 'conversation') {
                // 处理对话消息
                this.handleConversationMessage(data);
            } else {
                // 处理警报消息
                const alertEvent = new CustomEvent('newAlert', {
                    detail: data
                });
                document.dispatchEvent(alertEvent);
            }

        } catch (error) {
            console.error('处理WebSocket消息失败:', error);
        }
    }

    /**
     * 处理对话消息
     */
    handleConversationMessage(data) {
        try {
            console.log('收到对话消息:', data.message);

            // 更新对话历史显示
            this.updateConversationDisplay(data.message);

            // 触发对话更新事件
            const conversationEvent = new CustomEvent('newConversation', {
                detail: data.message
            });
            document.dispatchEvent(conversationEvent);

        } catch (error) {
            console.error('处理对话消息失败:', error);
        }
    }

    /**
     * 更新对话显示
     */
    updateConversationDisplay(message) {
        const historyContainer = document.getElementById('conversationHistory');
        if (!historyContainer) return;

        // 如果是空状态，先清空
        if (historyContainer.innerHTML.includes('暂无对话记录')) {
            historyContainer.innerHTML = '';
        }

        // 创建消息元素
        const messageDiv = document.createElement('div');
        const isUser = message.role === 'user';

        messageDiv.className = `mb-3 p-2 rounded-lg border-l-4 ${
            isUser ? 'bg-blue-900 border-blue-400 text-blue-100' : 'bg-green-900 border-green-400 text-green-100'
        }`;

        const time = new Date(message.timestamp).toLocaleTimeString('zh-CN', {
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit'
        });

        messageDiv.innerHTML = `
            <div class="flex items-center mb-1">
                <i class="fas fa-${isUser ? 'user' : 'robot'} mr-2"></i>
                <span class="font-medium">${isUser ? '用户' : '助手'}</span>
                <span class="text-xs ml-auto opacity-70">${time}</span>
            </div>
            <div class="text-sm">${message.content}</div>
        `;

        // 添加到容器
        historyContainer.appendChild(messageDiv);

        // 滚动到底部
        historyContainer.scrollTop = historyContainer.scrollHeight;

        // 更新统计
        this.updateConversationStats();
    }

    /**
     * 更新对话统计
     */
    updateConversationStats() {
        const historyContainer = document.getElementById('conversationHistory');
        const countElement = document.getElementById('conversationCount');

        if (historyContainer && countElement) {
            const messageCount = historyContainer.children.length;
            countElement.textContent = messageCount;
        }
    }

    /**
     * 更新视频状态
     */
    updateVideoStatus(status) {
        const statusElement = document.getElementById('videoStatus');
        const sourceDisplay = document.getElementById('videoSourceDisplay');

        if (statusElement) {
            switch (status) {
                case 'connected':
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '已连接';
                    break;
                case 'disconnected':
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-red-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '未连接';
                    break;
                case 'error':
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '连接错误';
                    break;
                case 'no_service':
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-gray-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '服务未启动';
                    break;
                default:
                    statusElement.className = 'inline-block w-3 h-3 rounded-full bg-gray-500 mr-2';
                    if (sourceDisplay) sourceDisplay.textContent = '未知状态';
            }
        }
    }

    /**
     * 安排视频流重连
     */
    scheduleVideoReconnect() {
        if (this.reconnectAttempts >= this.maxReconnectAttempts) {
            console.log('视频流重连次数已达上限');
            return;
        }

        this.reconnectAttempts++;
        console.log(`${this.reconnectDelay / 1000}秒后尝试重连视频流 (第${this.reconnectAttempts}次)`);

        setTimeout(() => {
            if (!this.videoSocket || this.videoSocket.readyState === WebSocket.CLOSED) {
                this.connectVideoStream();
            }
        }, this.reconnectDelay);

        // 递增重连延迟
        this.reconnectDelay = Math.min(this.reconnectDelay * 1.5, 30000);
    }

    /**
     * 安排警报流重连
     */
    scheduleAlertReconnect() {
        setTimeout(() => {
            if (!this.alertSocket || this.alertSocket.readyState === WebSocket.CLOSED) {
                this.connectAlertStream();
            }
        }, 3000);
    }

    /**
     * 关闭所有连接
     */
    disconnect() {
        if (this.videoSocket) {
            this.videoSocket.close();
            this.videoSocket = null;
        }
        
        if (this.alertSocket) {
            this.alertSocket.close();
            this.alertSocket = null;
        }
    }

    /**
     * 重新连接所有WebSocket
     */
    reconnectAll() {
        this.disconnect();
        setTimeout(() => {
            this.initialize();
        }, 1000);
    }
}

// 创建全局WebSocket管理器实例
window.wsManager = new WebSocketManager();

// 页面加载完成后初始化WebSocket连接
document.addEventListener('DOMContentLoaded', function() {
    console.log('初始化WebSocket连接...');
    window.wsManager.initialize();
});

// 页面卸载时关闭连接
window.addEventListener('beforeunload', function() {
    if (window.wsManager) {
        window.wsManager.disconnect();
    }
});

// 网络状态变化时重连
window.addEventListener('online', function() {
    console.log('网络已连接，重新连接WebSocket...');
    if (window.wsManager) {
        window.wsManager.reconnectAll();
    }
});

window.addEventListener('offline', function() {
    console.log('网络已断开');
});
