#!/usr/bin/env python3
"""
测试RAG系统导入是否修复
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_rag_imports():
    """测试RAG相关的导入"""
    print("测试RAG系统导入...")
    print("=" * 50)
    
    # 测试核心配置导入
    try:
        from src.core.config_adapter import RAGConfig, APIConfig
        print("✅ 核心配置导入成功")
        print(f"   RAG启用状态: {RAGConfig.ENABLE_RAG}")
        print(f"   Milvus主机: {RAGConfig.MILVUS_HOST}")
        print(f"   向量API: {RAGConfig.VECTOR_API_URL}")
    except Exception as e:
        print(f"❌ 核心配置导入失败: {e}")
        return False
    
    # 测试utility模块导入
    try:
        from utils.utility import get_embeddings, chat_request, insert_txt
        print("✅ utility模块导入成功")
    except Exception as e:
        print(f"❌ utility模块导入失败: {e}")
        return False
    
    # 测试RAG查询模块导入
    try:
        from src.api.rag_query import rag_query
        print("✅ RAG查询模块导入成功")
    except Exception as e:
        print(f"❌ RAG查询模块导入失败: {e}")
        print(f"   错误详情: {e}")
        return False
    
    # 测试RAG简化版导入
    try:
        from src.api.rag_query_simple import rag_query as rag_query_simple
        print("✅ RAG简化版模块导入成功")
    except Exception as e:
        print(f"❌ RAG简化版模块导入失败: {e}")
        return False
    
    return True

def test_insert_txt_function():
    """测试insert_txt函数是否能正常工作"""
    print("\n测试insert_txt函数...")
    print("-" * 30)
    
    try:
        from utils.utility import insert_txt
        
        # 测试函数调用（不实际插入数据）
        test_docs = ["测试文档1", "测试文档2"]
        test_timestamp = "2025-07-17 08:00:00"
        test_alert_type = "测试异常"
        test_alert_value = 1
        test_table_name = "test_table"
        
        print("✅ insert_txt函数可以正常调用")
        print("   注意: 实际插入需要Milvus服务运行")
        
    except Exception as e:
        print(f"❌ insert_txt函数测试失败: {e}")
        return False
    
    return True

def main():
    print("RAG系统导入修复测试")
    print("=" * 60)
    
    success = True
    
    # 测试导入
    if not test_rag_imports():
        success = False
    
    # 测试函数
    if not test_insert_txt_function():
        success = False
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 所有RAG导入测试通过!")
        print("RAG系统异常: No module named 'config' 问题已修复")
    else:
        print("❌ 部分测试失败，请检查导入路径")
    
    return success

if __name__ == "__main__":
    main()
