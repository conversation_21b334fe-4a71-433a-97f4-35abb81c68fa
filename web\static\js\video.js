// WebSocket连接配置
const config = {
    videoUrl: `ws://${window.location.hostname}:16532/video_feed`,
    alertUrl: `ws://${window.location.hostname}:16532/alerts`,
    reconnectInterval: 3000
};

// 初始化WebSocket连接
let videoSocket, alertSocket;

// 视频流处理
function connectVideoStream() {
    videoSocket = new WebSocket(config.videoUrl);

    videoSocket.onopen = () => {
        console.log('视频流WebSocket已连接');
        updateVideoConnectionStatus(true);
    };

    videoSocket.onmessage = (event) => {
        const img = document.getElementById('videoFeed');
        const blob = new Blob([event.data], { type: 'image/jpeg' });
        img.src = URL.createObjectURL(blob);

        // 更新状态为已连接并接收数据
        updateVideoConnectionStatus(true, true);
    };

    videoSocket.onclose = () => {
        console.log('视频流WebSocket已断开');
        updateVideoConnectionStatus(false);
        setTimeout(connectVideoStream, config.reconnectInterval);
    };

    videoSocket.onerror = (error) => {
        console.error('视频流WebSocket错误:', error);
        updateVideoConnectionStatus(false);
    };
}

function updateVideoConnectionStatus(connected, receivingData = false) {
    const statusIndicator = document.getElementById('videoStatus');
    const statusText = document.getElementById('videoSourceDisplay');

    if (connected && receivingData) {
        statusIndicator.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
        statusText.textContent = '已连接';
        statusText.className = 'text-sm text-green-400';
    } else if (connected) {
        statusIndicator.className = 'inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2';
        statusText.textContent = '连接中';
        statusText.className = 'text-sm text-yellow-400';
    } else {
        statusIndicator.className = 'inline-block w-3 h-3 rounded-full bg-red-500 mr-2';
        statusText.textContent = '未连接';
        statusText.className = 'text-sm text-gray-400';
    }
}
