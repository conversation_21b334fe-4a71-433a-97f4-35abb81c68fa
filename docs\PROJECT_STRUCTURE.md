# 项目目录结构说明

## 优化后的目录结构

```
aiWatchdog0703/
├── main.py                     # 主启动文件
├── requirements.txt            # Python依赖包
├── README.md                   # 项目说明文档
│
├── configs/                    # 配置文件目录
│   ├── app_config.yaml         # 应用主配置文件
│   └── voice_config.yaml       # 语音助手配置文件
│
├── src/                        # 源代码目录
│   ├── __init__.py
│   ├── core/                   # 核心模块
│   │   ├── __init__.py
│   │   ├── config.py           # 原配置文件（向后兼容）
│   │   ├── config_loader.py    # YAML配置加载器
│   │   └── custom_models.json  # 自定义模型配置
│   ├── api/                    # API和RAG模块
│   │   ├── __init__.py
│   │   ├── rag_query.py        # RAG查询主模块
│   │   ├── rag_query_simple.py # RAG查询简化版
│   │   └── prompt.py           # 提示词模板
│   ├── voice/                  # 语音助手模块
│   │   ├── __init__.py
│   │   ├── voice_api.py        # 语音API接口
│   │   ├── voice_assistant.py  # 语音助手核心
│   │   ├── voice_controller.py # 语音控制器
│   │   ├── start_voice_assistant.py        # 语音助手启动脚本
│   │   └── start_enhanced_voice_assistant.py # 增强版启动脚本
│   ├── video/                  # 视频处理模块
│   │   ├── __init__.py
│   │   └── video_server.py     # 视频服务器主程序
│   └── models/                 # 模型管理模块
│       ├── __init__.py
│       ├── multi_modal_analyzer.py    # 多模态分析器
│       └── custom_model_manager.py    # 自定义模型管理器
│
├── utils/                      # 工具模块
│   ├── __init__.py
│   ├── alert_statistics.py     # 警报统计工具
│   └── utility.py              # 通用工具函数
│
├── web/                        # 前端代码
│   ├── templates/              # HTML模板
│   │   ├── index.html          # 主页面
│   │   ├── config.html         # 配置页面
│   │   ├── login.html          # 登录页面
│   │   ├── voice.html          # 语音页面
│   │   ├── voice_assistant.html # 语音助手页面
│   │   ├── assistant.html      # 助手页面
│   │   └── auto_play_rules.html # 自动播放规则页面
│   └── static/                 # 静态资源
│       ├── js/                 # JavaScript文件
│       │   ├── main.js         # 主要JS逻辑
│       │   ├── websocket.js    # WebSocket通信
│       │   ├── auth.js         # 认证相关
│       │   ├── storage.js      # 存储管理
│       │   └── config.js       # 配置管理
│       ├── css/                # CSS样式文件
│       │   ├── style.css       # 主样式
│       │   └── login.css       # 登录样式
│       └── assets/             # 其他静态资源
│           └── alert.mp3       # 警报音频
│
├── test/                       # 测试文件
│   ├── test.mp4                # 测试视频文件
│   ├── test1.mp4
│   ├── test2.mp4
│   ├── test3.mp4
│   ├── test_funasr.py          # FunASR测试脚本
│   ├── test_funasr_correct_protocol.py
│   ├── test_funasr_enhanced.py
│   ├── test_funasr_final.py
│   ├── test_funasr_parameters.py
│   ├── test_funasr_protocol.py
│   ├── debug_funasr.py         # FunASR调试脚本
│   ├── funasr_compatibility_test.py
│   └── validate_html.py        # HTML验证脚本
│
├── data/                       # 数据存储目录
│   ├── uploads/                # 上传文件存储
│   ├── video_warning/          # 警告视频存储
│   │   └── output.mp4
│   ├── voice/                  # 语音数据存储
│   │   ├── conversations/      # 对话记录
│   │   ├── temp/               # 临时音频文件
│   │   └── tts_cache/          # TTS缓存
│   ├── archive/                # 归档文件
│   ├── output_frame.jpg        # 输出帧图片
│   └── video_histroy_info.txt  # 视频历史信息
│
├── logs/                       # 日志文件目录
│   ├── app.log                 # 应用主日志
│   ├── code.log                # 代码执行日志
│   ├── voice_assistant.log     # 语音助手日志
│   └── temp_command_*.wav      # 临时命令音频文件
│
└── docs/                       # 文档目录
    ├── readme.md               # 原始说明文档
    ├── README_EN.md            # 英文说明文档
    ├── CONFIG_USAGE.md         # 配置使用说明
    ├── PROJECT_STRUCTURE.md    # 项目结构说明（本文件）
    ├── AI安全监控软件APP开发方案.md
    ├── FUNASR_FINAL_GUIDE.md   # FunASR最终指南
    ├── FUNASR_INTEGRATION_SUMMARY.md
    ├── FUNASR_SETUP_GUIDE.md
    ├── FUNASR_TROUBLESHOOTING.md
    ├── VOICE_ASSISTANT_ENHANCEMENT_SUMMARY.md
    └── VOICE_ASSISTANT_README.md
```

## 目录功能说明

### 核心目录

- **src/**: 所有源代码的根目录，按功能模块组织
- **configs/**: 配置文件，使用YAML格式，便于维护和版本控制
- **web/**: 前端相关文件，包括HTML模板和静态资源
- **utils/**: 通用工具函数和辅助模块

### 数据目录

- **data/**: 所有数据文件的统一存储位置
- **logs/**: 日志文件集中管理
- **test/**: 测试相关文件，包括测试数据和测试脚本
- **docs/**: 项目文档集中管理

### 配置管理

- 使用YAML配置文件替代硬编码配置
- 提供配置加载器统一管理配置
- 保留原config.py文件以确保向后兼容

### 模块化设计

- 按功能将代码分为不同模块
- 每个模块都有独立的__init__.py文件
- 清晰的导入路径和依赖关系

## 优化效果

1. **结构清晰**: 文件按功能分类，便于维护和扩展
2. **配置统一**: YAML配置文件便于管理和版本控制
3. **模块化**: 代码组织更加模块化，降低耦合度
4. **易于部署**: 标准化的目录结构便于容器化部署
5. **开发友好**: 清晰的目录结构便于新开发者理解项目
