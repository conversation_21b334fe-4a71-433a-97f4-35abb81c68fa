#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR协议探索脚本
功能：探索FunASR WebSocket服务器的协议格式
"""

import asyncio
import json
import logging
import wave
import base64
import struct

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装")

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# FunASR配置
FUNASR_WS_URL = "ws://192.168.3.95:10096/"
FUNASR_TIMEOUT = 10

class FunASRProtocolTester:
    """FunASR协议测试器"""
    
    def __init__(self):
        self.ws_url = FUNASR_WS_URL
        self.timeout = FUNASR_TIMEOUT
    
    async def test_different_formats(self):
        """测试不同的数据格式"""
        print("\n🔬 测试不同的数据格式...")
        
        # 创建测试音频数据
        sample_rate = 16000
        duration = 1  # 1秒
        frequency = 440  # A4音符
        
        import numpy as np
        t = np.linspace(0, duration, int(sample_rate * duration), False)
        audio_data = np.sin(frequency * 2 * np.pi * t)
        audio_int16 = (audio_data * 32767).astype(np.int16)
        
        formats_to_test = [
            ("原始字节数据", audio_int16.tobytes()),
            ("Base64编码", base64.b64encode(audio_int16.tobytes()).decode()),
            ("JSON格式", json.dumps({
                "audio": base64.b64encode(audio_int16.tobytes()).decode(),
                "sample_rate": sample_rate,
                "format": "pcm"
            })),
            ("FunASR标准格式", json.dumps({
                "mode": "offline",
                "chunk_size": [5, 10, 5],
                "chunk_interval": 10,
                "wav_name": "test",
                "is_speaking": True,
                "wav_format": "pcm",
                "audio": base64.b64encode(audio_int16.tobytes()).decode()
            })),
        ]
        
        for format_name, data in formats_to_test:
            print(f"\n📤 测试格式: {format_name}")
            await self._test_single_format(format_name, data)
    
    async def _test_single_format(self, format_name, data):
        """测试单个格式"""
        try:
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:
                
                # 发送数据
                if isinstance(data, str):
                    await websocket.send(data)
                else:
                    await websocket.send(data)
                
                print(f"✅ {format_name}: 数据已发送")
                
                # 接收响应
                responses = []
                try:
                    while True:
                        response = await asyncio.wait_for(
                            websocket.recv(),
                            timeout=3
                        )
                        responses.append(response)
                        print(f"📥 收到响应: {response}")
                        
                except asyncio.TimeoutError:
                    pass
                
                if not responses:
                    print(f"⚠️ {format_name}: 未收到响应")
                else:
                    print(f"✅ {format_name}: 收到 {len(responses)} 个响应")
                    
        except Exception as e:
            print(f"❌ {format_name}: 测试失败 - {e}")
    
    async def test_handshake(self):
        """测试握手协议"""
        print("\n🤝 测试握手协议...")
        
        handshake_messages = [
            {"type": "start", "data": "begin"},
            {"signal": "start"},
            {"mode": "offline"},
            {"action": "start_recognition"},
            "START",
            "start",
        ]
        
        for msg in handshake_messages:
            await self._test_handshake_message(msg)
    
    async def _test_handshake_message(self, message):
        """测试单个握手消息"""
        try:
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:
                
                # 发送握手消息
                if isinstance(message, dict):
                    msg_str = json.dumps(message)
                    await websocket.send(msg_str)
                    print(f"📤 发送握手: {msg_str}")
                else:
                    await websocket.send(str(message))
                    print(f"📤 发送握手: {message}")
                
                # 等待响应
                try:
                    response = await asyncio.wait_for(
                        websocket.recv(),
                        timeout=3
                    )
                    print(f"📥 握手响应: {response}")
                except asyncio.TimeoutError:
                    print("⚠️ 握手无响应")
                    
        except Exception as e:
            print(f"❌ 握手失败: {e}")
    
    async def test_streaming_protocol(self):
        """测试流式协议"""
        print("\n🌊 测试流式协议...")
        
        try:
            async with websockets.connect(
                self.ws_url,
                timeout=self.timeout
            ) as websocket:
                
                # 1. 发送开始信号
                start_msg = json.dumps({
                    "mode": "offline",
                    "chunk_size": [5, 10, 5],
                    "chunk_interval": 10,
                    "wav_name": "test_stream"
                })
                await websocket.send(start_msg)
                print(f"📤 发送开始信号: {start_msg}")
                
                # 等待确认
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=3)
                    print(f"📥 开始响应: {response}")
                except asyncio.TimeoutError:
                    print("⚠️ 开始信号无响应")
                
                # 2. 发送音频数据
                sample_rate = 16000
                duration = 2
                import numpy as np
                t = np.linspace(0, duration, int(sample_rate * duration), False)
                audio_data = np.sin(440 * 2 * np.pi * t)
                audio_int16 = (audio_data * 32767).astype(np.int16)
                
                audio_msg = json.dumps({
                    "audio": base64.b64encode(audio_int16.tobytes()).decode(),
                    "is_speaking": True,
                    "wav_format": "pcm"
                })
                await websocket.send(audio_msg)
                print("📤 发送音频数据")
                
                # 3. 发送结束信号
                end_msg = json.dumps({
                    "is_speaking": False
                })
                await websocket.send(end_msg)
                print("📤 发送结束信号")
                
                # 4. 接收所有响应
                responses = []
                try:
                    while True:
                        response = await asyncio.wait_for(websocket.recv(), timeout=5)
                        responses.append(response)
                        print(f"📥 流式响应: {response}")
                except asyncio.TimeoutError:
                    pass
                
                print(f"✅ 流式测试完成，收到 {len(responses)} 个响应")
                
        except Exception as e:
            print(f"❌ 流式测试失败: {e}")
    
    async def run_protocol_exploration(self):
        """运行协议探索"""
        print("\n" + "=" * 60)
        print("🔬 FunASR协议探索")
        print("=" * 60)
        print(f"服务器地址: {self.ws_url}")
        print("=" * 60)
        
        if not WEBSOCKETS_AVAILABLE:
            print("❌ websockets库不可用")
            return
        
        # 1. 测试握手协议
        await self.test_handshake()
        
        # 2. 测试不同数据格式
        await self.test_different_formats()
        
        # 3. 测试流式协议
        await self.test_streaming_protocol()
        
        print("\n" + "=" * 60)
        print("🎉 协议探索完成")
        print("=" * 60)

async def main():
    """主函数"""
    tester = FunASRProtocolTester()
    
    try:
        await tester.run_protocol_exploration()
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        # Python 3.6兼容性
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
