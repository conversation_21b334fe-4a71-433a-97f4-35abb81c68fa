# 语音助手配置文件
# 请根据需要修改以下配置

# 音频设备配置
AUDIO_DEVICE_INDEX = None  # None表示使用默认设备，或指定设备索引
SAMPLE_RATE = 16000
CHUNK_SIZE = 1024
CHANNELS = 1

# 唤醒词配置
WAKE_WORDS = ["小助手", "AI助手", "语音助手", "智能助手"]
WAKE_THRESHOLD = 0.7

# 录音配置
RECORD_TIMEOUT = 5  # 秒
SILENCE_THRESHOLD = 500
SILENCE_DURATION = 2  # 秒

# TTS配置
TTS_VOICE = "zh-CN-XiaoxiaoNeural"  # 中文女声
TTS_RATE = "+0%"
TTS_VOLUME = "+0%"

# FunASR配置
# 注意：FunASR服务器连接正常但识别结果为空，可能需要服务器端配置调整
# 设置为False使用Whisper，设置为True使用FunASR（需要服务器正确配置）
FUNASR_ENABLED = True  # 暂时禁用，直到服务器配置问题解决
FUNASR_WS_URL = "ws://192.168.3.95:10096/"  # FunASR WebSocket地址
FUNASR_TIMEOUT = 10  # WebSocket连接超时时间(秒)

# Whisper模型配置 (备用)
WHISPER_MODEL_SIZE = "base"  # tiny, base, small, medium, large
WHISPER_DEVICE = "cpu"  # cpu 或 cuda

# 日志配置
LOG_LEVEL = "INFO"
LOG_FILE = "voice_assistant.log"
