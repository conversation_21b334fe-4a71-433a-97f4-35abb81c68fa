"""配置文件
包含视频监控系统的所有可配置参数
"""

from typing import Dict, Any
import logging
import os
from datetime import datetime  # 时间
# 视频源配置
VIDEO_SOURCE = r'test/test.mp4'
# VIDEO_SOURCE = "rtsp://localhost:8554/mystream"

# 异常监测参数配置（新增全局配置）
# 移除顶部导入
# from video_server import MODEL_NAMES, current_model

# 当前选择的模型
current_model = {
    'name': 'intrusion',
    'timestamp': datetime.now().isoformat()
}

# 模型名称映射
MODEL_NAMES = {
    # 原有模型
    'general': '通用异常检测',
    'fight': '打架斗殴检测',
    'fall': '跌倒检测',
    'intrusion': '入侵检测',
    'helmet': '安全帽检测',
    'custom': '自定义模型',

    # 安全防护类
    'uniform': '工作服检测',
    'reflective': '反光衣识别',
    'chef-hat': '厨师帽识别',
    'mask': '口罩检测',

    # 行为检测类
    'smoking': '吸烟检测',
    'phone': '打电话检测',
    'fighting': '打架识别',
    'weapon': '持械识别',
    'sleep': '睡岗检测',
    'leave': '离岗检测',

    # 区域监控类
    'perimeter': '周界入侵检测',
    'fence': '电子围栏',
    'loitering': '徘徊检测',

    # 计数统计类
    'counting': '计数检测',
    'line-crossing': '越线计数',
    'overcrowd': '人员超限检测',

    # 识别检测类
    'face': '人脸识别',
    'license-plate': '车牌识别',
    'stranger': '陌生人告警',

    # 环境安全类
    'fire': '火焰烟火检测',
    'open-fire': '明火检测',
    'dust': '烟尘检测',
    'fire-exit': '消防通道阻塞识别',

    # 作业规范类
    'standard-operation': '规范作业检测',
    'uniform_0001': '工作服监测',
    'detection_mask_0001': '口罩检测',

    # 自定义模型
    'detection_mask_0002': '蓝色口罩检测',
    'detection_0001': '倒地检测'
}
# 默认使用当前模型名称作为警报名称
ALERT_NAMES = current_model["name"]

# 异常监测参数 - 支持多模型检测
# 默认使用当前模型，但可以通过API设置为多个模型列表
ANOMALY_SURVEILLANCE = [MODEL_NAMES[current_model["name"]]]

# 视频处理配置
class VideoConfig:
    VIDEO_INTERVAL = 1800  # 视频分段时长(秒)
    ANALYSIS_INTERVAL = 10  # 分析间隔(秒)
    BUFFER_DURATION = 11  # 滑窗分析时长（实际模型分析视频时长）#强制抽帧，每秒一帧率
    WS_RETRY_INTERVAL = 3  # WebSocket重连间隔(秒)
    MAX_WS_QUEUE = 100  # 消息队列最大容量
    JPEG_QUALITY = 70  # JPEG压缩质量

# API配置
class APIConfig:
    # 通义千问API配置
    # QWEN_API_KEY = os.getenv("DASHSCOPE_API_KEY")
    # QWEN_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    # QWEN_MODEL = "qwen2-vl-72b-instruct"
    QWEN_API_KEY = ""  # 本地服务不需要API Key
    QWEN_API_URL = "http://************:11434/v1/chat/completions"  # Ollama默认API地址
    # QWEN_MODEL = "qwen2.5vl:32b"  # 本地部署的模型名称
    QWEN_MODEL = "gemma3:4b"

    # Moonshot语言模型 API配置，这里可以换成其他语言模型或者本地部署的内容
    # MOONSHOT_API_KEY = os.getenv("DASHSCOPE_API_KEY")
    # MOONSHOT_API_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"
    # MOONSHOT_MODEL = "qwen-plus-latest"

    MOONSHOT_API_KEY = ""
    MOONSHOT_API_URL = "http://************:11434/v1/chat/completions"
    # MOONSHOT_MODEL = "deepseek-r1:32b"
    MOONSHOT_MODEL = "qwen3:4b"

    # MOONSHOT_API_KEY = ""
    # MOONSHOT_API_URL = "http://localhost:11434/v1/chat/completions"
    # # MOONSHOT_MODEL = "deepseek-r1:1.5b"
    # MOONSHOT_MODEL = "qwen3:0.6b"
    # API请求配置
    REQUEST_TIMEOUT = 60.0 # 请求超时时间（秒）
    TEMPERATURE = 0.5 # 温度
    TOP_P = 0.01 
    TOP_K = 20
    REPETITION_PENALTY = 1.05

# RAG系统配置
class RAGConfig:
    # 知识库配置
    ENABLE_RAG = True
    MILVUS_HOST = "************"
    MILVUS_PORT = "19530"
    MILVUS_USER = "root"
    MILVUS_PASSWORD = "Milvus"
    COLLECTION_NAME = "table_test_table"
    EMBEDDING_DIM = 768
    VECTOR_API_URL = "http://************:11434/api/embeddings"
    HISTORY_FILE = "data/video_histroy_info.txt"  # 如果不启用RAG，历史记录将保存在该文件中


# 存档配置
ARCHIVE_DIR = "data/archive"

# 视频警告存储目录
VIDEO_WARNING_DIR = "data/video_warning"

# 服务器配置
class ServerConfig:
    HOST = "0.0.0.0"
    PORT = 16532
    RELOAD = True
    WORKERS = 1

# 日志配置
LOG_CONFIG = {
    'level': logging.INFO,
    'format': '%(asctime)s - %(levelname)s - %(message)s',
    'handlers': [
        {'type': 'file', 'filename': 'logs/code.log'},
        {'type': 'stream'}
    ]
}


def update_config(args: Dict[str, Any]):
    """动态更新配置参数"""
    global VIDEO_SOURCE, ANOMALY_SURVEILLANCE, ALERT_NAMES
    
    # 更新视频源
    if 'video_source' in args:
        VIDEO_SOURCE = args['video_source']
        
    # 更新异常监测参数
    if 'anomaly_surveillance' in args:
        ANOMALY_SURVEILLANCE = args['anomaly_surveillance']
        
    # 更新警报名称
    if 'alert_names' in args:
        ALERT_NAMES = args['alert_names']
        
    # 更新视频配置
    for key in ['video_interval', 'analysis_interval', 'buffer_duration',
               'ws_retry_interval', 'max_ws_queue', 'jpeg_quality']:
        if key in args:
            setattr(VideoConfig, key.upper(), args[key])

    # 更新视频存储路径
    if 'video_storage_path' in args:
        global VIDEO_WARNING_DIR
        VIDEO_WARNING_DIR = args['video_storage_path']
            
    # 更新API配置
    for key in ['qwen_api_key', 'qwen_api_url', 'qwen_model',
               'moonshot_api_key', 'moonshot_api_url', 'moonshot_model',
               'request_timeout', 'temperature', 'top_p', 'top_k',
               'repetition_penalty']:
        if key in args:
            setattr(APIConfig, key.upper(), args[key])
            
    # 更新RAG配置
    for key in ['enable_rag', 'vector_api_url', 'history_file',
               'history_save_interval']:
        if key in args:
            setattr(RAGConfig, key.upper(), args[key])
