#!/usr/bin/env python3
"""
测试request配置是否修复
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_api_config():
    """测试API配置"""
    print("测试API配置...")
    print("=" * 40)
    
    try:
        from src.core.config_adapter import APIConfig
        
        # 测试所有API配置属性
        print(f"✅ QWEN_API_URL: {APIConfig.QWEN_API_URL}")
        print(f"✅ QWEN_MODEL: {APIConfig.QWEN_MODEL}")
        print(f"✅ MOONSHOT_API_URL: {APIConfig.MOONSHOT_API_URL}")
        print(f"✅ MOONSHOT_MODEL: {APIConfig.MOONSHOT_MODEL}")
        
        # 测试request配置（这是之前出错的地方）
        print(f"✅ REQUEST_TIMEOUT: {APIConfig.REQUEST_TIMEOUT}")
        print(f"✅ TEMPERATURE: {APIConfig.TEMPERATURE}")
        print(f"✅ TOP_P: {APIConfig.TOP_P}")
        print(f"✅ TOP_K: {APIConfig.TOP_K}")
        print(f"✅ REPETITION_PENALTY: {APIConfig.REPETITION_PENALTY}")
        
        return True
        
    except Exception as e:
        print(f"❌ API配置测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_utility_functions():
    """测试utility.py中的函数"""
    print("\n测试utility.py函数...")
    print("-" * 30)
    
    try:
        from utils.utility import APIConfig
        
        # 测试APIConfig访问（这是出错的地方）
        timeout = APIConfig.REQUEST_TIMEOUT
        print(f"✅ utility.py可以访问REQUEST_TIMEOUT: {timeout}")
        
        # 测试其他配置
        temperature = APIConfig.TEMPERATURE
        print(f"✅ utility.py可以访问TEMPERATURE: {temperature}")
        
        return True
        
    except Exception as e:
        print(f"❌ utility.py测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_yaml_config_loading():
    """测试YAML配置加载"""
    print("\n测试YAML配置加载...")
    print("-" * 30)
    
    try:
        from src.core.config_loader import config_loader
        
        # 测试API配置加载
        api_config = config_loader.get_api_config()
        print(f"✅ API配置加载成功")
        
        # 检查request部分
        if 'request' in api_config:
            request_config = api_config['request']
            print(f"✅ request配置存在: {list(request_config.keys())}")
            print(f"   timeout: {request_config.get('timeout')}")
            print(f"   temperature: {request_config.get('temperature')}")
        else:
            print("❌ request配置不存在")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ YAML配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Request配置修复测试")
    print("=" * 50)
    
    test1 = test_yaml_config_loading()
    test2 = test_api_config()
    test3 = test_utility_functions()
    
    print("\n" + "=" * 50)
    if all([test1, test2, test3]):
        print("🎉 所有测试通过!")
        print("\n✅ request配置已修复")
        print("✅ APIConfig.REQUEST_TIMEOUT 可以正常访问")
        print("✅ 分析失败: 'request' 错误应该已解决")
        
        print("\n现在可以重新启动系统:")
        print("python main.py")
        
    else:
        print("❌ 部分测试失败")
        print("请检查configs/app_config.yaml中的request配置")

if __name__ == "__main__":
    main()
