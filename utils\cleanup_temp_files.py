#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
临时文件清理工具
自动清理项目中的临时文件和缓存文件
"""

import os
import shutil
import glob
from pathlib import Path
import time
import logging
from datetime import datetime, timedelta

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class TempFileCleaner:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.cleanup_rules = self._define_cleanup_rules()
        
    def _define_cleanup_rules(self):
        """定义清理规则"""
        return {
            # 临时音频文件
            'temp_audio': {
                'patterns': ['temp_command_*.wav', 'temp_*.wav'],
                'directories': ['.', 'temp', 'voice'],
                'max_age_hours': 24,  # 24小时后清理
                'description': '临时音频文件'
            },
            
            # Python缓存文件
            'python_cache': {
                'patterns': ['*.pyc', '*.pyo'],
                'directories': ['__pycache__', 'src/__pycache__', 'utils/__pycache__'],
                'max_age_hours': 0,  # 立即清理
                'description': 'Python缓存文件'
            },
            
            # 日志文件（旧的）
            'old_logs': {
                'patterns': ['*.log.1', '*.log.2', '*.log.old'],
                'directories': ['logs'],
                'max_age_hours': 168,  # 7天后清理
                'description': '旧日志文件'
            },
            
            # TTS缓存文件
            'tts_cache': {
                'patterns': ['tts_*.mp3'],
                'directories': ['voice', 'voice/tts_cache'],
                'max_age_hours': 72,  # 3天后清理
                'description': 'TTS缓存文件'
            },
            
            # 输出帧文件
            'output_frames': {
                'patterns': ['output_frame*.jpg', 'frame_*.jpg'],
                'directories': ['.', 'data'],
                'max_age_hours': 48,  # 2天后清理
                'description': '输出帧文件'
            }
        }
    
    def get_file_age_hours(self, file_path):
        """获取文件年龄（小时）"""
        try:
            file_time = os.path.getmtime(file_path)
            current_time = time.time()
            age_seconds = current_time - file_time
            return age_seconds / 3600  # 转换为小时
        except OSError:
            return 0
    
    def find_files_to_clean(self, rule_config):
        """查找需要清理的文件"""
        files_to_clean = []
        
        for directory in rule_config['directories']:
            dir_path = self.project_root / directory
            if not dir_path.exists():
                continue
                
            for pattern in rule_config['patterns']:
                files = list(dir_path.glob(pattern))
                for file_path in files:
                    if file_path.is_file():
                        age_hours = self.get_file_age_hours(file_path)
                        if age_hours >= rule_config['max_age_hours']:
                            files_to_clean.append({
                                'path': file_path,
                                'age_hours': age_hours,
                                'size_bytes': file_path.stat().st_size
                            })
        
        return files_to_clean
    
    def format_size(self, size_bytes):
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024.0:
                return f"{size_bytes:.1f} {unit}"
            size_bytes /= 1024.0
        return f"{size_bytes:.1f} TB"
    
    def clean_files(self, dry_run=False):
        """清理文件"""
        logger.info(f"开始清理临时文件 (项目根目录: {self.project_root})")
        
        total_files = 0
        total_size = 0
        
        for rule_name, rule_config in self.cleanup_rules.items():
            logger.info(f"\n处理规则: {rule_name} - {rule_config['description']}")
            logger.info(f"  最大年龄: {rule_config['max_age_hours']} 小时")
            
            files_to_clean = self.find_files_to_clean(rule_config)
            
            if not files_to_clean:
                logger.info(f"  未找到需要清理的文件")
                continue
            
            rule_files = len(files_to_clean)
            rule_size = sum(f['size_bytes'] for f in files_to_clean)
            
            logger.info(f"  找到 {rule_files} 个文件，总大小: {self.format_size(rule_size)}")
            
            for file_info in files_to_clean:
                file_path = file_info['path']
                age_hours = file_info['age_hours']
                size_bytes = file_info['size_bytes']
                
                if dry_run:
                    logger.info(f"    [预览] 将删除: {file_path} (年龄: {age_hours:.1f}h, 大小: {self.format_size(size_bytes)})")
                else:
                    try:
                        file_path.unlink()
                        logger.info(f"    删除: {file_path} (年龄: {age_hours:.1f}h, 大小: {self.format_size(size_bytes)})")
                        total_files += 1
                        total_size += size_bytes
                    except Exception as e:
                        logger.error(f"    删除失败: {file_path}, 错误: {e}")
        
        if dry_run:
            logger.info(f"\n预览完成")
        else:
            logger.info(f"\n清理完成，共删除 {total_files} 个文件，释放空间: {self.format_size(total_size)}")
    
    def clean_empty_directories(self, dry_run=False):
        """清理空目录"""
        logger.info("清理空目录...")
        
        directories_to_check = [
            'temp',
            'voice/tts_cache',
            '__pycache__',
            'src/__pycache__',
            'utils/__pycache__'
        ]
        
        cleaned_dirs = 0
        
        for dir_name in directories_to_check:
            dir_path = self.project_root / dir_name
            if dir_path.exists() and dir_path.is_dir():
                try:
                    # 检查目录是否为空
                    if not any(dir_path.iterdir()):
                        if dry_run:
                            logger.info(f"  [预览] 将删除空目录: {dir_path}")
                        else:
                            dir_path.rmdir()
                            logger.info(f"  删除空目录: {dir_path}")
                            cleaned_dirs += 1
                except OSError as e:
                    logger.debug(f"  无法删除目录 {dir_path}: {e}")
        
        if not dry_run:
            logger.info(f"清理了 {cleaned_dirs} 个空目录")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        report_path = self.project_root / "logs" / "cleanup_report.txt"
        report_path.parent.mkdir(exist_ok=True)
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write(f"临时文件清理报告\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"项目根目录: {self.project_root}\n\n")
            
            f.write("清理规则:\n")
            for rule_name, rule_config in self.cleanup_rules.items():
                f.write(f"  {rule_name}: {rule_config['description']}\n")
                f.write(f"    模式: {', '.join(rule_config['patterns'])}\n")
                f.write(f"    目录: {', '.join(rule_config['directories'])}\n")
                f.write(f"    最大年龄: {rule_config['max_age_hours']} 小时\n\n")
            
            # 统计当前文件
            f.write("当前文件统计:\n")
            for rule_name, rule_config in self.cleanup_rules.items():
                files = self.find_files_to_clean(rule_config)
                if files:
                    total_size = sum(f['size_bytes'] for f in files)
                    f.write(f"  {rule_name}: {len(files)} 个文件, {self.format_size(total_size)}\n")
                else:
                    f.write(f"  {rule_name}: 无文件\n")
        
        logger.info(f"生成清理报告: {report_path}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='临时文件清理工具')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际删除文件')
    parser.add_argument('--clean-dirs', action='store_true', help='清理空目录')
    parser.add_argument('--report', action='store_true', help='生成清理报告')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    
    args = parser.parse_args()
    
    cleaner = TempFileCleaner(args.project_root)
    
    if args.dry_run:
        logger.info("=== 预览模式 ===")
        cleaner.clean_files(dry_run=True)
        if args.clean_dirs:
            cleaner.clean_empty_directories(dry_run=True)
    else:
        cleaner.clean_files(dry_run=False)
        if args.clean_dirs:
            cleaner.clean_empty_directories(dry_run=False)
    
    if args.report:
        cleaner.generate_cleanup_report()

if __name__ == "__main__":
    main()
