#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试语音识别功能，验证模拟识别是否已被禁用
"""

import asyncio
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.voice.voice_assistant import SpeechRecognizer, VoiceConfig

async def test_speech_recognition():
    """测试语音识别功能"""
    print("🎤 测试语音识别功能")
    print("=" * 50)
    
    # 显示配置状态
    print(f"模拟功能启用状态: {VoiceConfig.ENABLE_MOCK_FEATURES}")
    print(f"FunASR WebSocket地址: {VoiceConfig.FUNASR_WS_URL}")
    
    # 创建语音识别器
    recognizer = SpeechRecognizer()
    
    print(f"\n识别器状态:")
    print(f"  FunASR可用: {recognizer.use_funasr}")
    print(f"  Whisper可用: {recognizer.whisper_model is not None}")
    
    # 测试识别功能（使用不存在的音频文件）
    print(f"\n测试语音识别（无音频文件）:")
    result = await recognizer.recognize("nonexistent_audio.wav")
    
    if result is None:
        print("✅ 正确：未返回模拟识别结果")
    else:
        print(f"❌ 错误：返回了识别结果: {result}")
    
    # 测试模拟识别方法
    print(f"\n测试模拟识别方法:")
    mock_result = await recognizer._mock_recognize("test.wav")
    
    if mock_result is None:
        print("✅ 正确：模拟识别已禁用")
    else:
        print(f"❌ 错误：模拟识别返回了结果: {mock_result}")

if __name__ == "__main__":
    print("🔧 语音识别测试工具")
    print("检查模拟语音识别是否已被正确禁用\n")

    try:
        # Python 3.6兼容性
        try:
            asyncio.run(test_speech_recognition())
        except AttributeError:
            loop = asyncio.get_event_loop()
            try:
                loop.run_until_complete(test_speech_recognition())
            finally:
                loop.close()
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()

    print("\n测试完成")
