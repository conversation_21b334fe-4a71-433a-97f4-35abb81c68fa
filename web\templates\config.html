<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>系统配置 - AI安全监控系统</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
      min-height: 100vh;
    }
    .config-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      border: 1px solid rgba(255, 255, 255, 0.1);
      transition: all 0.3s ease;
    }
    .config-card:hover {
      transform: translateY(-2px);
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
    }
    .upload-area {
      border: 2px dashed #4f46e5;
      transition: all 0.3s ease;
    }
    .upload-area.dragover {
      border-color: #7c3aed;
      background-color: rgba(124, 58, 237, 0.1);
    }
    .notification {
      position: fixed;
      top: 20px;
      right: 20px;
      z-index: 1000;
      transform: translateX(400px);
      transition: transform 0.3s ease;
    }
    .notification.show {
      transform: translateX(0);
    }
  </style>
</head>
<body class="text-gray-200">
  <!-- 通知组件 -->
  <div id="notification" class="notification">
    <div class="bg-green-500 text-white px-6 py-3 rounded-lg shadow-lg">
      <div class="flex items-center">
        <i class="fas fa-check-circle mr-2"></i>
        <span id="notificationText">操作成功</span>
      </div>
    </div>
  </div>

  <div class="container mx-auto px-4 py-8">
    <!-- 页面头部 -->
    <header class="mb-8">
      <div class="flex items-center justify-between">
        <div>
          <h1 class="text-3xl font-bold mb-2 bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
            系统配置
          </h1>
          <p class="text-gray-400">配置语音系统、视频流、音频参数和高级设置</p>
        </div>
        <a href="/" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-lg transition">
          <i class="fas fa-arrow-left mr-2"></i>返回主页
        </a>
      </div>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 语音系统配置 -->
      <div class="config-card rounded-xl p-6">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-microphone text-blue-400 mr-2"></i>
          语音系统配置
        </h2>

        <!-- 语音助手状态 -->
        <div class="mb-6">
          <label class="block text-sm font-medium mb-2">语音助手状态</label>
          <div class="bg-gray-700 rounded-md px-3 py-2 mb-3">
            <div class="flex items-center justify-between">
              <span id="voiceAssistantStatus" class="text-green-400">未启动</span>
              <span id="voiceStatusBadge" class="text-xs px-2 py-1 rounded bg-gray-600 text-white">离线</span>
            </div>
          </div>
        </div>

        <!-- 唤醒词配置 -->
        <div class="mb-6">
          <label class="block text-sm font-medium mb-2">唤醒词设置</label>
          <div class="space-y-2">
            <input type="text" id="wakeWord1" value="小凯小凯"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-sm"
                   placeholder="唤醒词1">
            <input type="text" id="wakeWord2" value="你好小凯"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-sm"
                   placeholder="唤醒词2">
            <input type="text" id="wakeWord3" value="小凯同学"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-sm"
                   placeholder="唤醒词3">
            <input type="text" id="wakeWord4" value="小凯助手"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2 text-sm"
                   placeholder="唤醒词4">
          </div>
          <p class="text-xs text-gray-400 mt-1">设置语音唤醒词，支持多个唤醒词</p>
        </div>

        <!-- 语音识别配置 -->
        <div class="mb-6">
          <label class="block text-sm font-medium mb-3">语音识别引擎</label>
          <div class="space-y-3">
            <label class="flex items-center p-3 bg-gray-700 rounded-md hover:bg-gray-600 transition cursor-pointer">
              <input type="radio" name="asrEngine" value="whisper" class="mr-3" checked>
              <div class="flex-1">
                <div class="font-medium">OpenAI Whisper</div>
                <div class="text-xs text-gray-400">高精度多语言语音识别</div>
              </div>
            </label>
            <label class="flex items-center p-3 bg-gray-700 rounded-md hover:bg-gray-600 transition cursor-pointer">
              <input type="radio" name="asrEngine" value="funasr" class="mr-3">
              <div class="flex-1">
                <div class="font-medium">FunASR</div>
                <div class="text-xs text-gray-400">阿里达摩院语音识别，中文优化</div>
              </div>
            </label>
          </div>
        </div>

        <!-- 语音参数 -->
        <div class="mb-6">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">唤醒阈值</label>
              <input type="range" id="wakeThreshold" min="0.1" max="1.0" step="0.1" value="0.6"
                     class="w-full">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>0.1</span>
                <span id="wakeThresholdValue">0.6</span>
                <span>1.0</span>
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">录音超时 (秒)</label>
              <input type="number" id="recordTimeout" min="5" max="30" value="10"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
            </div>
          </div>
        </div>

        <button id="applyVoiceConfig" class="w-full bg-blue-600 hover:bg-blue-700 rounded-md py-2 transition">
          <i class="fas fa-save mr-2"></i>保存语音配置
        </button>
      </div>

      <!-- 视频流配置 -->
      <div class="config-card rounded-xl p-6">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-video text-green-400 mr-2"></i>
          视频流配置
        </h2>

        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-2">视频源类型</label>
            <select id="videoSourceType" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <option value="camera">摄像头</option>
              <option value="rtsp">RTSP流</option>
              <option value="file">视频文件</option>
            </select>
            <div class="text-xs text-gray-400 mt-1">选择视频输入源类型</div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">视频源地址</label>
            <input type="text" id="videoSourceUrl"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="例如: 0 (摄像头) 或 rtsp://192.168.1.100:554/stream">
            <div class="text-xs text-gray-400 mt-1">摄像头索引、RTSP地址或文件路径</div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">视频分辨率</label>
              <select id="videoResolution" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
                <option value="640x480">640x480</option>
                <option value="1280x720" selected>1280x720</option>
                <option value="1920x1080">1920x1080</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">帧率 (FPS)</label>
              <input type="number" id="videoFps" min="1" max="60" value="30"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">视频编码格式</label>
            <select id="videoCodec" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <option value="h264" selected>H.264</option>
              <option value="h265">H.265</option>
              <option value="mjpeg">MJPEG</option>
            </select>
            <div class="text-xs text-gray-400 mt-1">视频编码格式，影响压缩率和兼容性</div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">视频存储路径</label>
            <input type="text" id="videoStoragePath"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="默认: ./videos">
            <div class="text-xs text-gray-400 mt-1">视频文件的存储目录路径</div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">视频质量 (JPEG压缩质量)</label>
            <input type="range" id="videoQuality" min="1" max="100" value="70"
                   class="w-full">
            <div class="flex justify-between text-xs text-gray-400 mt-1">
              <span>低质量</span>
              <span id="videoQualityValue">70</span>
              <span>高质量</span>
            </div>
            <div class="text-xs text-gray-400 mt-1">JPEG压缩质量，影响文件大小和画质 (yaml: jpeg_quality: 70)</div>
          </div>

          <!-- 视频处理配置 -->
          <div class="grid grid-cols-2 gap-4 mt-4">
            <div>
              <label class="block text-sm font-medium mb-2">视频分段时长 (秒)</label>
              <input type="number" id="videoInterval" min="60" max="7200" value="1800"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">视频分段时长 (yaml: video_interval: 1800)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">分析间隔 (秒)</label>
              <input type="number" id="analysisInterval" min="1" max="300" value="10"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">分析间隔时间 (yaml: analysis_interval: 10)</div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4 mt-4">
            <div>
              <label class="block text-sm font-medium mb-2">缓冲时长 (秒)</label>
              <input type="number" id="bufferDuration" min="5" max="60" value="11"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">滑窗分析时长 (yaml: buffer_duration: 11)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">WebSocket重连间隔 (秒)</label>
              <input type="number" id="wsRetryInterval" min="1" max="30" value="3"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">WebSocket重连间隔 (yaml: ws_retry_interval: 3)</div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">消息队列最大容量</label>
            <input type="number" id="maxWsQueue" min="10" max="1000" value="100"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
            <div class="text-xs text-gray-400 mt-1">消息队列最大容量 (yaml: max_ws_queue: 100)</div>
          </div>
        </div>

        <button id="applyVideoConfig" class="w-full mt-6 bg-green-600 hover:bg-green-700 rounded-md py-2 transition">
          <i class="fas fa-save mr-2"></i>保存视频配置
        </button>
      </div>

      <!-- 音频配置 -->
      <div class="config-card rounded-xl p-6">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-volume-up text-orange-400 mr-2"></i>
          音频配置
        </h2>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">采样率 (Hz)</label>
              <select id="audioSampleRate" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
                <option value="8000">8000</option>
                <option value="16000" selected>16000</option>
                <option value="22050">22050</option>
                <option value="44100">44100</option>
                <option value="48000">48000</option>
              </select>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">声道数</label>
              <select id="audioChannels" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
                <option value="1" selected>单声道</option>
                <option value="2">立体声</option>
              </select>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">音频块大小</label>
              <input type="number" id="audioChunkSize" min="512" max="8192" value="1024"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">音频处理的缓冲区大小</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">音频格式</label>
              <select id="audioFormat" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
                <option value="int16" selected>16位整数</option>
                <option value="int32">32位整数</option>
                <option value="float32">32位浮点</option>
              </select>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">TTS语音</label>
            <select id="ttsVoice" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <option value="zh-CN-XiaoxiaoNeural" selected>小晓 (女声)</option>
              <option value="zh-CN-YunxiNeural">云希 (男声)</option>
              <option value="zh-CN-YunyangNeural">云扬 (男声)</option>
              <option value="zh-CN-XiaoyiNeural">晓伊 (女声)</option>
            </select>
            <div class="text-xs text-gray-400 mt-1">文本转语音的声音选择</div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">语音速度</label>
              <input type="range" id="ttsRate" min="-50" max="50" value="0"
                     class="w-full">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>慢</span>
                <span id="ttsRateValue">正常</span>
                <span>快</span>
              </div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">语音音量</label>
              <input type="range" id="ttsVolume" min="-50" max="50" value="0"
                     class="w-full">
              <div class="flex justify-between text-xs text-gray-400 mt-1">
                <span>小</span>
                <span id="ttsVolumeValue">正常</span>
                <span>大</span>
              </div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">语音文件存储路径</label>
            <input type="text" id="voiceStoragePath" value="voice"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="默认: voice">
            <div class="text-xs text-gray-400 mt-1">TTS生成的音频文件存储目录</div>
          </div>
        </div>

        <button id="applyAudioConfig" class="w-full mt-6 bg-orange-600 hover:bg-orange-700 rounded-md py-2 transition">
          <i class="fas fa-save mr-2"></i>保存音频配置
        </button>
      </div>

      <!-- 服务器配置 -->
      <div class="config-card rounded-xl p-6">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-server text-blue-400 mr-2"></i>
          服务器配置
        </h2>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">服务器主机地址</label>
              <input type="text" id="serverHost" value="0.0.0.0"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="0.0.0.0">
              <div class="text-xs text-gray-400 mt-1">服务器监听地址 (yaml: host: "0.0.0.0")</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">服务器端口</label>
              <input type="number" id="serverPort" min="1000" max="65535" value="16532"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">服务器端口号 (yaml: port: 16532)</div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">工作进程数</label>
              <input type="number" id="serverWorkers" min="1" max="16" value="1"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">服务器工作进程数 (yaml: workers: 1)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">开发模式</label>
              <select id="serverReload" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
                <option value="true" selected>启用</option>
                <option value="false">禁用</option>
              </select>
              <div class="text-xs text-gray-400 mt-1">热重载模式 (yaml: reload: true)</div>
            </div>
          </div>
        </div>

        <button id="applyServerConfig" class="w-full mt-6 bg-blue-600 hover:bg-blue-700 rounded-md py-2 transition">
          <i class="fas fa-save mr-2"></i>保存服务器配置
        </button>
      </div>

      <!-- 存储路径配置 -->
      <div class="config-card rounded-xl p-6">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-folder text-yellow-400 mr-2"></i>
          存储路径配置
        </h2>

        <div class="space-y-4">
          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">视频归档目录</label>
              <input type="text" id="archiveDir" value="data/archive"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="data/archive">
              <div class="text-xs text-gray-400 mt-1">视频归档存储目录 (yaml: archive_dir)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">预警视频目录</label>
              <input type="text" id="videoWarningDir" value="data/video_warning"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="data/video_warning">
              <div class="text-xs text-gray-400 mt-1">预警视频存储目录 (yaml: video_warning_dir)</div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">上传文件目录</label>
              <input type="text" id="uploadsDir" value="data/uploads"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="data/uploads">
              <div class="text-xs text-gray-400 mt-1">上传文件存储目录 (yaml: uploads_dir)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">语音文件目录</label>
              <input type="text" id="voiceDir" value="data/voice"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="data/voice">
              <div class="text-xs text-gray-400 mt-1">语音文件存储目录 (yaml: voice_dir)</div>
            </div>
          </div>
        </div>

        <button id="applyStorageConfig" class="w-full mt-6 bg-yellow-600 hover:bg-yellow-700 rounded-md py-2 transition">
          <i class="fas fa-save mr-2"></i>保存存储配置
        </button>
      </div>

      <!-- API配置 -->
      <div class="config-card rounded-xl p-6">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-server text-purple-400 mr-2"></i>
          API配置
        </h2>
        
        <div class="space-y-4">
          <div>
            <label class="block text-sm font-medium mb-2">Qwen API地址</label>
            <input type="text" id="qwenApiUrl" value="http://************:11434/v1/chat/completions"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="http://************:11434/v1/chat/completions">
            <div class="text-xs text-gray-400 mt-1">Qwen API服务地址 (yaml: qwen.api_url)</div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">Qwen模型</label>
            <input type="text" id="qwenModel" value="gemma3:4b"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="gemma3:4b">
            <div class="text-xs text-gray-400 mt-1">Qwen使用的模型名称 (yaml: qwen.model)</div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">Moonshot API地址</label>
            <input type="text" id="moonshotApiUrl" value="http://************:11434/v1/chat/completions"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="http://************:11434/v1/chat/completions">
            <div class="text-xs text-gray-400 mt-1">Moonshot API服务地址 (yaml: moonshot.api_url)</div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">Moonshot模型</label>
            <input type="text" id="moonshotModel" value="qwen3:4b"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="qwen3:4b">
            <div class="text-xs text-gray-400 mt-1">Moonshot使用的模型名称 (yaml: moonshot.model)</div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">请求超时 (秒)</label>
              <input type="number" id="requestTimeout" min="10" max="300" value="60"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">API请求超时时间 (yaml: timeout: 60.0)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">温度参数</label>
              <input type="number" id="temperature" min="0" max="2" step="0.1" value="0.5"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">模型创造性参数 (yaml: temperature: 0.5)</div>
            </div>
          </div>

          <div class="grid grid-cols-3 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">Top P</label>
              <input type="number" id="topP" min="0" max="1" step="0.01" value="0.01"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">核采样参数 (yaml: top_p: 0.01)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">Top K</label>
              <input type="number" id="topK" min="1" max="100" value="20"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">候选词数量 (yaml: top_k: 20)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">重复惩罚</label>
              <input type="number" id="repetitionPenalty" min="1" max="2" step="0.01" value="1.05"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
              <div class="text-xs text-gray-400 mt-1">重复惩罚系数 (yaml: repetition_penalty: 1.05)</div>
            </div>
          </div>
        </div>

        <button id="applyApiConfig" class="w-full mt-6 bg-purple-600 hover:bg-purple-700 rounded-md py-2 transition">
          <i class="fas fa-save mr-2"></i>保存配置
        </button>
      </div>

      <!-- RAG配置 -->
      <div class="config-card rounded-xl p-6">
        <h2 class="text-xl font-semibold mb-4 flex items-center">
          <i class="fas fa-database text-yellow-400 mr-2"></i>
          RAG配置
        </h2>
        
        <div class="space-y-4">
          <div class="flex items-center">
            <input type="checkbox" id="enableRag" class="mr-2" checked>
            <label for="enableRag">启用RAG系统</label>
            <div class="text-xs text-gray-400 ml-2">(yaml: enable_rag: true)</div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">Milvus主机</label>
              <input type="text" id="milvusHost" value="************"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="************">
              <div class="text-xs text-gray-400 mt-1">Milvus数据库主机地址 (yaml: milvus.host)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">Milvus端口</label>
              <input type="text" id="milvusPort" value="19530"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="19530">
              <div class="text-xs text-gray-400 mt-1">Milvus数据库端口 (yaml: milvus.port)</div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">用户名</label>
              <input type="text" id="milvusUser" value="root"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="root">
              <div class="text-xs text-gray-400 mt-1">Milvus用户名 (yaml: milvus.user)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">密码</label>
              <input type="password" id="milvusPassword" value="Milvus"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="Milvus">
              <div class="text-xs text-gray-400 mt-1">Milvus密码 (yaml: milvus.password)</div>
            </div>
          </div>

          <div class="grid grid-cols-2 gap-4">
            <div>
              <label class="block text-sm font-medium mb-2">集合名称</label>
              <input type="text" id="collectionName" value="table_test_table"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="table_test_table">
              <div class="text-xs text-gray-400 mt-1">Milvus集合名称 (yaml: milvus.collection_name)</div>
            </div>
            <div>
              <label class="block text-sm font-medium mb-2">向量维度</label>
              <input type="number" id="embeddingDim" value="768"
                     class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                     placeholder="768">
              <div class="text-xs text-gray-400 mt-1">向量嵌入维度 (yaml: milvus.embedding_dim)</div>
            </div>
          </div>

          <div>
            <label class="block text-sm font-medium mb-2">向量API地址</label>
            <input type="text" id="vectorApiUrl" value="http://************:11434/api/embeddings"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="http://************:11434/api/embeddings">
            <div class="text-xs text-gray-400 mt-1">向量化API服务地址 (yaml: vector_api_url)</div>
          </div>
          
          <div>
            <label class="block text-sm font-medium mb-2">历史文件</label>
            <input type="text" id="historyFile" value="video_histroy_info.txt"
                   class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2"
                   placeholder="video_histroy_info.txt">
            <div class="text-xs text-gray-400 mt-1">历史记录文件路径 (yaml: history_file)</div>
          </div>
        </div>

        <button id="applyRagConfig" class="w-full mt-6 bg-yellow-600 hover:bg-yellow-700 rounded-md py-2 transition">
          <i class="fas fa-save mr-2"></i>保存配置
        </button>
      </div>
    </div>

    <!-- 操作按钮 -->
    <div class="mt-8 text-center">
      <button id="loadConfig" class="px-6 py-3 bg-gray-600 hover:bg-gray-700 rounded-md mr-4 transition">
        <i class="fas fa-refresh mr-2"></i>重新加载配置
      </button>
      <button id="resetConfig" class="px-6 py-3 bg-red-600 hover:bg-red-700 rounded-md transition">
        <i class="fas fa-undo mr-2"></i>重置为默认
      </button>
    </div>
  </div>

  <script src="/static/js/config.js"></script>
</body>
</html>
