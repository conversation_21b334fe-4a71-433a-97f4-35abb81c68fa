#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI安全监控系统 - 增强版语音助手启动器
功能：启动完善的语音助手，包含性能监控、文件管理、对话管理等功能
"""

import asyncio
import signal
import sys
import time
from datetime import datetime
import logging

# 导入语音助手模块
from src.voice.voice_assistant import (
    VoiceConfig,
    WakeWordDetector,
    SpeechRecognizer,
    TextToSpeech,
    DialogueManager,
    VoiceAssistantMonitor,
    VoiceFileManager,
    file_manager,
    performance_monitor
)

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

class EnhancedVoiceAssistant:
    """增强版语音助手"""
    
    def __init__(self):
        # 初始化组件
        self.wake_detector = WakeWordDetector(VoiceConfig.WAKE_WORDS)
        self.speech_recognizer = SpeechRecognizer()
        self.tts = TextToSpeech()
        self.dialogue_manager = DialogueManager()
        
        # 状态管理
        self.is_active = False
        self.is_listening = False
        self.conversation_active = False
        self.last_interaction_time = time.time()
        
        # 性能监控
        self.monitor = performance_monitor
        
        logger.info("增强版语音助手初始化完成")
    
    def print_startup_banner(self):
        """打印启动横幅"""
        print("\n" + "=" * 80)
        print("🎙️  AI安全监控系统 - 增强版语音助手 v2.0")
        print("=" * 80)
        print(f"🔧 FunASR集成: {'✅ 已启用' if VoiceConfig.FUNASR_ENABLED else '❌ 已禁用'}")
        print(f"🌐 FunASR服务器: {VoiceConfig.FUNASR_WS_URL}")
        print(f"🎯 唤醒词: {', '.join(VoiceConfig.WAKE_WORDS)}")
        print(f"📁 语音文件目录: {VoiceConfig.VOICE_DIR}")
        print(f"💾 TTS缓存: {'✅ 已启用' if VoiceConfig.TTS_CACHE_ENABLED else '❌ 已禁用'}")
        print("=" * 80)
        print("💡 新功能特性:")
        print("   • 智能对话管理 - 上下文记忆和情感分析")
        print("   • 性能监控 - 实时性能指标和错误追踪")
        print("   • 文件管理 - 自动清理和缓存优化")
        print("   • 增强检测 - 改进的唤醒词检测算法")
        print("   • 统计分析 - 详细的使用统计和报告")
        print("=" * 80)
        print("🎮 控制命令:")
        print("   • Ctrl+C: 优雅退出")
        print("   • 说出唤醒词开始对话")
        print("   • 支持连续对话和上下文理解")
        print("=" * 80)
    
    def print_system_status(self):
        """打印系统状态"""
        print("\n📊 系统状态检查:")
        
        # 检查FunASR连接
        funasr_status = "🟢 正常" if self.speech_recognizer.use_funasr else "🔴 不可用"
        print(f"   FunASR: {funasr_status}")
        
        # 检查Whisper
        whisper_status = "🟢 正常" if self.speech_recognizer.whisper_model else "🔴 不可用"
        print(f"   Whisper: {whisper_status}")
        
        # 检查TTS
        tts_status = "🟢 正常" if self.tts.use_pygame else "🔴 模拟模式"
        print(f"   语音合成: {tts_status}")
        
        # 检查文件系统
        print(f"   文件管理: 🟢 正常")
        print(f"   性能监控: 🟢 正常")
        
        # 显示配置信息
        print(f"\n⚙️ 配置信息:")
        print(f"   采样率: {VoiceConfig.SAMPLE_RATE} Hz")
        print(f"   缓冲区大小: {VoiceConfig.CHUNK_SIZE}")
        print(f"   对话超时: {VoiceConfig.CONVERSATION_TIMEOUT} 秒")
        print(f"   上下文记忆: {VoiceConfig.CONTEXT_MEMORY_SIZE} 轮")
    
    async def start(self):
        """启动语音助手"""
        self.print_startup_banner()
        self.print_system_status()
        
        print(f"\n🚀 启动语音助手...")
        
        self.is_active = True
        
        # 播放启动提示
        await self.tts.speak("增强版语音助手已启动，请说出唤醒词开始对话")
        
        # 启动主循环
        await self._main_loop()
    
    async def stop(self):
        """停止语音助手"""
        logger.info("🛑 语音助手停止中...")
        
        self.is_active = False
        
        # 保存对话日志
        if self.dialogue_manager.conversation_history:
            self.dialogue_manager.save_conversation_log()
        
        # 显示性能报告
        self._print_performance_report()
        
        await self.tts.speak("语音助手已关闭，再见")
        
        print("\n👋 语音助手已安全关闭")
    
    async def _main_loop(self):
        """主循环"""
        logger.info("🎙️ 开始监听唤醒词...")
        print("\n🎧 正在监听唤醒词，请说话...")
        
        try:
            while self.is_active:
                # 模拟音频检测（实际应用中会有真实的音频流）
                await asyncio.sleep(1)
                
                # 检查对话超时
                if self.conversation_active and self.dialogue_manager.is_conversation_timeout():
                    await self._handle_conversation_timeout()
                
                # 演示模式：不自动触发模拟唤醒
                if not self.conversation_active:
                    # 等待真实的唤醒词检测或手动触发
                    pass
                        
        except KeyboardInterrupt:
            logger.info("收到中断信号")
        except Exception as e:
            logger.error(f"主循环异常: {e}")
            self.monitor.record_error("main_loop", str(e))
    
    async def _simulate_wake_detection(self):
        """模拟唤醒词检测（演示用）"""
        print("\n🎯 模拟检测到唤醒词...")
        
        # 记录唤醒检测
        self.monitor.record_wake_detection()
        self.wake_detector.record_detection("模拟唤醒", 0.9)
        
        # 开始对话
        await self._start_conversation()
    
    async def _start_conversation(self):
        """开始对话"""
        self.conversation_active = True
        self.last_interaction_time = time.time()
        
        # 随机选择确认回复
        confirmation = self.wake_detector.get_random_confirmation()
        await self.tts.speak(confirmation)
        
        print(f"🤖 助手: {confirmation}")
        print("🎤 请说出您的指令...")
        
        # 模拟用户输入（实际应用中会是语音识别）
        await self._simulate_user_input()
    
    async def _simulate_user_input(self):
        """模拟用户输入（演示用）"""
        # 模拟语音识别过程
        print("🔄 正在识别语音...")
        
        start_time = time.time()
        
        # 演示模式已禁用模拟识别
        print("   演示模式：模拟语音识别已禁用")
        print("   请使用真实的语音输入或手动输入")

        # 结束演示对话
        await self._end_conversation()
    
    async def _process_user_input(self, user_input: str):
        """处理用户输入"""
        try:
            # 生成回复
            response = await self._generate_response(user_input)
            
            # 添加到对话历史
            self.dialogue_manager.add_message("assistant", response)
            
            # 语音回复
            start_time = time.time()
            await self.tts.speak(response)
            tts_time = time.time() - start_time
            
            # 记录TTS尝试
            self.monitor.record_tts_attempt(True, tts_time)
            
            print(f"🤖 助手: {response}")
            
            # 检查是否结束对话
            if any(keyword in user_input.lower() for keyword in ["退出", "再见", "结束"]):
                await self._end_conversation()
            else:
                # 继续等待用户输入
                print("🎤 继续说话或说'退出'结束对话...")
                await asyncio.sleep(2)  # 模拟等待
                await self._simulate_user_input()  # 继续模拟对话
                
        except Exception as e:
            logger.error(f"处理用户输入失败: {e}")
            self.monitor.record_error("user_input_processing", str(e))
            await self.tts.speak("抱歉，处理您的请求时出现了问题")
    
    async def _generate_response(self, user_input: str) -> str:
        """生成回复"""
        # 获取对话上下文
        context = self.dialogue_manager.get_context()
        
        # 简单的回复生成逻辑（实际应用中会使用RAG或LLM）
        if "警报" in user_input:
            return "根据最新数据，今天共检测到3个警报事件，包括1个入侵检测和2个异常行为。所有事件都已妥善处理。"
        elif "统计" in user_input:
            return "系统运行正常，今日监控时长12小时，检测准确率98.5%，无误报事件。"
        elif "帮助" in user_input:
            return "我可以帮您查询警报信息、系统统计、控制监控设备等。请告诉我您需要什么帮助。"
        else:
            return f"我理解您说的是：{user_input}。作为AI安全监控助手，我建议您查看系统日志或联系技术支持获取更详细的信息。"
    
    async def _handle_conversation_timeout(self):
        """处理对话超时"""
        print("\n⏰ 对话超时，结束当前会话")
        await self.tts.speak("对话超时，我将结束当前会话")
        await self._end_conversation()
    
    async def _end_conversation(self):
        """结束对话"""
        self.conversation_active = False
        
        # 保存对话日志
        self.dialogue_manager.save_conversation_log()
        
        # 显示对话统计
        stats = self.dialogue_manager.get_conversation_stats()
        print(f"\n📈 对话统计:")
        print(f"   对话轮数: {stats['total_turns']}")
        print(f"   持续时间: {stats['session_duration']:.1f} 秒")
        print(f"   用户情绪: {stats['user_mood']}")
        
        # 清空对话上下文
        self.dialogue_manager.clear_context()
        
        print("🎧 继续监听唤醒词...")
    
    def _print_performance_report(self):
        """打印性能报告"""
        report = self.monitor.get_performance_report()
        
        print(f"\n📊 性能报告:")
        print(f"   运行时间: {report['uptime_formatted']}")
        print(f"   总请求数: {report['total_requests']}")
        print(f"   识别成功率: {report['recognition_success_rate']}%")
        print(f"   FunASR成功率: {report['funasr_success_rate']}%")
        print(f"   TTS成功率: {report['tts_success_rate']}%")
        print(f"   平均识别时间: {report['avg_recognition_time']}s")
        print(f"   唤醒词检测: {report['wake_word_detections']} 次")
        print(f"   系统错误: {report['system_errors']} 次")

async def main():
    """主函数"""
    assistant = EnhancedVoiceAssistant()
    
    # 设置信号处理
    def signal_handler(signum, frame):
        print(f"\n收到信号 {signum}，正在优雅退出...")
        asyncio.create_task(assistant.stop())
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    
    try:
        await assistant.start()
    except KeyboardInterrupt:
        await assistant.stop()
    except Exception as e:
        logger.error(f"程序异常: {e}")
        await assistant.stop()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
