#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI安全监控系统 - 语音助手核心模块
功能：语音唤醒、语音识别、语音合成、对话管理、系统集成
版本：v2.0 - 集成FunASR，完善功能体验
作者：AI安全监控团队
"""

import asyncio
import threading
import queue
import time
import json
import re
import os
import uuid
from datetime import datetime, timedelta
from typing import Optional, Dict, List, Callable, Any
import logging
from pathlib import Path

# WebSocket支持
try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("websockets不可用，FunASR功能将不可用")

# 音频处理
try:
    import pyaudio
    PYAUDIO_AVAILABLE = True
except ImportError:
    PYAUDIO_AVAILABLE = False
    print("PyAudio不可用，将使用sounddevice作为替代")

import sounddevice as sd
import wave
import numpy as np

# 语音识别和合成
try:
    import whisper
    WHISPER_AVAILABLE = True
except ImportError:
    WHISPER_AVAILABLE = False
    print("Whisper不可用，将使用模拟语音识别")

try:
    import edge_tts
    EDGE_TTS_AVAILABLE = True
except ImportError:
    EDGE_TTS_AVAILABLE = False
    print("Edge-TTS不可用，将使用模拟语音合成")

try:
    import pygame
    PYGAME_AVAILABLE = True
except ImportError:
    PYGAME_AVAILABLE = False
    print("Pygame不可用，将使用模拟音频播放")

# 业务逻辑 - 使用try-except避免依赖问题
try:
    from src.api.rag_query import rag_query
    RAG_AVAILABLE = True
except ImportError as e:
    print(f"RAG查询不可用，尝试简化版: {e}")
    try:
        from src.api.rag_query_simple import rag_query
        RAG_AVAILABLE = True
        print("使用简化版RAG查询")
    except ImportError as e2:
        print(f"简化版RAG查询也不可用: {e2}")
        RAG_AVAILABLE = False
        async def rag_query(query):
            return "抱歉，语音查询功能暂时不可用"

try:
    from alert_statistics import query_recent_alert_counts, query_recent_alerts
    ALERT_STATS_AVAILABLE = True
except ImportError as e:
    print(f"警报统计不可用: {e}")
    ALERT_STATS_AVAILABLE = False
    def query_recent_alert_counts():
        return [], []  # 返回空数据而不是模拟数据
    def query_recent_alerts():
        return [], []  # 返回空数据而不是模拟数据

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class VoiceFileManager:
    """语音文件管理器"""

    def __init__(self):
        self.voice_dir = Path(VoiceConfig.VOICE_DIR)
        self.temp_dir = Path(VoiceConfig.TEMP_AUDIO_DIR)
        self.conversation_dir = Path(VoiceConfig.CONVERSATION_LOG_DIR)
        self.tts_cache_dir = Path(VoiceConfig.TTS_CACHE_DIR)

        # 创建必要的目录
        self._ensure_directories()

        # 启动清理任务
        self._start_cleanup_task()

    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        for directory in [self.voice_dir, self.temp_dir, self.conversation_dir, self.tts_cache_dir]:
            directory.mkdir(parents=True, exist_ok=True)
            logger.debug(f"确保目录存在: {directory}")

    def get_temp_audio_file(self, prefix: str = "temp_audio") -> str:
        """获取临时音频文件路径"""
        timestamp = int(time.time() * 1000)
        filename = f"{prefix}_{timestamp}.wav"
        return str(self.temp_dir / filename)

    def get_tts_cache_file(self, text: str, voice: str) -> str:
        """获取TTS缓存文件路径"""
        import hashlib
        text_hash = hashlib.md5(f"{text}_{voice}".encode()).hexdigest()
        filename = f"tts_{text_hash}.mp3"
        return str(self.tts_cache_dir / filename)

    def save_conversation_log(self, conversation_data: Dict[str, Any]):
        """保存对话日志"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"conversation_{timestamp}.json"
            filepath = self.conversation_dir / filename

            with open(filepath, 'w', encoding='utf-8') as f:
                json.dump(conversation_data, f, ensure_ascii=False, indent=2)

            logger.debug(f"对话日志已保存: {filepath}")
        except Exception as e:
            logger.error(f"保存对话日志失败: {e}")

    def cleanup_temp_files(self):
        """清理临时文件"""
        try:
            temp_files = list(self.temp_dir.glob("*.wav"))
            temp_files.extend(list(self.temp_dir.glob("*.mp3")))

            # 按修改时间排序，保留最新的文件
            temp_files.sort(key=lambda x: x.stat().st_mtime, reverse=True)

            # 删除超过限制的文件
            if len(temp_files) > VoiceConfig.MAX_TEMP_FILES:
                files_to_delete = temp_files[VoiceConfig.MAX_TEMP_FILES:]
                for file_path in files_to_delete:
                    try:
                        file_path.unlink()
                        logger.debug(f"删除临时文件: {file_path}")
                    except Exception as e:
                        logger.warning(f"删除临时文件失败 {file_path}: {e}")

            # 删除超过1小时的临时文件
            cutoff_time = time.time() - 3600  # 1小时前
            for file_path in temp_files:
                try:
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        logger.debug(f"删除过期临时文件: {file_path}")
                except Exception as e:
                    logger.warning(f"删除过期文件失败 {file_path}: {e}")

        except Exception as e:
            logger.error(f"清理临时文件失败: {e}")

    def _start_cleanup_task(self):
        """启动定期清理任务"""
        def cleanup_worker():
            while True:
                time.sleep(VoiceConfig.TEMP_FILE_CLEANUP_INTERVAL)
                self.cleanup_temp_files()

        cleanup_thread = threading.Thread(target=cleanup_worker, daemon=True)
        cleanup_thread.start()
        logger.debug("临时文件清理任务已启动")

# 全局文件管理器实例（延迟初始化）
file_manager = None

def get_file_manager():
    """获取文件管理器实例（延迟初始化）"""
    global file_manager
    if file_manager is None:
        file_manager = VoiceFileManager()
    return file_manager

class VoiceConfig:
    """语音助手配置 - 完善版"""

    # === 音频参数 ===
    SAMPLE_RATE = 16000
    CHUNK_SIZE = 1024
    CHANNELS = 1
    FORMAT = 'int16'  # sounddevice格式
    PYAUDIO_FORMAT = None  # 如果PyAudio可用则设置

    # === 唤醒词配置 ===
    WAKE_WORDS = ["小凯小凯", "你好小凯", "小凯同学", "小凯助手", "嘿小凯"]
    WAKE_THRESHOLD = 0.6  # 唤醒阈值 (降低以提高检测灵敏度)
    WAKE_CONFIRMATION_SOUNDS = ["嗯", "好的", "我在", "您好", "有什么可以帮您的"]

    # === 录音配置 ===
    RECORD_TIMEOUT = 5  # 录音超时时间(秒)
    SILENCE_THRESHOLD = 500  # 静音阈值
    SILENCE_DURATION = 2  # 静音持续时间(秒)
    MAX_RECORD_TIME = 30  # 最大录音时间(秒)
    MIN_RECORD_TIME = 0.5  # 最小录音时间(秒)

    # === FunASR配置 ===
    FUNASR_ENABLED = True  # 启用FunASR
    FUNASR_WS_URL = "ws://192.168.3.95:10096/"  # FunASR WebSocket地址
    FUNASR_TIMEOUT = 10  # WebSocket连接超时时间(秒)
    FUNASR_RETRY_COUNT = 2  # 重试次数
    FUNASR_CHUNK_SIZE = 960  # PCM数据块大小

    # === TTS配置 ===
    TTS_VOICE = "zh-CN-XiaoxiaoNeural"  # 中文女声
    TTS_RATE = "+0%"  # 语速
    TTS_VOLUME = "+0%"  # 音量
    TTS_CACHE_ENABLED = True  # 启用TTS缓存
    TTS_CACHE_DIR = "voice/tts_cache"  # TTS缓存目录

    # === 对话管理配置 ===
    CONVERSATION_TIMEOUT = 30  # 对话超时时间(秒)
    MAX_CONVERSATION_TURNS = 10  # 最大对话轮数
    CONTEXT_MEMORY_SIZE = 5  # 上下文记忆条数

    # === 休眠配置 ===
    SLEEP_TIMEOUT = 300  # 休眠超时时间(秒) - 5分钟
    SLEEP_REMINDER_INTERVAL = 60  # 休眠提醒间隔(秒)

    # === 调试和演示配置 ===
    ENABLE_MOCK_FEATURES = False  # 禁用所有模拟功能

    # === 文件管理配置 ===
    VOICE_DIR = "data/voice"  # 语音文件目录
    TEMP_AUDIO_DIR = "data/voice/temp"  # 临时音频文件目录
    CONVERSATION_LOG_DIR = "data/voice/conversations"  # 对话日志目录
    MAX_TEMP_FILES = 50  # 最大临时文件数量
    TEMP_FILE_CLEANUP_INTERVAL = 3600  # 临时文件清理间隔(秒)

    # === 性能配置 ===
    AUDIO_BUFFER_SIZE = 4096  # 音频缓冲区大小
    PROCESSING_THREADS = 2  # 处理线程数
    ENABLE_AUDIO_ENHANCEMENT = True  # 启用音频增强

    # === 调试配置 ===
    DEBUG_MODE = False  # 调试模式
    SAVE_DEBUG_AUDIO = False  # 保存调试音频
    LOG_LEVEL = "INFO"  # 日志级别

class WakeWordDetector:
    """唤醒词检测器"""
    
    def __init__(self, wake_words: List[str], threshold: float = 0.7):
        self.wake_words = wake_words
        self.threshold = threshold
        self.whisper_model = None
        self.detection_history = []  # 检测历史
        self.last_detection_time = 0
        self.detection_cooldown = 2.0  # 检测冷却时间(秒)
        self.false_positive_count = 0  # 误检计数
        self._load_model()
    
    def _load_model(self):
        """加载Whisper模型用于唤醒词检测"""
        if WHISPER_AVAILABLE:
            try:
                self.whisper_model = whisper.load_model("tiny")  # 使用小模型提高速度
                logger.info("唤醒词检测模型加载成功")
            except Exception as e:
                logger.error(f"唤醒词检测模型加载失败: {e}")
                self.whisper_model = None
        else:
            logger.info("Whisper不可用，将使用简单能量检测")
    
    def detect(self, audio_data: np.ndarray) -> bool:
        """
        检测音频中是否包含唤醒词
        """
        # 如果Whisper模型不可用，使用简单的能量检测
        if self.whisper_model is None:
            return self._simple_energy_detection(audio_data)

        try:
            # 使用Whisper进行语音识别
            result = self.whisper_model.transcribe(audio_data, language="zh")
            text = result["text"].strip()

            if text:
                logger.debug(f"检测到语音: {text}")
                # 检查是否包含唤醒词
                for wake_word in self.wake_words:
                    if wake_word in text:
                        logger.info(f"检测到唤醒词: {wake_word}")
                        return True

            return False
        except Exception as e:
            logger.error(f"唤醒词检测失败: {e}")
            return self._simple_energy_detection(audio_data)

    def _simple_energy_detection(self, audio_data: np.ndarray) -> bool:
        """简单的能量检测（用于模拟模式）"""
        try:
            # 计算音频能量
            energy = np.mean(np.abs(audio_data))

            # 如果能量超过阈值，认为检测到唤醒词
            if energy > 0.05:  # 模拟阈值
                # 添加时间间隔限制，避免频繁触发
                current_time = time.time()
                if not hasattr(self, '_last_detection'):
                    self._last_detection = 0

                if current_time - self._last_detection > 30:  # 30秒间隔
                    self._last_detection = current_time
                    logger.info("简单能量检测到可能的唤醒词")
                    return True

            return False
        except Exception as e:
            logger.error(f"简单能量检测失败: {e}")
            return False

    def get_random_confirmation(self) -> str:
        """获取随机确认回复"""
        import random
        return random.choice(VoiceConfig.WAKE_CONFIRMATION_SOUNDS)

    def record_detection(self, wake_word: str, confidence: float = 1.0):
        """记录检测结果"""
        detection_data = {
            "timestamp": datetime.now().isoformat(),
            "wake_word": wake_word,
            "confidence": confidence
        }

        self.detection_history.append(detection_data)
        self.last_detection_time = time.time()

        # 保持历史记录在合理范围内
        if len(self.detection_history) > 50:
            self.detection_history = self.detection_history[-25:]

        logger.debug(f"记录唤醒词检测: {detection_data}")

    def get_detection_stats(self) -> Dict[str, Any]:
        """获取检测统计信息"""
        current_time = time.time()

        # 兼容Python 3.6，使用dateutil.parser或手动解析
        recent_detections = []
        for d in self.detection_history:
            try:
                # 尝试解析时间戳
                if isinstance(d, dict) and "timestamp" in d:
                    # 简单的ISO格式解析
                    timestamp_str = d["timestamp"]
                    if "T" in timestamp_str:
                        timestamp_str = timestamp_str.replace("T", " ")
                    if "." in timestamp_str:
                        timestamp_str = timestamp_str.split(".")[0]

                    dt = datetime.strptime(timestamp_str[:19], "%Y-%m-%d %H:%M:%S")
                    if current_time - dt.timestamp() < 3600:
                        recent_detections.append(d)
            except:
                # 如果解析失败，跳过这个记录
                continue

        return {
            "total_detections": len(self.detection_history),
            "recent_detections": len(recent_detections),
            "last_detection": self.last_detection_time,
            "detection_rate": len(recent_detections) / 60 if recent_detections else 0,
            "false_positive_count": self.false_positive_count,
            "most_common_wake_word": self._get_most_common_wake_word()
        }

    def _get_most_common_wake_word(self) -> str:
        """获取最常用的唤醒词"""
        if not self.detection_history:
            return ""

        from collections import Counter
        wake_words = [d["wake_word"] for d in self.detection_history]
        most_common = Counter(wake_words).most_common(1)
        return most_common[0][0] if most_common else ""

class SpeechRecognizer:
    """语音识别器 - 支持FunASR和Whisper"""

    def __init__(self):
        self.whisper_model = None
        self.use_funasr = WEBSOCKETS_AVAILABLE and VoiceConfig.FUNASR_ENABLED
        self._load_model()

    def _load_model(self):
        """加载语音识别模型"""
        if self.use_funasr:
            logger.info("使用FunASR进行语音识别")
        elif WHISPER_AVAILABLE:
            try:
                self.whisper_model = whisper.load_model("base")  # 使用base模型平衡速度和精度
                logger.info("Whisper语音识别模型加载成功")
            except Exception as e:
                logger.error(f"Whisper模型加载失败: {e}")
                self.whisper_model = None
        else:
            logger.info("语音识别模型不可用，将使用模拟识别")

        # 如果FunASR被禁用，显示提示信息
        if not VoiceConfig.FUNASR_ENABLED:
            logger.info("FunASR已被禁用，使用Whisper作为主要识别引擎")

    async def recognize(self, audio_file: str) -> Optional[str]:
        """
        识别音频文件中的语音
        """
        # 优先使用FunASR
        if self.use_funasr:
            result = await self._funasr_recognize(audio_file)
            if result:
                return result
            else:
                logger.debug("FunASR未返回有效识别结果")
                # 不再回退到其他识别方式，直接返回None
                return None

        # 使用Whisper作为备用（仅在FunASR不可用时）
        if self.whisper_model is not None:
            try:
                # 异步执行语音识别
                loop = asyncio.get_event_loop()
                result = await loop.run_in_executor(
                    None,
                    lambda: self.whisper_model.transcribe(audio_file, language="zh")
                )

                text = result["text"].strip()
                logger.info(f"Whisper识别结果: {text}")
                return text if text else None

            except Exception as e:
                logger.error(f"Whisper识别失败: {e}")

        # 如果所有识别方式都不可用，返回None而不是模拟识别
        logger.warning("所有语音识别方式都不可用")
        return None

    async def _funasr_recognize(self, audio_file: str) -> Optional[str]:
        """使用FunASR进行语音识别 - 基于正确协议格式"""
        try:
            import os
            import struct

            # 获取文件名（不含路径和扩展名）
            wav_name = os.path.splitext(os.path.basename(audio_file))[0]

            # 转换为PCM格式
            pcm_data = self._convert_to_pcm_enhanced(audio_file)
            if not pcm_data:
                logger.error("PCM转换失败")
                return None

            # 连接FunASR WebSocket服务
            async with websockets.connect(
                VoiceConfig.FUNASR_WS_URL,
                timeout=VoiceConfig.FUNASR_TIMEOUT
            ) as websocket:

                # 1. 连接成功后立即发送初始配置（模仿HTML示例）
                initial_config = {
                    "chunk_size": [5, 10, 5],
                    "wav_name": wav_name,
                    "is_speaking": True,
                    "chunk_interval": 10,
                    "itn": False,  # 关闭逆文本标准化
                    "mode": "2pass"  # 使用2pass模式
                }
                await websocket.send(json.dumps(initial_config))
                logger.debug(f"FunASR: 发送初始配置 - {initial_config}")

                # 2. 流式发送PCM数据块（模仿HTML示例的分块发送）
                chunk_size = 960  # 每块960个样本点（与HTML示例一致）
                pcm_int16 = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)  # 转换为int16数组

                # 分块发送PCM数据
                for i in range(0, len(pcm_int16), chunk_size):
                    chunk = pcm_int16[i:i+chunk_size]
                    if len(chunk) > 0:
                        # 将int16数组转换为bytes
                        chunk_bytes = struct.pack(f'<{len(chunk)}h', *chunk)
                        await websocket.send(chunk_bytes)
                        logger.debug(f"FunASR: 发送PCM数据块 {i//chunk_size + 1} (长度: {len(chunk)})")

                # 3. 发送剩余数据（如果有）
                remaining = len(pcm_int16) % chunk_size
                if remaining > 0:
                    last_chunk = pcm_int16[-remaining:]
                    chunk_bytes = struct.pack(f'<{len(last_chunk)}h', *last_chunk)
                    await websocket.send(chunk_bytes)
                    logger.debug("FunASR: 发送最后的PCM数据块")

                # 4. 发送结束信号（模仿HTML示例）
                end_config = {
                    "chunk_size": [5, 10, 5],
                    "wav_name": wav_name,
                    "is_speaking": False,
                    "chunk_interval": 10,
                    "mode": "2pass"
                }
                await websocket.send(json.dumps(end_config))
                logger.debug("FunASR: 发送结束信号")

                # 5. 接收识别结果
                responses = []
                final_result = None

                try:
                    while True:
                        response = await asyncio.wait_for(
                            websocket.recv(),
                            timeout=8
                        )
                        responses.append(response)
                        logger.debug(f"FunASR响应: {response}")

                        # 解析响应
                        if isinstance(response, str):
                            try:
                                result_data = json.loads(response)
                                text = result_data.get('text', '').strip()
                                is_final = result_data.get('is_final', False)
                                mode = result_data.get('mode', '')

                                if text:
                                    if is_final or mode in ['2pass-offline', 'offline']:
                                        final_result = text
                                        logger.info(f"🎤 FunASR识别结果: {text}")
                                        break
                                    else:
                                        logger.debug(f"FunASR中间结果: {text}")
                                        # 保存中间结果作为备用
                                        if not final_result:
                                            final_result = text
                                elif is_final:
                                    # 最终响应但无文本，退出循环
                                    logger.debug("收到最终响应但无文本")
                                    break

                            except json.JSONDecodeError:
                                logger.warning(f"FunASR返回非JSON响应: {response}")

                except asyncio.TimeoutError:
                    logger.debug(f"FunASR: 接收完成，共收到 {len(responses)} 个响应")

                # 如果有结果，返回
                if final_result:
                    return final_result

                # 尝试从所有响应中提取文本
                for response in responses:
                    if isinstance(response, str):
                        try:
                            result_data = json.loads(response)
                            text = result_data.get('text', '').strip()
                            if text:
                                logger.info(f"🎤 FunASR从响应中提取结果: {text}")
                                return text
                        except json.JSONDecodeError:
                            continue

                logger.warning("FunASR未返回有效识别结果")
                return None

        except asyncio.TimeoutError:
            logger.error("FunASR识别超时")
            return None
        except Exception as e:
            logger.error(f"FunASR识别失败: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return None

    def _validate_audio_file(self, audio_file: str) -> bool:
        """验证音频文件是否符合FunASR要求"""
        try:
            import wave

            with wave.open(audio_file, 'rb') as wav_file:
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                frames = wav_file.getnframes()

                # 检查基本参数
                if sample_rate != 16000:
                    logger.debug(f"采样率不匹配: {sample_rate} != 16000")
                    return False
                if channels != 1:
                    logger.debug(f"声道数不匹配: {channels} != 1")
                    return False
                if sample_width != 2:
                    logger.debug(f"采样位数不匹配: {sample_width*8} != 16")
                    return False
                if frames == 0:
                    logger.debug("音频帧数为0")
                    return False

                # 检查音频长度（至少0.1秒）
                duration = frames / sample_rate
                if duration < 0.1:
                    logger.debug(f"音频太短: {duration}秒")
                    return False

                return True

        except Exception as e:
            logger.debug(f"音频文件验证失败: {e}")
            return False

    def _enhance_audio_quality(self, audio_data):
        """增强音频质量"""
        try:
            import wave
            import io
            import numpy as np

            if not audio_data.startswith(b'RIFF'):
                return audio_data

            with io.BytesIO(audio_data) as wav_io:
                with wave.open(wav_io, 'rb') as wav_file:
                    sample_rate = wav_file.getframerate()
                    channels = wav_file.getnchannels()
                    sample_width = wav_file.getsampwidth()
                    frames = wav_file.getnframes()

                    # 读取音频数据
                    raw_audio = wav_file.readframes(frames)

                    # 转换为numpy数组进行处理
                    if sample_width == 2:
                        audio_array = np.frombuffer(raw_audio, dtype=np.int16)
                    else:
                        return audio_data  # 不支持的格式

                    # 音频增强处理
                    enhanced_audio = self._apply_audio_enhancements(audio_array)

                    # 重新打包为WAV格式
                    enhanced_wav = io.BytesIO()
                    with wave.open(enhanced_wav, 'wb') as out_wav:
                        out_wav.setnchannels(1)  # 强制单声道
                        out_wav.setsampwidth(2)  # 16位
                        out_wav.setframerate(16000)  # 强制16kHz
                        out_wav.writeframes(enhanced_audio.tobytes())

                    return enhanced_wav.getvalue()

        except Exception as e:
            logger.warning(f"音频增强失败: {e}")
            return audio_data

    def _apply_audio_enhancements(self, audio_array):
        """应用音频增强算法"""
        try:
            import numpy as np

            # 1. 音量标准化
            max_val = np.max(np.abs(audio_array))
            if max_val > 0:
                # 标准化到合适的音量范围
                target_max = 20000  # 目标最大振幅
                if max_val < 1000:  # 音量太小
                    audio_array = audio_array * (target_max / max_val)
                elif max_val > 30000:  # 音量太大
                    audio_array = audio_array * (target_max / max_val)

            # 2. 简单的噪声抑制
            # 计算RMS并设置噪声门限
            rms = np.sqrt(np.mean(audio_array.astype(np.float64) ** 2))
            noise_threshold = rms * 0.1

            # 应用噪声门限
            audio_array = np.where(np.abs(audio_array) < noise_threshold, 0, audio_array)

            # 3. 确保数据类型正确
            audio_array = np.clip(audio_array, -32767, 32767).astype(np.int16)

            return audio_array

        except Exception as e:
            logger.warning(f"音频增强算法失败: {e}")
            return audio_array

    def _convert_to_pcm_enhanced(self, audio_file):
        """增强版PCM转换 - 直接从文件读取"""
        try:
            import wave

            # 直接从文件读取WAV数据
            with wave.open(audio_file, 'rb') as wav_file:
                sample_rate = wav_file.getframerate()
                channels = wav_file.getnchannels()
                sample_width = wav_file.getsampwidth()
                frames = wav_file.getnframes()

                logger.debug(f"WAV信息: {sample_rate}Hz, {channels}ch, {sample_width*8}bit, {frames}frames")

                # 验证音频格式
                if sample_rate != 16000:
                    logger.warning(f"采样率不匹配: {sample_rate} != 16000")
                if channels != 1:
                    logger.warning(f"声道数不匹配: {channels} != 1")
                if sample_width != 2:
                    logger.warning(f"采样位数不匹配: {sample_width*8} != 16")

                # 读取PCM数据
                pcm_data = wav_file.readframes(frames)

                # 验证PCM数据
                if len(pcm_data) == 0:
                    logger.error("PCM数据为空")
                    return None

                logger.debug(f"PCM数据长度: {len(pcm_data)} 字节")
                return pcm_data

        except Exception as e:
            logger.error(f"增强版PCM转换失败: {e}")
            import traceback
            logger.debug(traceback.format_exc())
            return None

    async def _mock_recognize(self, audio_file: str) -> Optional[str]:
        """模拟语音识别 - 已禁用"""
        # 检查是否启用模拟功能
        if not VoiceConfig.ENABLE_MOCK_FEATURES:
            logger.debug("模拟语音识别已禁用，返回None")
            return None

        # 如果启用了模拟功能（仅用于调试）
        logger.warning("使用模拟语音识别（仅调试模式）")
        return "调试模式模拟输入"

class TextToSpeech:
    """语音合成器 - 增强版"""

    def __init__(self):
        # 语音文件存储目录
        self.voice_dir = VoiceConfig.VOICE_DIR
        self.cache_dir = VoiceConfig.TTS_CACHE_DIR

        # TTS缓存
        self.tts_cache = {}
        self.cache_enabled = VoiceConfig.TTS_CACHE_ENABLED

        # 播放队列
        self.play_queue = queue.Queue()
        self.is_playing = False

        # 确保目录存在
        self._ensure_directories()

        # 初始化音频播放
        if PYGAME_AVAILABLE:
            try:
                pygame.mixer.init(frequency=22050, size=-16, channels=2, buffer=512)
                self.use_pygame = True
                logger.info("Pygame音频初始化成功")
            except Exception as e:
                logger.error(f"Pygame初始化失败: {e}")
                self.use_pygame = False
        else:
            self.use_pygame = False
            logger.info("Pygame不可用，将使用模拟音频播放")

        # 启动播放线程
        self._start_play_thread()

    def _ensure_directories(self):
        """确保所有必要目录存在"""
        for directory in [self.voice_dir, self.cache_dir]:
            Path(directory).mkdir(parents=True, exist_ok=True)

        # 清理旧的语音文件
        self._cleanup_old_voice_files()

        # 加载缓存索引
        self._load_cache_index()

    def _load_cache_index(self):
        """加载TTS缓存索引"""
        if not self.cache_enabled:
            return

        try:
            cache_index_file = Path(self.cache_dir) / "cache_index.json"
            if cache_index_file.exists():
                with open(cache_index_file, 'r', encoding='utf-8') as f:
                    self.tts_cache = json.load(f)
                logger.debug(f"加载TTS缓存索引: {len(self.tts_cache)} 条记录")
        except Exception as e:
            logger.warning(f"加载TTS缓存索引失败: {e}")
            self.tts_cache = {}

    def _save_cache_index(self):
        """保存TTS缓存索引"""
        if not self.cache_enabled:
            return

        try:
            cache_index_file = Path(self.cache_dir) / "cache_index.json"
            with open(cache_index_file, 'w', encoding='utf-8') as f:
                json.dump(self.tts_cache, f, ensure_ascii=False, indent=2)
            logger.debug("TTS缓存索引已保存")
        except Exception as e:
            logger.warning(f"保存TTS缓存索引失败: {e}")

    def _start_play_thread(self):
        """启动播放线程"""
        def play_worker():
            while True:
                try:
                    audio_file = self.play_queue.get()
                    if audio_file is None:  # 退出信号
                        break

                    self.is_playing = True
                    self._play_audio_file(audio_file)
                    self.is_playing = False

                    self.play_queue.task_done()
                except Exception as e:
                    logger.error(f"播放线程异常: {e}")
                    self.is_playing = False

        play_thread = threading.Thread(target=play_worker, daemon=True)
        play_thread.start()
        logger.debug("TTS播放线程已启动")

    def _cleanup_old_voice_files(self):
        """清理旧的语音文件"""
        import os
        import time

        try:
            if not os.path.exists(self.voice_dir):
                return

            current_time = time.time()
            cleanup_count = 0

            for filename in os.listdir(self.voice_dir):
                if filename.startswith("tts_") and filename.endswith(".mp3"):
                    file_path = os.path.join(self.voice_dir, filename)
                    try:
                        # 获取文件修改时间
                        file_mtime = os.path.getmtime(file_path)

                        # 如果文件超过1小时未使用，删除它
                        if current_time - file_mtime > 3600:  # 1小时 = 3600秒
                            os.remove(file_path)
                            cleanup_count += 1
                    except Exception as e:
                        logger.warning(f"清理语音文件失败 {file_path}: {e}")

            if cleanup_count > 0:
                logger.info(f"清理了 {cleanup_count} 个旧语音文件")

        except Exception as e:
            logger.warning(f"语音文件清理异常: {e}")

    def cleanup_all_voice_files(self):
        """清理所有语音文件"""
        import os

        try:
            if not os.path.exists(self.voice_dir):
                return 0

            cleanup_count = 0

            for filename in os.listdir(self.voice_dir):
                if filename.startswith("tts_") and filename.endswith(".mp3"):
                    file_path = os.path.join(self.voice_dir, filename)
                    try:
                        os.remove(file_path)
                        cleanup_count += 1
                    except Exception as e:
                        logger.warning(f"删除语音文件失败 {file_path}: {e}")

            logger.info(f"手动清理了 {cleanup_count} 个语音文件")
            return cleanup_count

        except Exception as e:
            logger.error(f"手动清理语音文件异常: {e}")
            return 0

    def get_voice_files_info(self):
        """获取语音文件信息"""
        import os

        try:
            if not os.path.exists(self.voice_dir):
                return {"count": 0, "total_size": 0, "files": []}

            files_info = []
            total_size = 0

            for filename in os.listdir(self.voice_dir):
                if filename.startswith("tts_") and filename.endswith(".mp3"):
                    file_path = os.path.join(self.voice_dir, filename)
                    try:
                        file_size = os.path.getsize(file_path)
                        file_mtime = os.path.getmtime(file_path)

                        files_info.append({
                            "name": filename,
                            "size": file_size,
                            "modified_time": file_mtime
                        })
                        total_size += file_size
                    except Exception as e:
                        logger.warning(f"获取文件信息失败 {file_path}: {e}")

            return {
                "count": len(files_info),
                "total_size": total_size,
                "files": files_info
            }

        except Exception as e:
            logger.error(f"获取语音文件信息异常: {e}")
            return {"count": 0, "total_size": 0, "files": []}
    
    async def speak(self, text: str, voice: str = VoiceConfig.TTS_VOICE) -> bool:
        """
        将文本转换为语音并播放
        """
        # 如果依赖不可用，使用模拟播放
        if not EDGE_TTS_AVAILABLE or not self.use_pygame:
            return await self._mock_speak(text)

        try:
            import os

            # 生成唯一文件名
            timestamp = int(time.time())
            filename = f"tts_{timestamp}.mp3"
            tts_file = os.path.join(self.voice_dir, filename)

            # 生成语音文件
            communicate = edge_tts.Communicate(text, voice)
            await communicate.save(tts_file)
            logger.info(f"语音文件已保存: {tts_file}")

            # 播放语音
            pygame.mixer.music.load(tts_file)
            pygame.mixer.music.play()

            # 等待播放完成
            while pygame.mixer.music.get_busy():
                await asyncio.sleep(0.1)

            # 播放完成后删除文件
            try:
                os.remove(tts_file)
                logger.info(f"语音文件已删除: {tts_file}")
            except Exception as e:
                logger.warning(f"删除语音文件失败: {e}")

            logger.info(f"语音播放完成: {text}")
            return True

        except Exception as e:
            logger.error(f"语音合成失败: {e}")
            return await self._mock_speak(text)

    async def _mock_speak(self, text: str) -> bool:
        """模拟语音播放"""
        try:
            logger.info(f"🔊 模拟语音播放: {text}")

            # 模拟播放时间（根据文本长度）
            play_time = len(text) * 0.1  # 每个字符0.1秒
            await asyncio.sleep(min(play_time, 3))  # 最多3秒

            logger.info(f"语音播放完成: {text}")
            return True

        except Exception as e:
            logger.error(f"模拟语音播放失败: {e}")
            return False

class AudioRecorder:
    """音频录制器 - 支持PyAudio和sounddevice"""

    def __init__(self):
        self.is_recording = False
        self.audio_queue = queue.Queue()
        self.input_device_index = None
        self.use_pyaudio = PYAUDIO_AVAILABLE

        if self.use_pyaudio:
            try:
                self.audio = pyaudio.PyAudio()
                VoiceConfig.PYAUDIO_FORMAT = pyaudio.paInt16
                self._find_input_device()
                logger.info("使用PyAudio作为音频后端")
            except Exception as e:
                logger.warning(f"PyAudio初始化失败，切换到sounddevice: {e}")
                self.use_pyaudio = False

        if not self.use_pyaudio:
            self._find_sounddevice_input()
            logger.info("使用sounddevice作为音频后端")

            # 如果sounddevice也不可用，使用模拟模式
            if self.input_device_index is None:
                logger.warning("所有音频后端都不可用，启用模拟模式")
                self.use_mock = True
            else:
                self.use_mock = False
        else:
            self.use_mock = False

    def _find_sounddevice_input(self):
        """查找sounddevice输入设备"""
        try:
            devices = sd.query_devices()
            logger.info("可用音频设备:")

            # 检查默认输入设备
            try:
                default_input = sd.default.device[0]
                if default_input >= 0:
                    device_info = devices[default_input]
                    if device_info['max_input_channels'] > 0:
                        self.input_device_index = default_input
                        logger.info(f"使用默认输入设备: {device_info['name']} (索引: {default_input})")
                        return
            except:
                pass

            # 如果没有默认设备，手动查找
            input_devices = []
            for i, device in enumerate(devices):
                if device['max_input_channels'] > 0:
                    logger.info(f"  输入设备 {i}: {device['name']}")
                    input_devices.append(i)

            if input_devices:
                # 优先选择麦克风设备
                for device_idx in input_devices:
                    device_info = devices[device_idx]
                    if 'mic' in device_info['name'].lower() or '麦克风' in device_info['name']:
                        # 测试设备是否真的可用
                        if self._test_sounddevice(device_idx):
                            self.input_device_index = device_idx
                            logger.info(f"选择麦克风设备: {device_info['name']} (索引: {device_idx})")
                            return

                # 如果麦克风不可用，测试其他输入设备
                for device_idx in input_devices:
                    if self._test_sounddevice(device_idx):
                        device_info = devices[device_idx]
                        self.input_device_index = device_idx
                        logger.info(f"选择输入设备: {device_info['name']} (索引: {device_idx})")
                        return

                logger.error("所有输入设备都不可用")
            else:
                logger.error("未找到任何输入设备")

        except Exception as e:
            logger.error(f"查找sounddevice设备失败: {e}")

    def _test_sounddevice(self, device_index):
        """测试sounddevice设备是否可用"""
        try:
            # 创建一个短暂的测试流
            logger.info(f"测试设备 {device_index}...")

            # 尝试录制0.1秒的音频来测试设备
            test_duration = 0.1
            sample_rate = VoiceConfig.SAMPLE_RATE

            audio_data = sd.rec(
                int(test_duration * sample_rate),
                samplerate=sample_rate,
                channels=VoiceConfig.CHANNELS,
                device=device_index,
                dtype='int16'
            )
            sd.wait()  # 等待录制完成

            # 检查是否有音频数据
            if audio_data is not None and len(audio_data) > 0:
                logger.info(f"设备 {device_index} 测试成功")
                return True
            else:
                logger.warning(f"设备 {device_index} 无音频数据")
                return False

        except Exception as e:
            logger.warning(f"设备 {device_index} 测试失败: {e}")
            return False
            with sd.InputStream(
                device=device_index,
                channels=1,
                samplerate=16000,
                dtype='int16'
            ):
                pass  # 如果能创建流就说明设备可用
            return True
        except Exception:
            return False

    def _find_input_device(self):
        """查找可用的音频输入设备"""
        try:
            logger.info("正在查找音频输入设备...")

            # 获取设备信息
            device_count = self.audio.get_device_count()
            logger.info(f"找到 {device_count} 个音频设备")

            # 查找默认输入设备
            try:
                default_input = self.audio.get_default_input_device_info()
                self.input_device_index = default_input['index']
                logger.info(f"使用默认输入设备: {default_input['name']} (索引: {self.input_device_index})")
                return
            except OSError:
                logger.warning("未找到默认输入设备，尝试查找其他可用设备...")

            # 遍历所有设备，查找输入设备
            for i in range(device_count):
                try:
                    device_info = self.audio.get_device_info_by_index(i)
                    if device_info['maxInputChannels'] > 0:
                        # 测试设备是否可用
                        try:
                            test_stream = self.audio.open(
                                format=VoiceConfig.FORMAT,
                                channels=1,
                                rate=VoiceConfig.SAMPLE_RATE,
                                input=True,
                                input_device_index=i,
                                frames_per_buffer=1024
                            )
                            test_stream.close()

                            self.input_device_index = i
                            logger.info(f"找到可用输入设备: {device_info['name']} (索引: {i})")
                            return
                        except:
                            continue
                except:
                    continue

            # 如果没有找到可用设备
            if self.input_device_index is None:
                logger.error("未找到可用的音频输入设备")
                raise RuntimeError("未找到可用的音频输入设备")

        except Exception as e:
            logger.error(f"查找音频设备失败: {e}")
            raise

    def _get_stream_params(self):
        """获取音频流参数"""
        if self.use_pyaudio:
            params = {
                'format': VoiceConfig.PYAUDIO_FORMAT,
                'channels': VoiceConfig.CHANNELS,
                'rate': VoiceConfig.SAMPLE_RATE,
                'input': True,
                'frames_per_buffer': VoiceConfig.CHUNK_SIZE
            }

            if self.input_device_index is not None:
                params['input_device_index'] = self.input_device_index

            return params
        else:
            # sounddevice参数
            return {
                'samplerate': VoiceConfig.SAMPLE_RATE,
                'channels': VoiceConfig.CHANNELS,
                'dtype': VoiceConfig.FORMAT,
                'device': self.input_device_index
            }

    def start_continuous_recording(self):
        """开始连续录音（用于唤醒词检测）"""
        if hasattr(self, 'use_mock') and self.use_mock:
            self._start_mock_recording()
        elif self.use_pyaudio:
            self._start_pyaudio_recording()
        else:
            self._start_sounddevice_recording()

    def _start_pyaudio_recording(self):
        """使用PyAudio开始录音"""
        def record_audio():
            try:
                stream_params = self._get_stream_params()
                stream = self.audio.open(**stream_params)
            except Exception as e:
                logger.error(f"无法打开PyAudio音频流: {e}")
                return

            logger.info("开始PyAudio连续录音...")

            while self.is_recording:
                try:
                    data = stream.read(VoiceConfig.CHUNK_SIZE, exception_on_overflow=False)
                    audio_array = np.frombuffer(data, dtype=np.int16).astype(np.float32) / 32768.0
                    self.audio_queue.put(audio_array)
                except Exception as e:
                    logger.error(f"PyAudio录音错误: {e}")
                    break

            stream.stop_stream()
            stream.close()
            logger.info("PyAudio连续录音结束")

        self.is_recording = True
        threading.Thread(target=record_audio, daemon=True).start()

    def _start_sounddevice_recording(self):
        """使用sounddevice开始录音"""
        def record_audio():
            logger.info("开始sounddevice连续录音...")

            try:
                stream_params = self._get_stream_params()

                def audio_callback(indata, frames, time, status):
                    if status:
                        logger.warning(f"sounddevice状态: {status}")

                    # 转换为float32格式
                    audio_array = indata[:, 0].astype(np.float32)
                    self.audio_queue.put(audio_array)

                with sd.InputStream(callback=audio_callback, **stream_params):
                    while self.is_recording:
                        sd.sleep(100)  # 100ms

            except Exception as e:
                logger.error(f"sounddevice录音错误: {e}")

            logger.info("sounddevice连续录音结束")

        self.is_recording = True
        threading.Thread(target=record_audio, daemon=True).start()

    def _start_mock_recording(self):
        """使用模拟录音"""
        def mock_record_audio():
            logger.info("开始模拟连续录音...")

            while self.is_recording:
                try:
                    # 生成模拟音频数据
                    audio_chunk = np.random.normal(0, 0.01, VoiceConfig.CHUNK_SIZE).astype(np.float32)

                    # 每30秒模拟一次"唤醒词"信号
                    if int(time.time()) % 30 == 0:
                        audio_chunk += np.random.normal(0, 0.1, VoiceConfig.CHUNK_SIZE).astype(np.float32)

                    self.audio_queue.put(audio_chunk)
                    time.sleep(VoiceConfig.CHUNK_SIZE / VoiceConfig.SAMPLE_RATE)

                except Exception as e:
                    logger.error(f"模拟录音错误: {e}")
                    break

            logger.info("模拟连续录音结束")

        self.is_recording = True
        threading.Thread(target=mock_record_audio, daemon=True).start()

    def stop_recording(self):
        """停止录音"""
        self.is_recording = False
    
    def record_command(self, duration: int = VoiceConfig.RECORD_TIMEOUT) -> str:
        """录制用户命令"""
        if hasattr(self, 'use_mock') and self.use_mock:
            return self._record_command_mock(duration)
        elif self.use_pyaudio:
            return self._record_command_pyaudio(duration)
        else:
            return self._record_command_sounddevice(duration)

    def _record_command_pyaudio(self, duration: int) -> str:
        """使用PyAudio录制命令"""
        try:
            stream_params = self._get_stream_params()
            stream = self.audio.open(**stream_params)

            logger.info(f"开始PyAudio录制命令，最长{duration}秒...")

            frames = []
            silence_count = 0
            max_silence = int(VoiceConfig.SILENCE_DURATION * VoiceConfig.SAMPLE_RATE / VoiceConfig.CHUNK_SIZE)

            for _ in range(0, int(VoiceConfig.SAMPLE_RATE / VoiceConfig.CHUNK_SIZE * duration)):
                data = stream.read(VoiceConfig.CHUNK_SIZE, exception_on_overflow=False)
                frames.append(data)

                # 检测静音
                audio_array = np.frombuffer(data, dtype=np.int16)
                if np.max(np.abs(audio_array)) < VoiceConfig.SILENCE_THRESHOLD:
                    silence_count += 1
                    if silence_count > max_silence:
                        logger.info("检测到静音，结束录音")
                        break
                else:
                    silence_count = 0

            stream.stop_stream()
            stream.close()

            # 保存音频文件到正确的目录
            import os
            os.makedirs(VoiceConfig.TEMP_AUDIO_DIR, exist_ok=True)

            filename = f"temp_command_{int(time.time())}.wav"
            filepath = os.path.join(VoiceConfig.TEMP_AUDIO_DIR, filename)
            with wave.open(filepath, 'wb') as wf:
                wf.setnchannels(VoiceConfig.CHANNELS)
                wf.setsampwidth(self.audio.get_sample_size(VoiceConfig.PYAUDIO_FORMAT))
                wf.setframerate(VoiceConfig.SAMPLE_RATE)
                wf.writeframes(b''.join(frames))

            logger.info(f"PyAudio命令录制完成: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"PyAudio录制命令失败: {e}")
            return ""

    def _record_command_sounddevice(self, duration: int) -> str:
        """使用sounddevice录制命令"""
        try:
            logger.info(f"开始sounddevice录制命令，最长{duration}秒...")

            # 录制音频
            stream_params = self._get_stream_params()
            audio_data = sd.rec(
                frames=int(duration * VoiceConfig.SAMPLE_RATE),
                **stream_params
            )
            sd.wait()  # 等待录制完成

            # 保存音频文件到正确的目录
            import os
            os.makedirs(VoiceConfig.TEMP_AUDIO_DIR, exist_ok=True)

            filename = f"temp_command_{int(time.time())}.wav"
            filepath = os.path.join(VoiceConfig.TEMP_AUDIO_DIR, filename)

            # 转换为int16格式并保存
            audio_int16 = (audio_data * 32767).astype(np.int16)

            with wave.open(filepath, 'wb') as wf:
                wf.setnchannels(VoiceConfig.CHANNELS)
                wf.setsampwidth(2)  # 16-bit = 2 bytes
                wf.setframerate(VoiceConfig.SAMPLE_RATE)
                wf.writeframes(audio_int16.tobytes())

            logger.info(f"sounddevice命令录制完成: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"sounddevice录制命令失败: {e}")
            return ""

    def _record_command_mock(self, duration: int) -> str:
        """使用模拟录制命令"""
        try:
            logger.info(f"开始模拟录制命令，时长: {duration}秒...")

            # 模拟处理时间
            time.sleep(1)

            # 生成模拟音频数据
            frames = int(duration * VoiceConfig.SAMPLE_RATE)
            audio_data = np.random.normal(0, 0.05, frames).astype(np.int16)

            # 保存音频文件到正确的目录
            import os
            os.makedirs(VoiceConfig.TEMP_AUDIO_DIR, exist_ok=True)

            filename = f"temp_command_{int(time.time())}.wav"
            filepath = os.path.join(VoiceConfig.TEMP_AUDIO_DIR, filename)

            with wave.open(filepath, 'wb') as wf:
                wf.setnchannels(VoiceConfig.CHANNELS)
                wf.setsampwidth(2)
                wf.setframerate(VoiceConfig.SAMPLE_RATE)
                wf.writeframes(audio_data.tobytes())

            logger.info(f"模拟命令录制完成: {filepath}")
            return filepath

        except Exception as e:
            logger.error(f"模拟录制命令失败: {e}")
            return ""

    def __del__(self):
        """清理资源"""
        if hasattr(self, 'audio') and self.use_pyaudio:
            self.audio.terminate()

class DialogueManager:
    """对话管理器 - 增强版"""

    def __init__(self):
        self.conversation_history = []
        self.context_window = VoiceConfig.CONTEXT_MEMORY_SIZE
        self.session_id = str(uuid.uuid4())
        self.session_start_time = datetime.now()
        self.last_interaction_time = datetime.now()

        # 对话统计
        self.total_turns = 0
        self.user_messages = 0
        self.assistant_messages = 0

        # 情感状态
        self.user_mood = "neutral"  # neutral, happy, frustrated, confused
        self.conversation_topic = "general"

        logger.info(f"对话管理器初始化完成，会话ID: {self.session_id}")

    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """添加对话消息"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "session_id": self.session_id,
            "metadata": metadata or {}
        }

        self.conversation_history.append(message)
        self.last_interaction_time = datetime.now()

        # 更新统计
        if role == "user":
            self.user_messages += 1
        elif role == "assistant":
            self.assistant_messages += 1

        self.total_turns += 1

        # 保持对话历史在合理范围内
        if len(self.conversation_history) > self.context_window * 2:
            self.conversation_history = self.conversation_history[-self.context_window * 2:]

        # 分析用户情感
        if role == "user":
            self._analyze_user_mood(content)

        logger.debug(f"添加对话消息: {role} - {content[:50]}...")

        # 保存对话历史到文件
        self._save_conversation_to_file()

    def get_context(self, include_metadata: bool = False) -> str:
        """获取对话上下文"""
        if not self.conversation_history:
            return ""

        context_parts = []
        recent_messages = self.conversation_history[-self.context_window:]

        for msg in recent_messages:
            role = "用户" if msg["role"] == "user" else "助手"
            content = msg["content"]

            if include_metadata and msg.get("metadata"):
                metadata_str = ", ".join([f"{k}:{v}" for k, v in msg["metadata"].items()])
                context_parts.append(f"{role}: {content} [{metadata_str}]")
            else:
                context_parts.append(f"{role}: {content}")

        return "\n".join(context_parts)

    def get_conversation_summary(self) -> Dict[str, Any]:
        """获取对话摘要"""
        duration = (self.last_interaction_time - self.session_start_time).total_seconds()

        return {
            "session_id": self.session_id,
            "start_time": self.session_start_time.isoformat(),
            "last_interaction": self.last_interaction_time.isoformat(),
            "duration_seconds": duration,
            "total_turns": self.total_turns,
            "user_messages": self.user_messages,
            "assistant_messages": self.assistant_messages,
            "user_mood": self.user_mood,
            "conversation_topic": self.conversation_topic,
            "message_count": len(self.conversation_history)
        }

    def _analyze_user_mood(self, content: str):
        """分析用户情感状态"""
        content_lower = content.lower()

        # 简单的情感分析
        if any(word in content_lower for word in ["谢谢", "好的", "不错", "很好", "满意"]):
            self.user_mood = "happy"
        elif any(word in content_lower for word in ["不行", "错误", "问题", "失败", "不好"]):
            self.user_mood = "frustrated"
        elif any(word in content_lower for word in ["什么", "怎么", "为什么", "不懂", "不明白"]):
            self.user_mood = "confused"
        else:
            self.user_mood = "neutral"

    def save_conversation_log(self):
        """保存对话日志"""
        try:
            conversation_data = {
                "summary": self.get_conversation_summary(),
                "messages": self.conversation_history
            }

            file_manager.save_conversation_log(conversation_data)
            logger.info(f"对话日志已保存，会话ID: {self.session_id}")
        except Exception as e:
            logger.error(f"保存对话日志失败: {e}")

    def clear_context(self):
        """清空对话上下文"""
        # 保存当前对话日志
        if self.conversation_history:
            self.save_conversation_log()

        # 重置状态
        self.conversation_history.clear()
        self.session_id = str(uuid.uuid4())
        self.session_start_time = datetime.now()
        self.total_turns = 0
        self.user_messages = 0
        self.assistant_messages = 0
        self.user_mood = "neutral"
        self.conversation_topic = "general"

        logger.info("对话上下文已清空，新会话已开始")

    def _save_conversation_to_file(self):
        """保存对话历史到文件"""
        try:
            import os
            import json

            # 确保目录存在
            os.makedirs("data/voice", exist_ok=True)

            # 保存到JSON文件
            history_file = "data/voice/conversation_history.json"
            with open(history_file, 'w', encoding='utf-8') as f:
                json.dump(self.conversation_history, f, ensure_ascii=False, indent=2)

            logger.debug(f"对话历史已保存到 {history_file}")

        except Exception as e:
            logger.warning(f"保存对话历史失败: {e}")

    def is_conversation_timeout(self) -> bool:
        """检查对话是否超时"""
        timeout_seconds = VoiceConfig.CONVERSATION_TIMEOUT
        time_since_last = (datetime.now() - self.last_interaction_time).total_seconds()
        return time_since_last > timeout_seconds

    def get_conversation_stats(self) -> Dict[str, Any]:
        """获取对话统计信息"""
        return {
            "active_session": self.session_id,
            "session_duration": (datetime.now() - self.session_start_time).total_seconds(),
            "total_turns": self.total_turns,
            "avg_response_time": self._calculate_avg_response_time(),
            "user_mood": self.user_mood,
            "conversation_topic": self.conversation_topic,
            "last_interaction": self.last_interaction_time.isoformat()
        }

    def _calculate_avg_response_time(self) -> float:
        """计算平均响应时间"""
        if len(self.conversation_history) < 2:
            return 0.0

        response_times = []
        for i in range(1, len(self.conversation_history)):
            try:
                # 兼容Python 3.6的时间解析
                prev_timestamp = self.conversation_history[i-1]["timestamp"]
                curr_timestamp = self.conversation_history[i]["timestamp"]

                # 简单的ISO格式解析
                def parse_timestamp(ts):
                    if "T" in ts:
                        ts = ts.replace("T", " ")
                    if "." in ts:
                        ts = ts.split(".")[0]
                    return datetime.strptime(ts[:19], "%Y-%m-%d %H:%M:%S")

                prev_time = parse_timestamp(prev_timestamp)
                curr_time = parse_timestamp(curr_timestamp)
                response_times.append((curr_time - prev_time).total_seconds())
            except:
                # 如果解析失败，跳过这个记录
                continue

        return sum(response_times) / len(response_times) if response_times else 0.0

class VoiceAssistantMonitor:
    """语音助手性能监控器"""

    def __init__(self):
        self.start_time = datetime.now()
        self.metrics = {
            "total_requests": 0,
            "successful_recognitions": 0,
            "failed_recognitions": 0,
            "funasr_requests": 0,
            "funasr_successes": 0,
            "whisper_requests": 0,
            "whisper_successes": 0,
            "tts_requests": 0,
            "tts_successes": 0,
            "wake_word_detections": 0,
            "false_wake_detections": 0,
            "avg_recognition_time": 0.0,
            "avg_tts_time": 0.0,
            "system_errors": 0
        }

        self.performance_history = []
        self.error_log = []

        logger.info("语音助手性能监控器已启动")

    def record_recognition_attempt(self, engine: str, success: bool, duration: float):
        """记录识别尝试"""
        self.metrics["total_requests"] += 1

        if success:
            self.metrics["successful_recognitions"] += 1
        else:
            self.metrics["failed_recognitions"] += 1

        # 记录引擎特定指标
        if engine == "funasr":
            self.metrics["funasr_requests"] += 1
            if success:
                self.metrics["funasr_successes"] += 1
        elif engine == "whisper":
            self.metrics["whisper_requests"] += 1
            if success:
                self.metrics["whisper_successes"] += 1

        # 更新平均识别时间
        self._update_avg_time("recognition", duration)

        logger.debug(f"记录识别尝试: {engine}, 成功: {success}, 耗时: {duration:.2f}s")

    def record_tts_attempt(self, success: bool, duration: float):
        """记录TTS尝试"""
        self.metrics["tts_requests"] += 1

        if success:
            self.metrics["tts_successes"] += 1

        # 更新平均TTS时间
        self._update_avg_time("tts", duration)

        logger.debug(f"记录TTS尝试: 成功: {success}, 耗时: {duration:.2f}s")

    def record_wake_detection(self, is_false_positive: bool = False):
        """记录唤醒词检测"""
        self.metrics["wake_word_detections"] += 1

        if is_false_positive:
            self.metrics["false_wake_detections"] += 1

        logger.debug(f"记录唤醒词检测: 误检: {is_false_positive}")

    def record_error(self, error_type: str, error_message: str):
        """记录系统错误"""
        self.metrics["system_errors"] += 1

        error_entry = {
            "timestamp": datetime.now().isoformat(),
            "type": error_type,
            "message": error_message
        }

        self.error_log.append(error_entry)

        # 保持错误日志在合理范围内
        if len(self.error_log) > 100:
            self.error_log = self.error_log[-50:]

        logger.debug(f"记录系统错误: {error_type} - {error_message}")

    def _update_avg_time(self, metric_type: str, duration: float):
        """更新平均时间指标"""
        if metric_type == "recognition":
            current_avg = self.metrics["avg_recognition_time"]
            total_requests = self.metrics["total_requests"]
            self.metrics["avg_recognition_time"] = (current_avg * (total_requests - 1) + duration) / total_requests
        elif metric_type == "tts":
            current_avg = self.metrics["avg_tts_time"]
            total_requests = self.metrics["tts_requests"]
            self.metrics["avg_tts_time"] = (current_avg * (total_requests - 1) + duration) / total_requests

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        uptime = (datetime.now() - self.start_time).total_seconds()

        # 计算成功率
        recognition_success_rate = (
            self.metrics["successful_recognitions"] / max(self.metrics["total_requests"], 1) * 100
        )

        funasr_success_rate = (
            self.metrics["funasr_successes"] / max(self.metrics["funasr_requests"], 1) * 100
        )

        whisper_success_rate = (
            self.metrics["whisper_successes"] / max(self.metrics["whisper_requests"], 1) * 100
        )

        tts_success_rate = (
            self.metrics["tts_successes"] / max(self.metrics["tts_requests"], 1) * 100
        )

        false_detection_rate = (
            self.metrics["false_wake_detections"] / max(self.metrics["wake_word_detections"], 1) * 100
        )

        return {
            "uptime_seconds": uptime,
            "uptime_formatted": str(timedelta(seconds=int(uptime))),
            "total_requests": self.metrics["total_requests"],
            "recognition_success_rate": round(recognition_success_rate, 2),
            "funasr_success_rate": round(funasr_success_rate, 2),
            "whisper_success_rate": round(whisper_success_rate, 2),
            "tts_success_rate": round(tts_success_rate, 2),
            "avg_recognition_time": round(self.metrics["avg_recognition_time"], 3),
            "avg_tts_time": round(self.metrics["avg_tts_time"], 3),
            "wake_word_detections": self.metrics["wake_word_detections"],
            "false_detection_rate": round(false_detection_rate, 2),
            "system_errors": self.metrics["system_errors"],
            "requests_per_minute": round(self.metrics["total_requests"] / (uptime / 60), 2) if uptime > 0 else 0
        }

    def get_recent_errors(self, limit: int = 10) -> List[Dict[str, Any]]:
        """获取最近的错误"""
        return self.error_log[-limit:] if self.error_log else []

    def reset_metrics(self):
        """重置性能指标"""
        self.start_time = datetime.now()
        self.metrics = {k: 0 if isinstance(v, (int, float)) else v for k, v in self.metrics.items()}
        self.performance_history.clear()
        self.error_log.clear()

        logger.info("性能指标已重置")

# 全局监控器实例（延迟初始化）
performance_monitor = None

def get_performance_monitor():
    """获取性能监控器实例（延迟初始化）"""
    global performance_monitor
    if performance_monitor is None:
        performance_monitor = VoiceAssistantMonitor()
    return performance_monitor
