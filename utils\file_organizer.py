#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件整理工具
自动将项目中的文件按类型和功能进行归类整理
"""

import os
import shutil
import glob
from pathlib import Path
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class FileOrganizer:
    def __init__(self, project_root="."):
        self.project_root = Path(project_root).resolve()
        self.rules = self._define_organization_rules()
        
    def _define_organization_rules(self):
        """定义文件整理规则"""
        return {
            # 文档文件规则
            'docs': {
                'patterns': ['*.md', '*.txt', '*.rst'],
                'exclude_patterns': ['README.md', 'requirements.txt'],
                'target_dir': 'docs',
                'description': '文档文件'
            },
            
            # 测试文件规则
            'tests': {
                'patterns': ['test_*.py', '*_test.py', 'test*.py'],
                'exclude_patterns': [],
                'target_dir': 'test',
                'description': '测试文件'
            },
            
            # 临时音频文件规则
            'temp_audio': {
                'patterns': ['temp_command_*.wav', 'temp_*.wav'],
                'exclude_patterns': [],
                'target_dir': 'temp',
                'description': '临时音频文件'
            },
            
            # 语音文件规则
            'voice_files': {
                'patterns': ['tts_*.mp3', '*.wav'],
                'exclude_patterns': ['temp_*.wav'],
                'target_dir': 'voice',
                'description': '语音文件'
            },
            
            # 日志文件规则
            'logs': {
                'patterns': ['*.log'],
                'exclude_patterns': [],
                'target_dir': 'logs',
                'description': '日志文件'
            },
            
            # 配置文件规则
            'configs': {
                'patterns': ['*.yaml', '*.yml', '*.json', '*.ini', '*.cfg'],
                'exclude_patterns': ['package.json', 'package-lock.json'],
                'target_dir': 'configs',
                'description': '配置文件'
            }
        }
    
    def create_directories(self):
        """创建必要的目录结构"""
        directories = [
            'docs',
            'test', 
            'temp',
            'voice',
            'logs',
            'configs',
            'web/templates/test',
            'web/static/css',
            'web/static/js',
            'web/static/assets'
        ]
        
        for dir_path in directories:
            full_path = self.project_root / dir_path
            full_path.mkdir(parents=True, exist_ok=True)
            logger.info(f"确保目录存在: {full_path}")
    
    def find_files_by_pattern(self, patterns, exclude_patterns=None):
        """根据模式查找文件"""
        if exclude_patterns is None:
            exclude_patterns = []
            
        found_files = []
        
        for pattern in patterns:
            files = list(self.project_root.glob(pattern))
            for file_path in files:
                # 检查是否在排除列表中
                should_exclude = False
                for exclude_pattern in exclude_patterns:
                    if file_path.match(exclude_pattern):
                        should_exclude = True
                        break
                
                if not should_exclude and file_path.is_file():
                    found_files.append(file_path)
        
        return found_files
    
    def move_file(self, source_path, target_dir):
        """移动文件到目标目录"""
        target_path = self.project_root / target_dir
        target_path.mkdir(parents=True, exist_ok=True)
        
        destination = target_path / source_path.name
        
        try:
            # 如果目标文件已存在，添加序号
            counter = 1
            original_destination = destination
            while destination.exists():
                stem = original_destination.stem
                suffix = original_destination.suffix
                destination = target_path / f"{stem}_{counter}{suffix}"
                counter += 1
            
            shutil.move(str(source_path), str(destination))
            logger.info(f"移动文件: {source_path} → {destination}")
            return True
            
        except Exception as e:
            logger.error(f"移动文件失败: {source_path} → {destination}, 错误: {e}")
            return False
    
    def organize_files(self, dry_run=False):
        """整理文件"""
        logger.info(f"开始整理项目文件 (项目根目录: {self.project_root})")
        
        if not dry_run:
            self.create_directories()
        
        total_moved = 0
        
        for rule_name, rule_config in self.rules.items():
            logger.info(f"\n处理规则: {rule_name} - {rule_config['description']}")
            
            files = self.find_files_by_pattern(
                rule_config['patterns'], 
                rule_config.get('exclude_patterns', [])
            )
            
            if not files:
                logger.info(f"  未找到匹配的文件")
                continue
            
            logger.info(f"  找到 {len(files)} 个文件")
            
            for file_path in files:
                if dry_run:
                    logger.info(f"  [预览] 将移动: {file_path} → {rule_config['target_dir']}/")
                else:
                    if self.move_file(file_path, rule_config['target_dir']):
                        total_moved += 1
        
        if dry_run:
            logger.info(f"\n预览完成，共找到可整理的文件")
        else:
            logger.info(f"\n文件整理完成，共移动 {total_moved} 个文件")
    
    def clean_empty_directories(self):
        """清理空目录"""
        logger.info("清理空目录...")
        
        for root, dirs, files in os.walk(self.project_root, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                try:
                    if not any(dir_path.iterdir()):  # 目录为空
                        dir_path.rmdir()
                        logger.info(f"删除空目录: {dir_path}")
                except OSError:
                    pass  # 目录不为空或无法删除
    
    def generate_report(self):
        """生成整理报告"""
        report_path = self.project_root / "docs" / "FILE_ORGANIZATION_REPORT.md"
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# 文件整理报告\n\n")
            f.write(f"项目根目录: {self.project_root}\n")
            f.write(f"整理时间: {logging.Formatter().formatTime(logging.LogRecord('', 0, '', 0, '', (), None))}\n\n")
            
            f.write("## 整理规则\n\n")
            for rule_name, rule_config in self.rules.items():
                f.write(f"### {rule_name}\n")
                f.write(f"- 描述: {rule_config['description']}\n")
                f.write(f"- 目标目录: `{rule_config['target_dir']}/`\n")
                f.write(f"- 匹配模式: {', '.join(rule_config['patterns'])}\n")
                if rule_config.get('exclude_patterns'):
                    f.write(f"- 排除模式: {', '.join(rule_config['exclude_patterns'])}\n")
                f.write("\n")
            
            f.write("## 目录结构\n\n")
            f.write("```\n")
            for root, dirs, files in os.walk(self.project_root):
                level = root.replace(str(self.project_root), '').count(os.sep)
                indent = ' ' * 2 * level
                f.write(f"{indent}{os.path.basename(root)}/\n")
                subindent = ' ' * 2 * (level + 1)
                for file in files[:5]:  # 只显示前5个文件
                    f.write(f"{subindent}{file}\n")
                if len(files) > 5:
                    f.write(f"{subindent}... 还有 {len(files) - 5} 个文件\n")
            f.write("```\n")
        
        logger.info(f"生成整理报告: {report_path}")

def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='项目文件整理工具')
    parser.add_argument('--dry-run', action='store_true', help='预览模式，不实际移动文件')
    parser.add_argument('--clean', action='store_true', help='清理空目录')
    parser.add_argument('--report', action='store_true', help='生成整理报告')
    parser.add_argument('--project-root', default='.', help='项目根目录路径')
    
    args = parser.parse_args()
    
    organizer = FileOrganizer(args.project_root)
    
    if args.dry_run:
        logger.info("=== 预览模式 ===")
        organizer.organize_files(dry_run=True)
    else:
        organizer.organize_files(dry_run=False)
    
    if args.clean:
        organizer.clean_empty_directories()
    
    if args.report:
        organizer.generate_report()

if __name__ == "__main__":
    main()
