#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
全局对话历史管理器
确保对话历史能够在不同的语音助手实例之间共享和持久化
"""

import json
import os
import logging
from datetime import datetime
from typing import List, Dict, Any
from threading import Lock

logger = logging.getLogger(__name__)

class GlobalConversationManager:
    """全局对话历史管理器"""
    
    def __init__(self):
        self.history_file = "data/voice/conversation_history.json"
        self.conversation_history = []
        self.lock = Lock()
        
        # 确保目录存在
        os.makedirs("data/voice", exist_ok=True)
        
        # 加载现有历史
        self.load_history()
    
    def load_history(self):
        """从文件加载对话历史"""
        try:
            if os.path.exists(self.history_file):
                with open(self.history_file, 'r', encoding='utf-8') as f:
                    self.conversation_history = json.load(f)
                logger.info(f"加载了 {len(self.conversation_history)} 条对话记录")
            else:
                self.conversation_history = []
                logger.info("没有找到历史文件，创建新的对话历史")
        except Exception as e:
            logger.error(f"加载对话历史失败: {e}")
            self.conversation_history = []
    
    def save_history(self):
        """保存对话历史到文件"""
        try:
            with self.lock:
                with open(self.history_file, 'w', encoding='utf-8') as f:
                    json.dump(self.conversation_history, f, ensure_ascii=False, indent=2)
                logger.debug(f"对话历史已保存到 {self.history_file}")
        except Exception as e:
            logger.error(f"保存对话历史失败: {e}")
    
    def add_message(self, role: str, content: str, metadata: Dict[str, Any] = None):
        """添加对话消息"""
        message = {
            "role": role,
            "content": content,
            "timestamp": datetime.now().isoformat(),
            "metadata": metadata or {}
        }

        with self.lock:
            self.conversation_history.append(message)

            # 保持历史在合理范围内（最多100条）
            if len(self.conversation_history) > 100:
                self.conversation_history = self.conversation_history[-100:]

        # 保存到文件
        self.save_history()

        # 通过WebSocket推送新消息到前端
        self._broadcast_conversation_message(message)

        logger.info(f"添加对话消息: {role} - {content[:50]}...")

    def _broadcast_conversation_message(self, message: Dict[str, Any]):
        """通过WebSocket广播对话消息到前端"""
        try:
            # 导入AlertService来复用WebSocket连接
            import asyncio
            from src.video.video_server import AlertService

            # 创建对话消息格式
            conversation_data = {
                "type": "conversation",
                "message": message,
                "timestamp": datetime.now().isoformat()
            }

            # 异步广播消息
            asyncio.create_task(AlertService.notify(conversation_data))
            logger.debug(f"广播对话消息: {message['role']} - {message['content'][:30]}...")

        except Exception as e:
            logger.warning(f"广播对话消息失败: {e}")
    
    def get_history(self) -> List[Dict[str, Any]]:
        """获取对话历史"""
        with self.lock:
            return self.conversation_history.copy()
    
    def clear_history(self):
        """清空对话历史"""
        with self.lock:
            self.conversation_history = []
        
        self.save_history()
        logger.info("对话历史已清空")
    
    def get_stats(self) -> Dict[str, int]:
        """获取对话统计"""
        with self.lock:
            user_count = sum(1 for msg in self.conversation_history if msg.get('role') == 'user')
            assistant_count = sum(1 for msg in self.conversation_history if msg.get('role') == 'assistant')
            
            return {
                "total": len(self.conversation_history),
                "user": user_count,
                "assistant": assistant_count
            }

# 全局实例
global_conversation_manager = GlobalConversationManager()

def add_conversation_message(role: str, content: str, metadata: Dict[str, Any] = None):
    """添加对话消息的便捷函数"""
    global_conversation_manager.add_message(role, content, metadata)

def get_conversation_history() -> List[Dict[str, Any]]:
    """获取对话历史的便捷函数"""
    return global_conversation_manager.get_history()

def clear_conversation_history():
    """清空对话历史的便捷函数"""
    global_conversation_manager.clear_history()

def get_conversation_stats() -> Dict[str, int]:
    """获取对话统计的便捷函数"""
    return global_conversation_manager.get_stats()
