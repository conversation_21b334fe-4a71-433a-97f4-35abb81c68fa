# 语音助手功能说明

## 概述

AI安全监控系统集成了智能语音助手功能，支持语音唤醒、语音识别、语音合成和智能对话，为用户提供便捷的语音交互体验。

## 功能特性

### 1. 语音唤醒
- **唤醒词**: 支持多个唤醒词（"小凯小凯"、"你好小凯"、"小凯同学"、"小凯助手"、"嘿小凯"）
- **连续监听**: 后台持续监听，无需手动激活
- **智能检测**: 基于能量检测和语音识别的双重检测机制

### 2. 语音识别
- **实时识别**: 支持实时语音转文字
- **中文优化**: 针对中文语音进行优化
- **多种后端**: 支持Whisper和模拟识别模式

### 3. 语音合成
- **自然语音**: 使用Edge-TTS生成自然流畅的中文语音
- **多种音色**: 支持不同的语音音色选择
- **实时播放**: 即时语音反馈

### 4. 智能对话
- **上下文理解**: 维护对话上下文，支持多轮对话
- **系统集成**: 与监控系统深度集成，可查询警报、统计等信息
- **命令执行**: 支持系统控制命令

## 支持的命令

### 查询类命令
- "查询今天的警报"
- "显示统计信息"
- "查看最近的异常检测"
- "显示系统状态"

### 控制类命令
- "开始监控"
- "停止监控"
- "切换视频源"
- "调整检测灵敏度"

### 帮助类命令
- "帮助"
- "功能介绍"
- "使用说明"

### 退出命令
- "退出"
- "再见"
- "结束对话"

## 技术架构

### 音频处理
- **多后端支持**: PyAudio、SoundDevice、模拟模式
- **自动降级**: 设备不可用时自动切换到模拟模式
- **设备检测**: 智能检测和选择最佳音频设备

### 语音识别
- **Whisper集成**: 使用OpenAI Whisper进行高精度语音识别
- **模拟模式**: 在Whisper不可用时提供模拟识别功能
- **异步处理**: 异步语音识别，不阻塞主线程

### 语音合成
- **Edge-TTS**: 使用微软Edge-TTS进行语音合成
- **缓存机制**: 临时文件管理和自动清理
- **播放控制**: 支持播放状态监控和控制

## 配置说明

### 音频配置
```python
# voice_config.py
SAMPLE_RATE = 16000      # 采样率
CHUNK_SIZE = 1024        # 音频块大小
CHANNELS = 1             # 声道数
FORMAT = 'int16'         # 音频格式
```

### 唤醒词配置
```python
WAKE_WORDS = ["小助手", "AI助手", "语音助手", "智能助手"]
WAKE_THRESHOLD = 0.7     # 唤醒阈值
```

### 录音配置
```python
RECORD_TIMEOUT = 5       # 录音超时时间(秒)
SILENCE_THRESHOLD = 500  # 静音阈值
SILENCE_DURATION = 2     # 静音持续时间(秒)
```

### TTS配置
```python
TTS_VOICE = "zh-CN-XiaoxiaoNeural"  # 中文女声
TTS_RATE = "+0%"         # 语速
TTS_VOLUME = "+0%"       # 音量
```

## 使用方法

### 1. 启动语音助手
```python
# 通过API启动
POST /api/voice/control
{
    "action": "start"
}
```

### 2. Web界面控制
- 访问 `/ui/voice_assistant.html`
- 点击"启动语音助手"按钮
- 系统开始监听唤醒词

### 3. 语音交互流程
1. 说出唤醒词（如"小助手"）
2. 听到提示音后开始说话
3. 系统识别语音并执行相应操作
4. 语音播报执行结果

## API接口

### 控制接口
```http
POST /api/voice/control
Content-Type: application/json

{
    "action": "start|stop"
}
```

### 状态查询
```http
GET /api/voice/status
```

### 语音播报
```http
POST /api/voice/speak
Content-Type: application/json

{
    "text": "要播报的文本内容"
}
```

## 故障排除

### 音频设备问题
- **错误**: `OSError: [Errno -9996] Invalid input device`
- **解决**: 系统会自动切换到SoundDevice或模拟模式
- **建议**: 检查Windows音频设置和麦克风权限

### 语音识别问题
- **错误**: Whisper模型加载失败
- **解决**: 系统自动切换到模拟识别模式
- **建议**: 安装whisper依赖包

### 语音合成问题
- **错误**: Edge-TTS不可用
- **解决**: 系统切换到模拟播放模式
- **建议**: 检查网络连接和依赖安装

## 依赖安装

### 必需依赖
```bash
pip install sounddevice numpy
```

### 可选依赖
```bash
pip install openai-whisper  # 语音识别
pip install edge-tts        # 语音合成
pip install pygame          # 音频播放
```

## 注意事项

1. **隐私保护**: 语音数据仅在本地处理，不会上传到外部服务器
2. **性能影响**: 语音功能会占用一定的CPU和内存资源
3. **网络要求**: 语音合成功能需要网络连接
4. **设备兼容**: 需要麦克风和扬声器设备支持
5. **权限设置**: 确保应用有麦克风访问权限

## 更新日志

### v1.2.0
- 添加多音频后端支持
- 实现自动设备检测和选择
- 添加模拟模式降级机制
- 改进错误处理和容错能力

### v1.1.0
- 集成Whisper语音识别
- 添加Edge-TTS语音合成
- 实现多轮对话功能

### v1.0.0
- 基础语音唤醒功能
- 简单命令识别
- 基础语音播报
