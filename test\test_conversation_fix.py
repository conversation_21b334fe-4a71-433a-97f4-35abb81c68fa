#!/usr/bin/env python3
"""
测试对话记录修复是否有效
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_global_conversation_manager():
    """测试全局对话管理器"""
    print("测试全局对话管理器...")
    print("=" * 50)
    
    try:
        from src.voice.conversation_manager import (
            add_conversation_message, 
            get_conversation_history, 
            clear_conversation_history,
            get_conversation_stats
        )
        
        # 清空现有历史
        clear_conversation_history()
        print("✅ 清空现有历史")
        
        # 添加测试对话
        add_conversation_message("user", "你好，我想查询今天的警报", {"confidence": 0.95})
        add_conversation_message("assistant", "您好！今天共有3个警报事件，都已处理完毕", {"response_time": 1.2})
        add_conversation_message("user", "能详细说明一下吗？", {"confidence": 0.88})
        add_conversation_message("assistant", "当然可以。第一个是入侵检测警报...", {"response_time": 0.8})
        
        print("✅ 添加测试对话")
        
        # 获取对话历史
        history = get_conversation_history()
        print(f"✅ 获取到 {len(history)} 条对话记录")
        
        # 显示对话内容
        for i, msg in enumerate(history):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            timestamp = msg.get('timestamp', '')
            print(f"   {i+1}. [{role}] {content[:30]}... ({timestamp[:19]})")
        
        # 获取统计信息
        stats = get_conversation_stats()
        print(f"✅ 统计信息: 总计={stats['total']}, 用户={stats['user']}, 助手={stats['assistant']}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_file_persistence():
    """测试文件持久化"""
    print("\n测试文件持久化...")
    print("=" * 50)
    
    try:
        import json
        
        # 检查文件是否存在
        history_file = "data/voice/conversation_history.json"
        if os.path.exists(history_file):
            with open(history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            print(f"✅ 文件存在: {history_file}")
            print(f"✅ 文件中有 {len(data)} 条记录")
            
            # 显示最后几条记录
            for i, msg in enumerate(data[-3:]):
                role = msg.get('role', 'unknown')
                content = msg.get('content', '')
                print(f"   {i+1}. [{role}] {content[:30]}...")
            
            return True
        else:
            print(f"❌ 文件不存在: {history_file}")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_api_integration():
    """测试API集成"""
    print("\n测试API集成...")
    print("=" * 50)
    
    try:
        import requests
        import time
        
        # 等待服务器启动
        print("等待服务器启动...")
        time.sleep(2)
        
        # 测试对话历史API
        response = requests.get("http://127.0.0.1:16532/api/voice/conversation-history", timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            history = data.get('data', {}).get('history', [])
            
            print(f"✅ API调用成功")
            print(f"✅ 返回 {len(history)} 条对话记录")
            
            if len(history) > 0:
                print("✅ 对话记录内容:")
                for i, msg in enumerate(history[-3:]):
                    role = msg.get('role', 'unknown')
                    content = msg.get('content', '')
                    print(f"   {i+1}. [{role}] {content[:30]}...")
                
                return True
            else:
                print("⚠️ 没有对话记录")
                return False
        else:
            print(f"❌ API调用失败: {response.status_code}")
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        return False
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def main():
    print("对话记录修复测试")
    print("=" * 80)
    
    # 测试1: 全局对话管理器
    test1 = test_global_conversation_manager()
    
    # 测试2: 文件持久化
    test2 = test_file_persistence()
    
    # 测试3: API集成
    test3 = test_api_integration()
    
    print("\n" + "=" * 80)
    
    if test1 and test2:
        print("🎉 基础功能测试通过!")
        
        if test3:
            print("🎉 API集成测试也通过!")
            print("\n✅ 修复完成! 现在对话记录应该能正常显示了")
            
            print("\n📋 使用说明:")
            print("1. 启动主服务器: python main.py")
            print("2. 与语音助手进行对话")
            print("3. 在浏览器中查看 http://127.0.0.1:16532")
            print("4. 对话记录区域会实时显示对话内容")
            
        else:
            print("⚠️ API集成测试失败，但基础功能正常")
            print("请确保主服务器正在运行")
    else:
        print("❌ 基础功能测试失败")
        print("请检查代码实现")

if __name__ == "__main__":
    main()
