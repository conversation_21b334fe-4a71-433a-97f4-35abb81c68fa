#!/usr/bin/env python3
"""
测试模型配置是否正确应用
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_model_configuration():
    """测试模型配置"""
    print("测试模型配置...")
    print("=" * 50)
    
    try:
        # 1. 测试YAML配置加载
        from src.core.config_loader import config_loader
        current_model = config_loader.get_current_model()
        model_names = config_loader.get_model_names()
        
        print(f"1. YAML配置:")
        print(f"   当前模型: {current_model['name']}")
        print(f"   模型描述: {model_names.get(current_model['name'], '未知')}")
        
        # 2. 测试多模态分析器配置获取
        from src.models.multi_modal_analyzer import MultiModalAnalyzer
        analyzer = MultiModalAnalyzer()
        
        print(f"\n2. 多模态分析器配置:")
        surveillance_items, alert_names = analyzer._get_surveillance_config()
        print(f"   监控项目: {surveillance_items}")
        print(f"   警报名称: {alert_names}")
        
        # 3. 检查是否匹配
        expected_model = current_model['name']
        expected_display = model_names.get(expected_model, expected_model)
        
        print(f"\n3. 配置匹配检查:")
        print(f"   期望模型: {expected_model}")
        print(f"   期望显示: {expected_display}")
        
        if alert_names == expected_model:
            print("   ✅ 模型配置匹配")
        else:
            print(f"   ❌ 模型配置不匹配: 期望 {expected_model}, 实际 {alert_names}")
        
        if expected_display in surveillance_items:
            print("   ✅ 监控项目匹配")
        else:
            print(f"   ❌ 监控项目不匹配: 期望包含 {expected_display}, 实际 {surveillance_items}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_video_server_initialization():
    """测试video_server初始化"""
    print("\n测试video_server初始化...")
    print("-" * 30)
    
    try:
        # 模拟video_server的初始化过程
        from src.core.config_loader import config_loader
        from src.core.config_adapter import MODEL_NAMES
        
        # 加载YAML配置
        current_model = config_loader.get_current_model()
        MODEL_NAMES.update(config_loader.get_model_names())
        
        # 初始化算法设置
        current_model_name = current_model['name']
        current_model_display = MODEL_NAMES.get(current_model_name, current_model_name)
        current_algorithms = [current_model_display]
        current_alert_names = current_model_name
        
        print(f"初始化结果:")
        print(f"   current_algorithms: {current_algorithms}")
        print(f"   current_alert_names: {current_alert_names}")
        
        # 验证
        if current_model_name == "intrusion" and "入侵检测" in current_algorithms:
            print("   ✅ video_server初始化正确")
            return True
        else:
            print("   ❌ video_server初始化不正确")
            return False
        
    except Exception as e:
        print(f"❌ video_server初始化测试失败: {e}")
        return False

def main():
    print("模型配置测试")
    print("=" * 60)
    
    success1 = test_model_configuration()
    success2 = test_video_server_initialization()
    
    print("\n" + "=" * 60)
    if success1 and success2:
        print("🎉 所有测试通过!")
        print("\n现在系统应该:")
        print("- 使用 intrusion (入侵检测) 模型")
        print("- 不再显示 '通用异常检测'")
        print("- 正确应用YAML配置")
    else:
        print("❌ 部分测试失败")
    
    print("\n重启系统以应用修复:")
    print("python main.py")

if __name__ == "__main__":
    main()
