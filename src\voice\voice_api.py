#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
语音助手API接口
提供Web接口控制语音功能
"""

import asyncio
import json
from datetime import datetime
from typing import Dict, Optional
import logging

from fastapi import APIRouter, HTTPException, UploadFile, File
from fastapi.responses import JSONResponse
from pydantic import BaseModel

from src.voice.voice_controller import (
    start_voice_assistant, stop_voice_assistant,
    trigger_voice_alert, voice_assistant
)
from src.voice.conversation_manager import get_conversation_history, clear_conversation_history

logger = logging.getLogger(__name__)

# 创建路由器
voice_router = APIRouter(prefix="/api/voice", tags=["语音助手"])

# 请求模型
class VoiceControlRequest(BaseModel):
    action: str  # start, stop, status
    
class VoiceSpeakRequest(BaseModel):
    text: str
    voice: Optional[str] = None

class VoiceQueryRequest(BaseModel):
    text: str
    use_context: bool = True

# 全局状态
voice_status = {
    "active": False,
    "listening": False,
    "conversation_active": False,
    "last_interaction": None
}

@voice_router.post("/control")
async def control_voice_assistant(request: VoiceControlRequest):
    """控制语音助手启停"""
    try:
        if request.action == "start":
            if voice_status["active"]:
                return JSONResponse({
                    "status": "success",
                    "message": "语音助手已经在运行",
                    "data": voice_status
                })
            
            # 启动语音助手（在后台任务中运行）
            asyncio.create_task(start_voice_assistant())
            voice_status["active"] = True
            voice_status["last_interaction"] = datetime.now().isoformat()
            
            return JSONResponse({
                "status": "success",
                "message": "语音助手启动成功",
                "data": voice_status
            })
        
        elif request.action == "stop":
            if not voice_status["active"]:
                return JSONResponse({
                    "status": "success",
                    "message": "语音助手未在运行",
                    "data": voice_status
                })
            
            await stop_voice_assistant()
            voice_status["active"] = False
            voice_status["listening"] = False
            voice_status["conversation_active"] = False
            voice_status["last_interaction"] = datetime.now().isoformat()
            
            return JSONResponse({
                "status": "success",
                "message": "语音助手停止成功",
                "data": voice_status
            })
        
        elif request.action == "status":
            # 更新状态
            if voice_assistant:
                voice_status["active"] = voice_assistant.is_active
                voice_status["listening"] = voice_assistant.is_listening
                voice_status["conversation_active"] = voice_assistant.conversation_active
            voice_status["is_sleeping"] = getattr(voice_assistant, 'is_sleeping', False)
            voice_status["last_interaction"] = getattr(voice_assistant, 'last_interaction_time', None)
            
            return JSONResponse({
                "status": "success",
                "message": "状态查询成功",
                "data": voice_status
            })
        
        else:
            raise HTTPException(status_code=400, detail="无效的操作类型")
    
    except Exception as e:
        logger.error(f"语音助手控制失败: {e}")
        raise HTTPException(status_code=500, detail=f"操作失败: {str(e)}")

@voice_router.post("/speak")
async def speak_text(request: VoiceSpeakRequest):
    """文本转语音播放"""
    try:
        if not voice_assistant or not voice_assistant.is_active:
            raise HTTPException(status_code=400, detail="语音助手未启动")
        
        # 播放文本
        success = await voice_assistant.tts.speak(request.text, request.voice)
        
        if success:
            return JSONResponse({
                "status": "success",
                "message": "语音播放成功",
                "data": {
                    "text": request.text,
                    "voice": request.voice,
                    "timestamp": datetime.now().isoformat()
                }
            })
        else:
            raise HTTPException(status_code=500, detail="语音播放失败")
    
    except Exception as e:
        logger.error(f"语音播放失败: {e}")
        raise HTTPException(status_code=500, detail=f"播放失败: {str(e)}")

@voice_router.post("/query")
async def voice_query(request: VoiceQueryRequest):
    """文本查询（模拟语音对话）"""
    try:
        if not voice_assistant or not voice_assistant.is_active:
            raise HTTPException(status_code=400, detail="语音助手未启动")
        
        # 处理查询
        if request.use_context:
            voice_assistant.dialogue_manager.add_message("user", request.text)
        
        response = await voice_assistant._process_command(request.text)
        
        if request.use_context:
            voice_assistant.dialogue_manager.add_message("assistant", response)
        
        return JSONResponse({
            "status": "success",
            "message": "查询成功",
            "data": {
                "query": request.text,
                "response": response,
                "timestamp": datetime.now().isoformat(),
                "context_used": request.use_context
            }
        })
    
    except Exception as e:
        logger.error(f"语音查询失败: {e}")
        raise HTTPException(status_code=500, detail=f"查询失败: {str(e)}")

@voice_router.post("/upload-audio")
async def upload_audio_for_recognition(file: UploadFile = File(...)):
    """上传音频文件进行语音识别"""
    try:
        if not voice_assistant or not voice_assistant.is_active:
            raise HTTPException(status_code=400, detail="语音助手未启动")
        
        # 检查文件类型
        if not file.content_type.startswith('audio/'):
            raise HTTPException(status_code=400, detail="请上传音频文件")
        
        # 保存临时文件
        import tempfile
        import os
        
        with tempfile.NamedTemporaryFile(delete=False, suffix='.wav') as temp_file:
            content = await file.read()
            temp_file.write(content)
            temp_filename = temp_file.name
        
        try:
            # 语音识别
            recognized_text = await voice_assistant.speech_recognizer.recognize(temp_filename)
            
            if recognized_text:
                # 处理识别的文本
                response = await voice_assistant._process_command(recognized_text)
                
                return JSONResponse({
                    "status": "success",
                    "message": "语音识别成功",
                    "data": {
                        "recognized_text": recognized_text,
                        "response": response,
                        "timestamp": datetime.now().isoformat()
                    }
                })
            else:
                return JSONResponse({
                    "status": "warning",
                    "message": "未识别到有效语音",
                    "data": {
                        "recognized_text": "",
                        "timestamp": datetime.now().isoformat()
                    }
                })
        
        finally:
            # 清理临时文件
            try:
                os.unlink(temp_filename)
            except:
                pass
    
    except Exception as e:
        logger.error(f"音频识别失败: {e}")
        raise HTTPException(status_code=500, detail=f"识别失败: {str(e)}")

@voice_router.post("/alert")
async def trigger_alert_broadcast(alert_text: str):
    """触发警报语音播报"""
    try:
        await trigger_voice_alert(alert_text)
        
        return JSONResponse({
            "status": "success",
            "message": "警报播报成功",
            "data": {
                "alert_text": alert_text,
                "timestamp": datetime.now().isoformat()
            }
        })
    
    except Exception as e:
        logger.error(f"警报播报失败: {e}")
        raise HTTPException(status_code=500, detail=f"播报失败: {str(e)}")

@voice_router.get("/conversation-history")
async def get_conversation_history_api():
    """获取对话历史"""
    try:
        # 使用全局对话管理器获取历史
        history = get_conversation_history()

        logger.info(f"获取到 {len(history)} 条对话记录")

        return JSONResponse({
            "status": "success",
            "message": "获取对话历史成功",
            "data": {
                "history": history,
                "count": len(history),
                "timestamp": datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"获取对话历史失败: {e}")
        # 返回空历史而不是抛出异常
        return JSONResponse({
            "status": "success",
            "message": "获取对话历史失败，返回空历史",
            "data": {
                "history": [],
                "count": 0,
                "timestamp": datetime.now().isoformat()
            }
        })

@voice_router.delete("/conversation-history")
async def clear_conversation_history_api():
    """清空对话历史"""
    try:
        # 清空全局对话历史
        clear_conversation_history()

        # 如果语音助手实例存在，也清空其对话历史
        if voice_assistant and hasattr(voice_assistant, 'dialogue_manager'):
            voice_assistant.dialogue_manager.clear_context()

        return JSONResponse({
            "status": "success",
            "message": "对话历史已清空",
            "data": {
                "timestamp": datetime.now().isoformat()
            }
        })

    except Exception as e:
        logger.error(f"清空对话历史失败: {e}")
        raise HTTPException(status_code=500, detail=f"清空失败: {str(e)}")

@voice_router.get("/config")
async def get_voice_config():
    """获取语音配置"""
    from voice_assistant import VoiceConfig
    
    return JSONResponse({
        "status": "success",
        "message": "获取配置成功",
        "data": {
            "wake_words": VoiceConfig.WAKE_WORDS,
            "sample_rate": VoiceConfig.SAMPLE_RATE,
            "record_timeout": VoiceConfig.RECORD_TIMEOUT,
            "tts_voice": VoiceConfig.TTS_VOICE,
            "silence_threshold": VoiceConfig.SILENCE_THRESHOLD,
            "silence_duration": VoiceConfig.SILENCE_DURATION
        }
    })

@voice_router.get("/health")
async def voice_health_check():
    """语音助手健康检查"""
    try:
        health_status = {
            "voice_assistant_active": voice_status["active"],
            "components": {
                "wake_detector": False,
                "speech_recognizer": False,
                "tts": False,
                "audio_recorder": False
            },
            "timestamp": datetime.now().isoformat()
        }
        
        if voice_assistant:
            health_status["components"]["wake_detector"] = voice_assistant.wake_detector.whisper_model is not None
            health_status["components"]["speech_recognizer"] = voice_assistant.speech_recognizer.whisper_model is not None
            health_status["components"]["tts"] = True  # TTS通常总是可用的
            health_status["components"]["audio_recorder"] = voice_assistant.recorder is not None
        
        all_healthy = all(health_status["components"].values()) if voice_status["active"] else True
        
        return JSONResponse({
            "status": "healthy" if all_healthy else "degraded",
            "message": "健康检查完成",
            "data": health_status
        })
    
    except Exception as e:
        logger.error(f"健康检查失败: {e}")
        return JSONResponse({
            "status": "unhealthy",
            "message": f"健康检查失败: {str(e)}",
            "data": {
                "timestamp": datetime.now().isoformat()
            }
        })

@voice_router.get("/voice-files")
async def get_voice_files():
    """获取语音文件信息"""
    try:
        if not voice_assistant or not hasattr(voice_assistant, 'tts'):
            return JSONResponse({
                "status": "error",
                "message": "语音助手未启动",
                "data": {"count": 0, "total_size": 0, "files": []}
            })

        files_info = voice_assistant.tts.get_voice_files_info()

        return JSONResponse({
            "status": "success",
            "message": "获取语音文件信息成功",
            "data": files_info
        })

    except Exception as e:
        logger.error(f"获取语音文件信息失败: {e}")
        return JSONResponse({
            "status": "error",
            "message": str(e),
            "data": {"count": 0, "total_size": 0, "files": []}
        })

@voice_router.post("/cleanup-voice-files")
async def cleanup_voice_files():
    """清理语音文件"""
    try:
        if not voice_assistant or not hasattr(voice_assistant, 'tts'):
            raise HTTPException(status_code=400, detail="语音助手未启动")

        cleanup_count = voice_assistant.tts.cleanup_all_voice_files()

        return JSONResponse({
            "status": "success",
            "message": f"已清理 {cleanup_count} 个语音文件",
            "data": {"cleanup_count": cleanup_count}
        })

    except Exception as e:
        logger.error(f"清理语音文件失败: {e}")
        raise HTTPException(status_code=500, detail=f"清理失败: {str(e)}")
