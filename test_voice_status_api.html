<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>语音状态API测试</title>
    <script src="https://cdn.tailwindcss.com"></script>
</head>
<body class="bg-gray-900 text-white p-6">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-6">🎤 语音状态API测试</h1>
        
        <!-- 当前状态显示 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">当前状态</h2>
            <div class="grid grid-cols-1 gap-4">
                <div class="text-center">
                    <div class="text-sm text-gray-400">助手状态</div>
                    <div id="assistantStatus" class="px-2 py-1 rounded text-xs bg-red-600">未启动</div>
                </div>
            </div>
        </div>

        <!-- 控制按钮 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-6">
            <h2 class="text-xl font-semibold mb-4">控制操作</h2>
            <div class="flex flex-wrap gap-4">
                <button onclick="startVoiceAssistant()" class="bg-green-600 hover:bg-green-700 px-4 py-2 rounded">
                    启动语音助手
                </button>
                <button onclick="stopVoiceAssistant()" class="bg-red-600 hover:bg-red-700 px-4 py-2 rounded">
                    停止语音助手
                </button>
                <button onclick="refreshStatus()" class="bg-blue-600 hover:bg-blue-700 px-4 py-2 rounded">
                    刷新状态
                </button>
                <button onclick="toggleAutoRefresh()" id="autoRefreshBtn" class="bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded">
                    开启自动刷新
                </button>
            </div>
        </div>

        <!-- API响应日志 -->
        <div class="bg-gray-800 p-6 rounded-lg">
            <h2 class="text-xl font-semibold mb-4">API响应日志</h2>
            <div id="apiLog" class="bg-black p-4 rounded text-sm font-mono h-64 overflow-y-auto">
                <div class="text-gray-400">等待API调用...</div>
            </div>
            <button onclick="clearLog()" class="mt-4 bg-gray-600 hover:bg-gray-700 px-4 py-2 rounded text-sm">
                清空日志
            </button>
        </div>
    </div>

    <script>
        const host = window.location.hostname;
        const port = '16532';
        const baseUrl = `http://${host}:${port}`;
        
        let autoRefreshInterval = null;
        let isAutoRefreshing = false;

        // 添加日志
        function addLog(message, type = 'info') {
            const logDiv = document.getElementById('apiLog');
            const timestamp = new Date().toLocaleTimeString();
            const colorClass = type === 'error' ? 'text-red-400' : 
                              type === 'success' ? 'text-green-400' : 
                              type === 'warning' ? 'text-yellow-400' : 'text-blue-400';
            
            const logEntry = document.createElement('div');
            logEntry.className = `${colorClass} mb-1`;
            logEntry.innerHTML = `[${timestamp}] ${message}`;
            
            logDiv.appendChild(logEntry);
            logDiv.scrollTop = logDiv.scrollHeight;
        }

        // 更新状态显示
        function updateStatusUI(status) {
            addLog(`更新状态: ${JSON.stringify(status)}`, 'info');
            
            // 更新助手状态
            const assistantEl = document.getElementById('assistantStatus');
            if (assistantEl) {
                assistantEl.textContent = status.active ? '运行中' : '未启动';
                assistantEl.className = `px-2 py-1 rounded text-xs ${status.active ? 'bg-green-600' : 'bg-red-600'}`;
            }

            // 只保留助手状态，其他状态显示已移除
        }

        // 刷新状态
        async function refreshStatus() {
            try {
                addLog('正在获取语音状态...', 'info');
                
                const response = await fetch(`${baseUrl}/api/voice/status`);
                const result = await response.json();
                
                addLog(`API响应: ${JSON.stringify(result, null, 2)}`, 'success');
                
                if (result.status === 'success') {
                    updateStatusUI(result.data);
                } else {
                    addLog(`API错误: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`请求失败: ${error.message}`, 'error');
            }
        }

        // 启动语音助手
        async function startVoiceAssistant() {
            try {
                addLog('正在启动语音助手...', 'info');
                
                const response = await fetch(`${baseUrl}/api/voice/control`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'start' })
                });
                
                const result = await response.json();
                addLog(`启动响应: ${JSON.stringify(result, null, 2)}`, 'success');
                
                if (result.status === 'success') {
                    updateStatusUI(result.data);
                } else {
                    addLog(`启动失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`启动请求失败: ${error.message}`, 'error');
            }
        }

        // 停止语音助手
        async function stopVoiceAssistant() {
            try {
                addLog('正在停止语音助手...', 'info');
                
                const response = await fetch(`${baseUrl}/api/voice/control`, {
                    method: 'POST',
                    headers: { 'Content-Type': 'application/json' },
                    body: JSON.stringify({ action: 'stop' })
                });
                
                const result = await response.json();
                addLog(`停止响应: ${JSON.stringify(result, null, 2)}`, 'success');
                
                if (result.status === 'success') {
                    updateStatusUI(result.data);
                } else {
                    addLog(`停止失败: ${result.message}`, 'error');
                }
            } catch (error) {
                addLog(`停止请求失败: ${error.message}`, 'error');
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');
            
            if (isAutoRefreshing) {
                clearInterval(autoRefreshInterval);
                isAutoRefreshing = false;
                btn.textContent = '开启自动刷新';
                btn.className = 'bg-purple-600 hover:bg-purple-700 px-4 py-2 rounded';
                addLog('自动刷新已停止', 'warning');
            } else {
                autoRefreshInterval = setInterval(refreshStatus, 3000); // 每3秒刷新
                isAutoRefreshing = true;
                btn.textContent = '停止自动刷新';
                btn.className = 'bg-orange-600 hover:bg-orange-700 px-4 py-2 rounded';
                addLog('自动刷新已开启 (每3秒)', 'success');
            }
        }

        // 清空日志
        function clearLog() {
            document.getElementById('apiLog').innerHTML = '<div class="text-gray-400">日志已清空</div>';
        }

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            addLog('页面加载完成，开始初始化...', 'info');
            refreshStatus();
        });
    </script>
</body>
</html>
