#!/usr/bin/env python3
"""
测试video_server配置加载
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_video_server_imports():
    """测试video_server的导入和配置"""
    print("测试video_server配置加载...")
    print("=" * 50)
    
    try:
        # 模拟video_server的导入过程
        print("1. 导入配置模块...")
        from src.core.config_adapter import VideoConfig, ServerConfig, APIConfig, RAGConfig, LOG_CONFIG, ARCHIVE_DIR, update_config
        from src.core.config_loader import config_loader
        from src.core.config_adapter import MODEL_NAMES
        import src.core.config_adapter as config
        
        print("✅ 基础配置导入成功")
        
        print("2. 加载YAML配置...")
        try:
            VIDEO_SOURCE = config_loader.get_video_source()
            current_model = config_loader.get_current_model()
            MODEL_NAMES.update(config_loader.get_model_names())
            print(f"✅ 使用YAML配置: 视频源={VIDEO_SOURCE}, 当前模型={current_model['name']}")
        except Exception as e:
            print(f"⚠️ YAML配置加载失败，使用默认配置: {e}")
            from src.core.config_adapter import current_model, VIDEO_SOURCE
        
        print("3. 显示最终配置...")
        print(f"   📹 视频源: {VIDEO_SOURCE}")
        print(f"   🤖 当前模型: {current_model['name']}")
        print(f"   📝 模型描述: {MODEL_NAMES.get(current_model['name'], '未知模型')}")
        
        # 检查视频文件是否存在
        if os.path.exists(VIDEO_SOURCE):
            print(f"   ✅ 视频文件存在")
        else:
            print(f"   ❌ 视频文件不存在: {VIDEO_SOURCE}")
        
        return True
        
    except Exception as e:
        print(f"❌ 配置加载失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    print("Video Server 配置测试")
    print("=" * 60)
    
    success = test_video_server_imports()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 配置加载测试成功!")
        print("\n现在启动video_server应该使用:")
        print("- 视频源: test/test3.mp4")
        print("- 检测模型: intrusion (入侵检测)")
    else:
        print("❌ 配置加载测试失败")
    
    print("\n如果启动后仍显示旧配置，请检查:")
    print("1. YAML文件格式是否正确")
    print("2. 是否有缓存的配置")
    print("3. 重启系统")

if __name__ == "__main__":
    main()
