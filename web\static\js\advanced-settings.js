// 高级设置功能
window.addEventListener('load', function() {
  console.log('高级设置脚本开始执行');

  // 高级设置弹框控制
  const advancedModal = document.getElementById('advancedModal');
  const advancedSettingsBtn = document.getElementById('advancedSettings');
  const closeModalBtn = document.getElementById('closeModal');
  const cancelSettingsBtn = document.getElementById('cancelSettings');
  const confirmSettingsBtn = document.getElementById('confirmSettings');
  const scenarioTabs = document.querySelectorAll('.scenario-tab');
  const algorithmItems = document.querySelectorAll('.algorithm-item');

  console.log('高级设置元素检查:');
  console.log('- advancedModal:', advancedModal);
  console.log('- advancedSettingsBtn:', advancedSettingsBtn);
  console.log('- closeModalBtn:', closeModalBtn);

  if (!advancedSettingsBtn) {
    console.error('找不到高级设置按钮');
    return;
  }

  if (!advancedModal) {
    console.error('找不到高级设置弹框');
    return;
  }

  // 场景算法映射
  const scenarioAlgorithms = {
    all: [], // 显示所有算法
    industrial: ['helmet', 'uniform', 'reflective', 'smoking', 'phone', 'fire', 'dust', 'standard-operation', 'leave', 'sleep'],
    childcare: ['fall', 'stranger', 'overcrowd', 'intrusion', 'face'],
    elderly: ['fall', 'leave', 'sleep', 'stranger', 'face', 'intrusion'],
    pet: ['counting', 'intrusion', 'loitering'],
    'non-camera': ['dust', 'fire', 'open-fire'],
    custom: ['face', 'license-plate', 'counting', 'line-crossing']
  };

  // 打开高级设置弹框
  advancedSettingsBtn.addEventListener('click', function() {
    console.log('高级设置按钮被点击');
    advancedModal.classList.remove('hidden');

    // 每次打开弹框时重新加载自定义模型
    setTimeout(() => {
      if (typeof loadCustomModels === 'function') {
        loadCustomModels();
      }
    }, 100);
  });

  // 关闭弹框
  function closeModal() {
    console.log('关闭弹框');
    advancedModal.classList.add('hidden');
  }

  if (closeModalBtn) {
    closeModalBtn.addEventListener('click', closeModal);
  }

  if (cancelSettingsBtn) {
    cancelSettingsBtn.addEventListener('click', closeModal);
  }

  // 点击背景关闭弹框
  advancedModal.addEventListener('click', function(e) {
    if (e.target === advancedModal) {
      closeModal();
    }
  });

  // 场景切换
  scenarioTabs.forEach(tab => {
    tab.addEventListener('click', function() {
      // 更新选中状态
      scenarioTabs.forEach(t => {
        t.classList.remove('active', 'bg-blue-600');
        t.classList.add('bg-gray-600');
      });
      tab.classList.add('active', 'bg-blue-600');
      tab.classList.remove('bg-gray-600');

      // 过滤算法显示
      const scenario = tab.dataset.scenario;
      filterAlgorithms(scenario);

      // 显示/隐藏自定义模型添加区域
      const customModelSection = document.getElementById('customModelSection');
      if (scenario === 'custom') {
        customModelSection.classList.remove('hidden');
      } else {
        customModelSection.classList.add('hidden');
      }
    });
  });

  // 过滤算法显示
  function filterAlgorithms(scenario) {
    const allowedAlgorithms = scenarioAlgorithms[scenario];

    // 获取所有算法项（包括动态添加的自定义模型）
    const allAlgorithmItems = document.querySelectorAll('.algorithm-item');

    allAlgorithmItems.forEach(item => {
      const inputValue = item.querySelector('input').value;
      const isCustomModel = item.getAttribute('data-custom') === 'true';

      if (scenario === 'all') {
        // 显示所有算法
        item.style.display = 'flex';
      } else if (scenario === 'custom') {
        // 自定义模型场景：只显示自定义模型和一些基础算法
        if (isCustomModel || allowedAlgorithms.includes(inputValue)) {
          item.style.display = 'flex';
        } else {
          item.style.display = 'none';
        }
      } else {
        // 其他场景：显示预定义的算法
        if (allowedAlgorithms.includes(inputValue)) {
          item.style.display = 'flex';
        } else {
          item.style.display = 'none';
        }
      }
    });
  }

  // 确认设置
  if (confirmSettingsBtn) {
    confirmSettingsBtn.addEventListener('click', function() {
      const selectedAlgorithms = [];
      document.querySelectorAll('input[name="algorithms"]:checked').forEach(checkbox => {
        const parentElement = checkbox.parentElement;
        const spans = parentElement.querySelectorAll('span');
        // 获取第一个span的文本（算法名称），忽略[自定义]标签
        const algorithmName = spans[0].textContent;

        selectedAlgorithms.push({
          value: checkbox.value,
          name: algorithmName
        });
      });

      if (selectedAlgorithms.length === 0) {
        alert('请至少选择一个算法！');
        return;
      }

      // 保存选择的算法
      localStorage.setItem('selectedAlgorithms', JSON.stringify(selectedAlgorithms));

      // 更新主页面的算法标签显示
      updateAlgorithmTags(selectedAlgorithms);

      // 显示选择结果
      const algorithmNames = selectedAlgorithms.map(alg => alg.name).join('、');
      showNotification(`已选择算法：${algorithmNames}`, 'success');

      closeModal();
    });
  }

  // 自定义模型管理
  const customModelName = document.getElementById('customModelName');
  const addCustomModelBtn = document.getElementById('addCustomModel');
  const customModelList = document.getElementById('customModelList');
  let customModels = [];

  // 初始化自定义模型显示
  loadCustomModels();

  // 添加自定义模型
  if (addCustomModelBtn) {
    addCustomModelBtn.addEventListener('click', async function() {
      const modelName = customModelName.value.trim();
      if (!modelName) {
        alert('请输入模型名称！');
        return;
      }

      if (customModels.some(model => model.chinese_name === modelName)) {
        alert('模型名称已存在！');
        return;
      }

      try {
        // 调用后端API添加自定义模型
        const response = await fetch('/api/custom-models', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({
            name: modelName
          })
        });

        const result = await response.json();

        if (result.status === 'success') {
          customModelName.value = '';
          await loadCustomModels(); // 重新加载自定义模型列表
          addCustomModelToGrid(result.english_name, result.chinese_name); // 添加到算法网格
          showNotification(`已添加自定义模型：${modelName}`, 'success');
        } else {
          throw new Error(result.message || '添加失败');
        }
      } catch (error) {
        console.error('添加自定义模型失败:', error);
        showNotification(`添加失败: ${error.message}`, 'error');
      }
    });
  }

  // 加载自定义模型列表
  async function loadCustomModels() {
    try {
      const response = await fetch('/api/custom-models');
      const result = await response.json();

      if (result.status === 'success') {
        customModels = Object.entries(result.models).map(([englishName, modelInfo]) => ({
          english_name: englishName,
          chinese_name: modelInfo.chinese_name,
          keywords: modelInfo.keywords || [],
          created: modelInfo.created || new Date().toISOString()
        }));
        updateCustomModelList();
        addCustomModelsToGrid(); // 添加到算法网格
      }
    } catch (error) {
      console.error('加载自定义模型失败:', error);
      customModels = [];
      updateCustomModelList();
    }
  }

  // 更新自定义模型列表
  function updateCustomModelList() {
    if (!customModelList) return;

    if (customModels.length === 0) {
      customModelList.innerHTML = '<p class="text-gray-400 text-sm">暂无自定义模型</p>';
      return;
    }

    customModelList.innerHTML = customModels.map(model => `
      <div class="flex items-center justify-between p-2 bg-gray-700 rounded">
        <span class="text-sm">${model.chinese_name}</span>
        <button onclick="removeCustomModel('${model.english_name}')" class="text-red-400 hover:text-red-300">
          <i class="fas fa-trash text-xs"></i>
        </button>
      </div>
    `).join('');
  }

  // 删除自定义模型
  window.removeCustomModel = async function(englishName) {
    try {
      const response = await fetch(`/api/custom-models/${englishName}`, {
        method: 'DELETE'
      });

      const result = await response.json();

      if (result.status === 'success') {
        await loadCustomModels(); // 重新加载列表
        removeCustomModelFromGrid(englishName); // 从算法网格中移除
        showNotification('已删除自定义模型', 'success');
      } else {
        throw new Error(result.message || '删除失败');
      }
    } catch (error) {
      console.error('删除自定义模型失败:', error);
      showNotification(`删除失败: ${error.message}`, 'error');
    }
  };

  // 添加自定义模型到算法网格
  function addCustomModelToGrid(englishName, chineseName) {
    const algorithmGrid = document.getElementById('algorithmGrid');
    if (!algorithmGrid) return;

    // 检查是否已存在
    const existingItem = algorithmGrid.querySelector(`input[value="${englishName}"]`);
    if (existingItem) return;

    // 创建新的算法项
    const algorithmItem = document.createElement('label');
    algorithmItem.className = 'algorithm-item flex items-center p-3 bg-gray-700 rounded-lg hover:bg-gray-600 cursor-pointer transition';
    algorithmItem.setAttribute('data-category', 'custom');
    algorithmItem.setAttribute('data-custom', 'true');

    algorithmItem.innerHTML = `
      <input type="checkbox" name="algorithms" value="${englishName}" class="mr-3 accent-blue-500">
      <span class="text-sm text-white">${chineseName}</span>
      <span class="ml-2 text-xs text-blue-400">[自定义]</span>
    `;

    // 添加到网格末尾
    algorithmGrid.appendChild(algorithmItem);
  }

  // 批量添加自定义模型到算法网格
  function addCustomModelsToGrid() {
    customModels.forEach(model => {
      addCustomModelToGrid(model.english_name, model.chinese_name);
    });
  }

  // 从算法网格中移除自定义模型
  function removeCustomModelFromGrid(englishName) {
    const algorithmGrid = document.getElementById('algorithmGrid');
    if (!algorithmGrid) return;

    const algorithmItem = algorithmGrid.querySelector(`input[value="${englishName}"]`);
    if (algorithmItem) {
      algorithmItem.closest('.algorithm-item').remove();
    }
  }

  // 更新算法标签显示
  window.updateAlgorithmTags = function(algorithms) {
    const tagsContainer = document.getElementById('selectedAlgorithmTags');
    if (!tagsContainer) return;

    if (algorithms.length === 0) {
      tagsContainer.innerHTML = '<p class="text-gray-400 text-sm">未选择算法</p>';
      return;
    }

    tagsContainer.innerHTML = algorithms.map(alg => `
      <span class="algorithm-tag inline-flex items-center px-3 py-1 rounded-full text-sm font-medium text-white mr-2 mb-2">
        ${alg.name}
        <button onclick="removeAlgorithmTag('${alg.value}')" class="remove-btn ml-2 text-white hover:text-red-300">
          <i class="fas fa-times text-xs"></i>
        </button>
      </span>
    `).join('');
  };

  // 删除算法标签
  window.removeAlgorithmTag = function(algorithmValue) {
    let selectedAlgorithms = JSON.parse(localStorage.getItem('selectedAlgorithms') || '[]');
    selectedAlgorithms = selectedAlgorithms.filter(alg => alg.value !== algorithmValue);
    localStorage.setItem('selectedAlgorithms', JSON.stringify(selectedAlgorithms));
    updateAlgorithmTags(selectedAlgorithms);
    
    // 同时更新弹框中的选择状态
    const checkbox = document.querySelector(`input[name="algorithms"][value="${algorithmValue}"]`);
    if (checkbox) {
      checkbox.checked = false;
    }
  };

  // 页面加载时恢复选择状态
  const savedAlgorithms = JSON.parse(localStorage.getItem('selectedAlgorithms') || '[]');
  if (savedAlgorithms.length > 0) {
    updateAlgorithmTags(savedAlgorithms);
    
    // 恢复弹框中的选择状态
    savedAlgorithms.forEach(alg => {
      const checkbox = document.querySelector(`input[name="algorithms"][value="${alg.value}"]`);
      if (checkbox) {
        checkbox.checked = true;
      }
    });
  }
});
