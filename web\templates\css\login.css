
/* ai_security_monitor/frontend/css/login.css */
@import url('https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

:root {
  --primary-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  --secondary-gradient: linear-gradient(to right, #3b82f6, #8b5cf6);
  --bg-dark: #0f172a;
  --bg-light: #1e293b;
  --input-bg: rgba(255, 255, 255, 0.1);
  --input-focus-bg: rgba(255, 255, 255, 0.15);
}

body {
  font-family: 'Noto Sans SC', sans-serif;
  background: var(--primary-gradient);
  color: #e2e8f0;
  min-height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
}

.login-container {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
  padding: 2rem;
}

.login-card {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 1rem;
  padding: 2.5rem;
  width: 100%;
  max-width: 24rem;
  margin: 0 auto;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
}

.login-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
}

.login-logo {
  text-align: center;
  margin-bottom: 2rem;
}

.login-logo-icon {
  width: 5rem;
  height: 5rem;
  margin: 0 auto 1rem;
  background: var(--secondary-gradient);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.login-title {
  font-size: 1.875rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  background-clip: text;
  -webkit-background-clip: text;
  color: transparent;
  background-image: var(--secondary-gradient);
}

.login-subtitle {
  color: #94a3b8;
  font-size: 0.875rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.input-group {
  position: relative;
}

.input-icon {
  position: absolute;
  left: 1rem;
  top: 50%;
  transform: translateY(-50%);
  color: #64748b;
}

.input-field {
  width: 100%;
  padding: 0.75rem 1rem 0.75rem 2.5rem;
  background: var(--input-bg);
  border: 1px solid #334155;
  border-radius: 0.5rem;
  color: #e2e8f0;
  transition: all 0.3s ease;
}

.input-field:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  background: var(--input-focus-bg);
}

.remember-forgot {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 0.875rem;
}

.remember-me {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #94a3b8;
}

.forgot-password {
  color: #60a5fa;
  text-decoration: none;
  transition: color 0.2s;
}

.forgot-password:hover {
  color: #3b82f6;
}

.login-button {
  width: 100%;
  padding: 0.75rem;
  border-radius: 0.5rem;
  background: var(--secondary-gradient);
  color: white;
  font-weight: 500;
  border: none;
  cursor: pointer;
  transition: all 0.3s ease;
}

.login-button:hover {
  background: linear-gradient(to right, #2563eb, #7c3aed);
  transform: translateY(-2px);
}

.register-link {
  text-align: center;
  font-size: 0.875rem;
  color: #94a3b8;
}

.register-link a {
  color: #60a5fa;
  text-decoration: none;
  transition: color 0.2s;
}

.register-link a:hover {
  color: #3b82f6;
}

/* 粒子效果容器 */
#particles-js {
  position: fixed;
  width: 100%;
  height: 100%;
  z-index: -1;
}

/* 响应式调整 */
@media (max-width: 768px) {
  .login-card {
    padding: 1.5rem;
  }
  
  .login-title {
    font-size: 1.5rem;
  }
}
