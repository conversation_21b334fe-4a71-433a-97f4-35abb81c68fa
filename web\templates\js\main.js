
// ai_security_monitor/frontend/js/main.js
document.addEventListener('DOMContentLoaded', function() {
  // 视频流处理
  const video = document.getElementById('videoFeed');
  const videoStatus = document.getElementById('videoStatus');
  const videoSource = document.getElementById('videoSource');
  const startCameraBtn = document.getElementById('startCamera');
  const uploadVideoBtn = document.getElementById('uploadVideo');
  const startAnalysisBtn = document.getElementById('startAnalysis');
  
  let stream = null;
  let isAnalyzing = false;
  let analysisInterval = null;
  let canvas = document.createElement('canvas');
  let ctx = canvas.getContext('2d');
  
  // 开启摄像头
  startCameraBtn.addEventListener('click', async function() {
    try {
      stream = await navigator.mediaDevices.getUserMedia({ video: true });
      video.srcObject = stream;
      videoStatus.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
      videoSource.textContent = '摄像头已连接';
      startCameraBtn.disabled = true;
    } catch (err) {
      console.error('摄像头访问失败:', err);
      videoStatus.className = 'inline-block w-3 h-3 rounded-full bg-red-500 mr-2';
      videoSource.textContent = '摄像头连接失败';
    }
  });
  
  // 上传视频处理
  uploadVideoBtn.addEventListener('click', function() {
    document.getElementById('uploadModal').classList.remove('hidden');
  });
  
  // 开始分析
  startAnalysisBtn.addEventListener('click', function() {
    if (!video.srcObject && !video.src) {
      alert('请先开启摄像头或上传视频');
      return;
    }
    
    isAnalyzing = !isAnalyzing;
    
    if (isAnalyzing) {
      startAnalysisBtn.innerHTML = '<i class="fas fa-stop mr-2"></i>停止分析';
      startAnalysisBtn.className = 'px-6 py-3 bg-red-600 hover:bg-red-700 rounded-md font-medium transition';
      
      // 设置分析间隔
      const sensitivity = document.getElementById('sensitivity').value;
      const interval = 1100 - (sensitivity * 100); // 灵敏度越高，间隔越短
      
      analysisInterval = setInterval(analyzeFrame, interval);
    } else {
      startAnalysisBtn.innerHTML = '<i class="fas fa-play mr-2"></i>开始分析';
      startAnalysisBtn.className = 'px-6 py-3 bg-green-600 hover:bg-green-700 rounded-md font-medium transition';
      clearInterval(analysisInterval);
    }
  });
  
  // 分析视频帧
  function analyzeFrame() {
    if (!video.videoWidth) return;
    
    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    ctx.drawImage(video, 0, 0, canvas.width, canvas.height);
    
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const model = document.getElementById('detectionModel').value;
    const customPrompt = document.getElementById('customPrompt').value;
    
    // 模拟分析结果
    const randomAlert = Math.random();
    if (randomAlert > 0.8) {
      generateAlert(model, customPrompt);
    }
  }
  
  // 生成预警信息
  function generateAlert(model, prompt) {
    const alertList = document.getElementById('alertList');
    const alertCount = document.getElementById('alertCount');
    const now = new Date();
    const timestamp = now.toISOString().replace('T', ' ').substring(0, 19);
    
    let alertType = 'warning';
    let alertTitle = '异常行为检测';
    let alertDesc = '检测到可疑行为';
    
    switch(model) {
      case 'fight':
        alertType = 'danger';
        alertTitle = '打架斗殴检测';
        alertDesc = '检测到肢体冲突行为';
        break;
      case 'fall':
        alertType = 'danger';
        alertTitle = '跌倒检测';
        alertDesc = '检测到人员跌倒';
        break;
      case 'intrusion':
        alertType = 'danger';
        alertTitle = '入侵检测';
        alertDesc = '检测到未授权人员进入';
        break;
    }
    
    if (prompt) {
      alertDesc += ` (自定义规则: ${prompt})`;
    }
    
    const alertItem = document.createElement('div');
    alertItem.className = `alert-item rounded-lg p-4 border-l-4 border-${alertType === 'danger' ? 'red' : 'yellow'}-500`;
    alertItem.innerHTML = `
      <div class="flex justify-between items-start">
        <div>
          <h4 class="font-medium">${alertTitle}</h4>
          <p class="text-sm text-gray-400 mt-1">${timestamp}</p>
        </div>
        <button class="text-gray-400 hover:text-white">
          <i class="fas fa-ellipsis-v"></i>
        </button>
      </div>
      <p class="text-sm mt-2">${alertDesc}</p>
    `;
    
    alertList.insertBefore(alertItem, alertList.firstChild);
    alertCount.textContent = parseInt(alertCount.textContent) + 1;
    
    // 保存到本地存储
    saveAlert({
      type: alertType,
      title: alertTitle,
      description: alertDesc,
      timestamp: timestamp,
      model: model,
      customPrompt: prompt
    });
  }
  
  // 上传视频模态框处理
  document.getElementById('selectVideo').addEventListener('click', function() {
    document.getElementById('videoFile').click();
  });
  
  document.getElementById('videoFile').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (!file) return;
    
    const videoURL = URL.createObjectURL(file);
    video.src = videoURL;
    video.srcObject = null;
    if (stream) {
      stream.getTracks().forEach(track => track.stop());
    }
    
    videoStatus.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
    videoSource.textContent = '视频文件已加载: ' + file.name;
    document.getElementById('confirmUpload').disabled = false;
  });
  
  document.getElementById('confirmUpload').addEventListener('click', function() {
    document.getElementById('uploadModal').classList.add('hidden');
  });
  
  document.getElementById('cancelUpload').addEventListener('click', function() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('videoFile').value = '';
    document.getElementById('confirmUpload').disabled = true;
  });
  
  document.getElementById('closeUploadModal').addEventListener('click', function() {
    document.getElementById('uploadModal').classList.add('hidden');
    document.getElementById('videoFile').value = '';
    document.getElementById('confirmUpload').disabled = true;
  });
  
  // // 查看历史记录
  // document.getElementById('viewHistory').addEventListener('click', function() {
  //   alert('正在开发中: 历史记录功能');
  // });
});
