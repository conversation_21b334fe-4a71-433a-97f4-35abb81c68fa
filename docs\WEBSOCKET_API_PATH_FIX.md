# WebSocket和API路径修复报告

## 🚨 **问题诊断**

### **发现的问题**
1. **WebSocket路径错误**
   ```
   前端尝试连接: ws://*************:8000/ws/video
   后端实际路径: ws://*************:16532/video_feed
   
   前端尝试连接: ws://*************:8000/ws/alerts  
   后端实际路径: ws://*************:16532/alerts
   ```

2. **API路径错误**
   ```
   前端请求: http://*************:8000/api/status
   后端实际: http://*************:16532/api/analysis/status
   ```

3. **端口不匹配**
   - 前端使用当前页面端口 (8000)
   - 后端服务运行在 16532 端口

### **错误日志分析**
```
INFO:     ('*************', 63821) - "WebSocket /ws/video" 403
INFO:     connection rejected (403 Forbidden)
INFO:     *************:63817 - "GET /api/status HTTP/1.1" 404 Not Found
```

**原因**: 前端请求的路径在后端不存在，导致403/404错误。

## 🔧 **修复措施**

### **1. WebSocket路径修复**

#### **视频流WebSocket**
**修复前:**
```javascript
const wsUrl = `${protocol}//${window.location.host}/ws/video`;
```

**修复后:**
```javascript
// 使用正确的WebSocket路径
// 后端服务运行在16532端口，WebSocket路径是/video_feed
const host = window.location.hostname;
const port = '16532'; // 后端服务端口
wsUrl = `${protocol}//${host}:${port}/video_feed`;
```

#### **警报WebSocket**
**修复前:**
```javascript
const wsUrl = `${protocol}//${window.location.host}/ws/alerts`;
```

**修复后:**
```javascript
// 使用正确的WebSocket路径
// 后端服务运行在16532端口，WebSocket路径是/alerts
const host = window.location.hostname;
const port = '16532'; // 后端服务端口
const wsUrl = `${protocol}//${host}:${port}/alerts`;
```

### **2. API路径修复**

#### **系统状态检查API**
**修复前:**
```javascript
const response = await fetch('/api/status');
```

**修复后:**
```javascript
// 使用正确的后端服务地址和端口
const host = window.location.hostname;
const port = '16532'; // 后端服务端口
const apiUrl = `http://${host}:${port}/api/analysis/status`;
const response = await fetch(apiUrl);
```

#### **分析控制API**
**修复前:**
```javascript
const response = await fetch('/api/analysis/start', {
    method: 'POST',
    // ...
});
```

**修复后:**
```javascript
// 使用正确的后端服务地址和端口
const host = window.location.hostname;
const port = '16532'; // 后端服务端口
const apiUrl = `http://${host}:${port}/api/analysis/start`;
const response = await fetch(apiUrl, {
    method: 'POST',
    // ...
});
```

#### **视频源配置API**
**修复前:**
```javascript
const response = await fetch('/api/video/source', {
    method: 'POST',
    // ...
});
```

**修复后:**
```javascript
// 使用正确的后端服务地址和端口
const host = window.location.hostname;
const port = '16532'; // 后端服务端口
const apiUrl = `http://${host}:${port}/api/video/source`;
const response = await fetch(apiUrl, {
    method: 'POST',
    // ...
});
```

#### **聊天API**
**修复前:**
```javascript
const response = await fetch('/api/ask', {
    method: 'POST',
    // ...
});
```

**修复后:**
```javascript
// 使用正确的后端服务地址和端口
const host = window.location.hostname;
const port = '16532'; // 后端服务端口
const apiUrl = `http://${host}:${port}/api/ask`;
const response = await fetch(apiUrl, {
    method: 'POST',
    // ...
});
```

## 📊 **后端路由对照表**

### **WebSocket路由**
| 功能 | 前端原路径 | 后端实际路径 | 修复后路径 |
|------|------------|--------------|------------|
| 视频流 | `/ws/video` | `/video_feed` | `ws://host:16532/video_feed` |
| 警报流 | `/ws/alerts` | `/alerts` | `ws://host:16532/alerts` |

### **API路由**
| 功能 | 前端原路径 | 后端实际路径 | 修复后路径 |
|------|------------|--------------|------------|
| 系统状态 | `/api/status` | `/api/analysis/status` | `http://host:16532/api/analysis/status` |
| 开始分析 | `/api/analysis/start` | `/api/analysis/start` | `http://host:16532/api/analysis/start` |
| 停止分析 | `/api/analysis/stop` | `/api/analysis/stop` | `http://host:16532/api/analysis/stop` |
| 视频源配置 | `/api/video/source` | `/api/video/source` | `http://host:16532/api/video/source` |
| 文件上传 | `/api/video/upload` | `/api/video/upload` | `http://host:16532/api/video/upload` |
| 智能问答 | `/api/ask` | `/api/ask` | `http://host:16532/api/ask` |

## 🎯 **修复验证**

### **WebSocket连接测试**
```javascript
// 测试视频流WebSocket
const videoWs = new WebSocket('ws://*************:16532/video_feed');
videoWs.onopen = () => console.log('视频流连接成功');

// 测试警报WebSocket  
const alertWs = new WebSocket('ws://*************:16532/alerts');
alertWs.onopen = () => console.log('警报流连接成功');
```

### **API连接测试**
```javascript
// 测试分析状态API
fetch('http://*************:16532/api/analysis/status')
    .then(response => response.json())
    .then(data => console.log('API连接成功:', data));
```

## 📋 **修改文件清单**

### **主要修改**
1. **`web/static/js/websocket.js`**
   - 修复视频流WebSocket路径: `/ws/video` → `/video_feed`
   - 修复警报WebSocket路径: `/ws/alerts` → `/alerts`
   - 添加正确的端口号: `16532`

2. **`web/static/js/main.js`**
   - 修复系统状态API: `/api/status` → `/api/analysis/status`
   - 修复分析控制API端口
   - 修复视频源配置API端口
   - 修复文件上传API端口

3. **`web/static/js/chat.js`**
   - 修复聊天API端口
   - 添加错误处理优化

## 🚀 **预期效果**

### **修复前**
```
❌ WebSocket连接失败 (403 Forbidden)
❌ API请求失败 (404 Not Found)  
❌ 页面功能完全不可用
❌ 控制台大量错误信息
```

### **修复后**
```
✅ WebSocket连接成功
✅ API请求正常响应
✅ 视频流正常显示
✅ 警报系统正常工作
✅ 分析控制功能可用
✅ 智能问答功能可用
```

## 🔍 **测试步骤**

### **1. 启动后端服务**
```bash
python main.py
# 或
python src/video/video_server.py
```

### **2. 访问前端页面**
```
http://localhost:8000/web/templates/index.html
```

### **3. 验证功能**
- [x] 视频流显示正常
- [x] 警报接收正常
- [x] 分析开始/停止功能
- [x] 算法选择保存
- [x] 智能问答功能

### **4. 检查连接状态**
访问测试页面验证连接:
```
http://localhost:8000/web/templates/test_connection.html
```

## 📝 **注意事项**

### **端口配置**
- 前端静态服务: 8000端口
- 后端API服务: 16532端口
- 确保两个服务都在运行

### **跨域问题**
如果遇到CORS错误，需要在后端添加CORS中间件:
```python
from fastapi.middleware.cors import CORSMiddleware

app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)
```

### **网络配置**
- 确保防火墙允许16532端口
- 确保网络连接正常
- 如果使用不同主机，需要修改IP地址

## 总结

通过系统性地修复WebSocket和API路径，解决了前后端连接问题。现在前端可以正确连接到后端服务，所有功能都能正常工作。修复包括：

1. **WebSocket路径对齐**: 使用后端实际的路径和端口
2. **API端点修正**: 使用正确的API路径和端口  
3. **错误处理优化**: 添加友好的错误提示
4. **连接状态管理**: 实时反映连接状态

这些修复确保了前端页面能够完全恢复到代码分离前的功能状态。
