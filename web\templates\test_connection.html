<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>连接测试页面</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css" rel="stylesheet">
</head>
<body class="bg-gray-900 text-white p-8">
    <div class="max-w-4xl mx-auto">
        <h1 class="text-3xl font-bold mb-8 text-center">连接状态测试</h1>
        
        <!-- 连接状态面板 -->
        <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            <!-- 后端API状态 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-server mr-2"></i>后端API
                </h3>
                <div class="flex items-center mb-2">
                    <span id="apiStatus" class="inline-block w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                    <span id="apiStatusText">检测中...</span>
                </div>
                <button id="testApiBtn" class="mt-3 px-4 py-2 bg-blue-600 hover:bg-blue-700 rounded text-sm">
                    测试连接
                </button>
            </div>
            
            <!-- 视频WebSocket状态 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-video mr-2"></i>视频流
                </h3>
                <div class="flex items-center mb-2">
                    <span id="videoWsStatus" class="inline-block w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                    <span id="videoWsStatusText">检测中...</span>
                </div>
                <button id="testVideoWsBtn" class="mt-3 px-4 py-2 bg-green-600 hover:bg-green-700 rounded text-sm">
                    测试连接
                </button>
            </div>
            
            <!-- 警报WebSocket状态 -->
            <div class="bg-gray-800 p-6 rounded-lg">
                <h3 class="text-lg font-semibold mb-4 flex items-center">
                    <i class="fas fa-exclamation-triangle mr-2"></i>警报流
                </h3>
                <div class="flex items-center mb-2">
                    <span id="alertWsStatus" class="inline-block w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                    <span id="alertWsStatusText">检测中...</span>
                </div>
                <button id="testAlertWsBtn" class="mt-3 px-4 py-2 bg-yellow-600 hover:bg-yellow-700 rounded text-sm">
                    测试连接
                </button>
            </div>
        </div>
        
        <!-- 演示模式状态 -->
        <div class="bg-gray-800 p-6 rounded-lg mb-8">
            <h3 class="text-lg font-semibold mb-4 flex items-center">
                <i class="fas fa-play-circle mr-2"></i>演示模式
            </h3>
            <div class="flex items-center justify-between">
                <div class="flex items-center">
                    <span id="demoModeStatus" class="inline-block w-3 h-3 rounded-full bg-gray-500 mr-2"></span>
                    <span id="demoModeText">未启用</span>
                </div>
                <button id="toggleDemoBtn" class="px-4 py-2 bg-purple-600 hover:bg-purple-700 rounded text-sm">
                    启用演示模式
                </button>
            </div>
        </div>
        
        <!-- 日志面板 -->
        <div class="bg-gray-800 p-6 rounded-lg">
            <div class="flex justify-between items-center mb-4">
                <h3 class="text-lg font-semibold flex items-center">
                    <i class="fas fa-list mr-2"></i>连接日志
                </h3>
                <button id="clearLogBtn" class="px-3 py-1 bg-red-600 hover:bg-red-700 rounded text-sm">
                    清空日志
                </button>
            </div>
            <div id="connectionLog" class="bg-gray-900 p-4 rounded h-64 overflow-y-auto font-mono text-sm">
                <div class="text-gray-400">等待连接测试...</div>
            </div>
        </div>
        
        <!-- 返回主页 -->
        <div class="text-center mt-8">
            <a href="index.html" class="px-6 py-3 bg-blue-600 hover:bg-blue-700 rounded-lg font-medium">
                <i class="fas fa-home mr-2"></i>返回主页
            </a>
        </div>
    </div>

    <script>
        // 日志记录
        function addLog(message, type = 'info') {
            const logContainer = document.getElementById('connectionLog');
            const timestamp = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            
            let colorClass = 'text-gray-300';
            let icon = 'info-circle';
            
            switch (type) {
                case 'success':
                    colorClass = 'text-green-400';
                    icon = 'check-circle';
                    break;
                case 'error':
                    colorClass = 'text-red-400';
                    icon = 'times-circle';
                    break;
                case 'warning':
                    colorClass = 'text-yellow-400';
                    icon = 'exclamation-triangle';
                    break;
            }
            
            logEntry.className = `${colorClass} mb-1`;
            logEntry.innerHTML = `<i class="fas fa-${icon} mr-2"></i>[${timestamp}] ${message}`;
            
            logContainer.appendChild(logEntry);
            logContainer.scrollTop = logContainer.scrollHeight;
        }
        
        // 更新状态指示器
        function updateStatus(elementId, textId, status, text) {
            const statusEl = document.getElementById(elementId);
            const textEl = document.getElementById(textId);
            
            let colorClass = 'bg-gray-500';
            switch (status) {
                case 'connected':
                    colorClass = 'bg-green-500';
                    break;
                case 'disconnected':
                    colorClass = 'bg-red-500';
                    break;
                case 'error':
                    colorClass = 'bg-yellow-500';
                    break;
            }
            
            statusEl.className = `inline-block w-3 h-3 rounded-full ${colorClass} mr-2`;
            textEl.textContent = text;
        }
        
        // 测试API连接
        async function testApiConnection() {
            addLog('测试API连接...', 'info');
            updateStatus('apiStatus', 'apiStatusText', 'testing', '测试中...');
            
            try {
                const response = await fetch('/api/status');
                if (response.ok) {
                    const result = await response.json();
                    addLog('API连接成功', 'success');
                    updateStatus('apiStatus', 'apiStatusText', 'connected', '已连接');
                    return true;
                } else {
                    throw new Error(`HTTP ${response.status}`);
                }
            } catch (error) {
                addLog(`API连接失败: ${error.message}`, 'error');
                updateStatus('apiStatus', 'apiStatusText', 'disconnected', '连接失败');
                return false;
            }
        }
        
        // 测试WebSocket连接
        function testWebSocketConnection(url, statusId, textId, name) {
            addLog(`测试${name}WebSocket连接...`, 'info');
            updateStatus(statusId, textId, 'testing', '测试中...');
            
            return new Promise((resolve) => {
                const ws = new WebSocket(url);
                
                const timeout = setTimeout(() => {
                    ws.close();
                    addLog(`${name}WebSocket连接超时`, 'warning');
                    updateStatus(statusId, textId, 'error', '连接超时');
                    resolve(false);
                }, 5000);
                
                ws.onopen = () => {
                    clearTimeout(timeout);
                    addLog(`${name}WebSocket连接成功`, 'success');
                    updateStatus(statusId, textId, 'connected', '已连接');
                    ws.close();
                    resolve(true);
                };
                
                ws.onerror = () => {
                    clearTimeout(timeout);
                    addLog(`${name}WebSocket连接失败`, 'error');
                    updateStatus(statusId, textId, 'disconnected', '连接失败');
                    resolve(false);
                };
            });
        }
        
        // 切换演示模式
        function toggleDemoMode() {
            const isDemoMode = document.getElementById('demoModeText').textContent === '已启用';
            
            if (isDemoMode) {
                updateStatus('demoModeStatus', 'demoModeText', 'disconnected', '未启用');
                document.getElementById('toggleDemoBtn').textContent = '启用演示模式';
                addLog('演示模式已禁用', 'info');
            } else {
                updateStatus('demoModeStatus', 'demoModeText', 'connected', '已启用');
                document.getElementById('toggleDemoBtn').textContent = '禁用演示模式';
                addLog('演示模式已启用', 'success');
            }
        }
        
        // 事件监听器
        document.getElementById('testApiBtn').addEventListener('click', testApiConnection);
        
        document.getElementById('testVideoWsBtn').addEventListener('click', () => {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const url = `${protocol}//${window.location.host}/ws/video`;
            testWebSocketConnection(url, 'videoWsStatus', 'videoWsStatusText', '视频流');
        });
        
        document.getElementById('testAlertWsBtn').addEventListener('click', () => {
            const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
            const url = `${protocol}//${window.location.host}/ws/alerts`;
            testWebSocketConnection(url, 'alertWsStatus', 'alertWsStatusText', '警报流');
        });
        
        document.getElementById('toggleDemoBtn').addEventListener('click', toggleDemoMode);
        
        document.getElementById('clearLogBtn').addEventListener('click', () => {
            document.getElementById('connectionLog').innerHTML = '<div class="text-gray-400">日志已清空</div>';
        });
        
        // 页面加载时自动测试
        window.addEventListener('load', () => {
            addLog('页面加载完成，开始连接测试', 'info');
            
            setTimeout(async () => {
                const apiConnected = await testApiConnection();
                
                if (!apiConnected) {
                    addLog('后端服务不可用，建议启用演示模式', 'warning');
                }
            }, 1000);
        });
    </script>
</body>
</html>
