
<!-- ai_security_monitor/frontend/assistant.html -->
<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
  <title>AI安全监控系统 - 辅助功能</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/tailwindcss/2.2.19/tailwind.min.css" rel="stylesheet">
  <link href="https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap" rel="stylesheet">
  <script src="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/js/all.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/Chart.js/3.7.1/chart.min.js"></script>
  <script src="https://cdnjs.cloudflare.com/ajax/libs/particles.js/2.0.0/particles.min.js"></script>
  <link href="css/style.css" rel="stylesheet">
  <style>
    body {
      font-family: 'Noto Sans SC', sans-serif;
      background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
      min-height: 100vh;
    }
    .feature-card {
      background: rgba(255, 255, 255, 0.05);
      backdrop-filter: blur(10px);
      box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
      transition: all 0.3s ease;
    }
    .feature-card:hover {
      transform: translateY(-5px);
      box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
    }
    #particles-js {
      position: absolute;
      width: 100%;
      height: 100%;
      z-index: -1;
    }
    .video-player {
      background: rgba(0, 0, 0, 0.3);
      border-radius: 0.5rem;
      overflow: hidden;
    }
    .video-controls {
      background: rgba(30, 41, 59, 0.8);
    }
    .timeline-marker {
      position: absolute;
      width: 4px;
      height: 100%;
      background: rgba(239, 68, 68, 0.7);
      top: 0;
    }
  </style>
</head>
<body class="text-gray-200">
  <div id="particles-js"></div>
  
  <div class="container mx-auto px-4 py-8">
    <header class="mb-8 flex justify-between items-center">
      <h1 class="text-3xl font-bold bg-clip-text text-transparent bg-gradient-to-r from-blue-400 to-purple-600">
        AI安全监控辅助系统
      </h1>
      <a href="index.html" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md transition">
        <i class="fas fa-arrow-left mr-2"></i>返回主控台
      </a>
    </header>

    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      <!-- 异常片段查看模块 -->
      <div class="feature-card rounded-xl p-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-blue-600 rounded-full flex items-center justify-center mr-3">
            <i class="fas fa-video text-white"></i>
          </div>
          <h2 class="text-xl font-semibold">异常片段查看</h2>
        </div>
        
        <div class="mb-4">
          <label class="block text-sm font-medium mb-2">选择日期</label>
          <input type="date" id="datePicker" class="w-full bg-gray-700 border border-gray-600 rounded-md px-3 py-2">
        </div>
        
        <div class="space-y-3 max-h-96 overflow-y-auto pr-2" id="videoClipsList">
          <!-- 动态生成的视频片段 -->
          <template id="clipTemplate">
            <div class="p-3 bg-gray-800 rounded-lg flex items-center cursor-pointer hover:bg-gray-700 clip-item">
              <div class="w-16 h-16 bg-gray-700 rounded mr-3 overflow-hidden">
                <img class="w-full h-full object-cover thumbnail">
              </div>
              <div>
                <h4 class="font-medium time-info"></h4>
                <p class="text-sm text-gray-400 timestamp"></p>
              </div>
            </div>
          </template>
        </div>
        
        <div class="mt-4 flex justify-center">
          <button id="loadMoreClips" class="px-4 py-2 bg-gray-700 hover:bg-gray-600 rounded-md transition">
            <i class="fas fa-sync-alt mr-2"></i>加载更多
          </button>
        </div>
      </div>

      <!-- 视频播放模块 -->
      <div class="feature-card rounded-xl p-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-purple-600 rounded-full flex items-center justify-center mr-3">
            <i class="fas fa-play-circle text-white"></i>
          </div>
          <h2 class="text-xl font-semibold">异常视频播放</h2>
        </div>
        
        <div class="video-player mb-4">
          <div class="relative">
            <video id="clipPlayer" class="w-full h-auto max-h-64 bg-black" controls>
              <source src="" type="video/mp4">
              您的浏览器不支持视频播放
            </video>
            <div id="timelineMarkers" class="absolute bottom-0 left-0 right-0 h-1 bg-gray-700">
              <div class="timeline-marker" style="left: 15%"></div>
              <div class="timeline-marker" style="left: 45%"></div>
              <div class="timeline-marker" style="left: 75%"></div>
            </div>
          </div>
<!--          <div class="video-controls p-3 flex items-center justify-between">-->
<!--            <button id="playPauseBtn" class="w-8 h-8 rounded-full bg-purple-600 flex items-center justify-center">-->
<!--              <i class="fas fa-play text-white"></i>-->
<!--            </button>-->
<!--            <div class="flex-1 mx-4">-->
<!--              <div class="relative h-1 bg-gray-600 rounded-full">-->
<!--                <div id="progressBar" class="absolute top-0 left-0 h-full bg-purple-500 rounded-full" style="width: 0%"></div>-->
<!--              </div>-->
<!--            </div>-->
<!--            <span id="timeDisplay" class="text-sm text-gray-300">00:00 / 00:00</span>-->
<!--          </div>-->
        </div>
        
        <div class="bg-gray-800 rounded-lg p-4">
          <h3 class="font-medium mb-2">异常信息</h3>
          <div class="grid grid-cols-2 gap-2 text-sm">
            <div>
              <p class="text-gray-400">异常类型:</p>
              <p class="font-medium time-info"></p>
            </div>
            <div>
              <p class="text-gray-400">发生时间:</p>
              <p class="text-sm text-gray-400 timestamp"></p>
            </div>
          </div>
          <!-- <div class="mt-3">
            <p class="text-gray-400">描述:</p>
            <p class="text-sm">检测到两人肢体冲突，持续约15秒</p>
          </div> -->
        </div>
      </div>

      <!-- 异常统计可视化模块 -->
      <div class="lg:col-span-2 feature-card rounded-xl p-6">
        <div class="flex items-center mb-4">
          <div class="w-10 h-10 bg-green-600 rounded-full flex items-center justify-center mr-3">
            <i class="fas fa-chart-bar text-white"></i>
          </div>
          <h2 class="text-xl font-semibold">异常统计分析</h2>
        </div>
        
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h3 class="text-lg font-medium mb-3">本周异常类型分布</h3>
            <canvas id="alertTypeChart" height="250"></canvas>
          </div>
          <div>
            <h3 class="text-lg font-medium mb-3">近7天异常趋势</h3>
            <canvas id="alertTrendChart" height="250"></canvas>
          </div>
        </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 脚本引入 -->
  <script src="js/assistant.js"></script>
  <script src="js/chart-utils.js"></script>
  
  <script>
    // 粒子效果初始化
    document.addEventListener('DOMContentLoaded', function() {
      particlesJS('particles-js', {
        particles: {
          number: { value: 80, density: { enable: true, value_area: 800 } },
          color: { value: "#3b82f6" },
          shape: { type: "circle" },
          opacity: { value: 0.5, random: true },
          size: { value: 3, random: true },
          line_linked: { enable: true, distance: 150, color: "#3b82f6", opacity: 0.4, width: 1 },
          move: { enable: true, speed: 2, direction: "none", random: true, straight: false, out_mode: "out" }
        },
        interactivity: {
          detect_on: "canvas",
          events: {
            onhover: { enable: true, mode: "grab" },
            onclick: { enable: true, mode: "push" }
          }
        }
      });

      // 初始化图表
      initCharts();
    });

    function initCharts() {
      // 异常类型分布图
      const typeCtx = document.getElementById('alertTypeChart').getContext('2d');
            // 新增AJAX请求
      fetch('/api/recent-alerts')
      .then(response => response.json())
      .then(data => {
      // 动态生成颜色数组，确保每个异常类型都有不同的颜色
      const generateColors = (count) => {
        const baseColors = [
          '#8b5cf6', // 紫色
          '#10b981', // 绿色
          '#f59e0b', // 橙色
          '#3b82f6', // 蓝色
          '#ef4444', // 红色
          '#64748b', // 灰色
          '#06b6d4', // 青色
          '#84cc16', // 黄绿色
          '#f97316', // 深橙色
          '#ec4899', // 粉色
          '#8b5a2b', // 棕色
          '#6366f1'  // 靛蓝色
        ];

        // 如果需要的颜色数量超过基础颜色数量，生成额外颜色
        const colors = [];
        for (let i = 0; i < count; i++) {
          if (i < baseColors.length) {
            colors.push(baseColors[i]);
          } else {
            // 生成随机颜色
            const hue = (i * 137.508) % 360; // 使用黄金角度分布
            colors.push(`hsl(${hue}, 70%, 60%)`);
          }
        }
        return colors;
      };

      new Chart(typeCtx, {
        type: 'doughnut',
        data: {
          labels: data.labels,
          datasets: [{
            data: data.data,
            backgroundColor: generateColors(data.labels.length)
          }]
        },
        options: {
          responsive: true,
          plugins: {
            legend: {
              position: 'right',
              labels: {
                color: '#e2e8f0',
                usePointStyle: true,
                padding: 15
              }
            },
            tooltip: {
              callbacks: {
                label: function(context) {
                  const label = context.label || '';
                  const value = context.parsed;
                  const total = context.dataset.data.reduce((a, b) => a + b, 0);
                  const percentage = ((value / total) * 100).toFixed(1);
                  return `${label}: ${value} (${percentage}%)`;
                }
              }
            }
          }
        }
      });
    })
    .catch(error => console.error('获取数据失败:', error));
      // 异常趋势图
      const trendCtx = document.getElementById('alertTrendChart').getContext('2d');
      
      // 新增AJAX请求
      fetch('/api/alerts/trend')
        .then(response => response.json())
        .then(data => {
          new Chart(trendCtx, {
            type: 'line',
            data: {
              labels: data.labels,
              datasets: [{
                label: '异常数量',
                data: data.data,
                borderColor: '#8b5cf6',
                backgroundColor: 'rgba(139, 92, 246, 0.1)',
                tension: 0.3,
                fill: true
              }]
            },
            options: {
              responsive: true,
              scales: {
                y: {
                  beginAtZero: true,
                  grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                  },
                  ticks: {
                    color: '#e2e8f0'
                  }
                },
                x: {
                  grid: {
                    color: 'rgba(255, 255, 255, 0.1)'
                  },
                  ticks: {
                    color: '#e2e8f0'
                  }
                }
              },
              plugins: {
                legend: {
                  labels: {
                    color: '#e2e8f0'
                  }
                }
              }
            }
          });
        })
        .catch(error => console.error('获取数据失败:', error));
    }
  </script>
  <script>
    // 初始化视频片段
    async function loadVideoClips() {
      try {
        const dateFilter = document.getElementById('datePicker').value;
        const response = await fetch(`/api/video-clips?date=${dateFilter}`);
        if (!response.ok) throw new Error('API请求失败');
        
        const clips = await response.json();
        const template = document.getElementById('clipTemplate');
        const container = document.getElementById('videoClipsList');
        
        container.innerHTML = ''; // 清空现有内容
        
        clips.forEach(data => {
          const clone = template.content.cloneNode(true);
          clone.querySelector('.thumbnail').src = data.thumbnail;
          clone.querySelector('.time-info').textContent = data.alertType;  // 显示异常类型
          clone.querySelector('.timestamp').textContent = new Date(data.timestamp).toLocaleString();
          // 将原始代码中的 timestamp 显示改为格式化时间
          
          clone.querySelector('.clip-item').addEventListener('click', () => {
            const player = document.getElementById('clipPlayer');
            player.src = data.videoPath;
            player.load();
            player.play();
            
            // 更新后的异常信息定位
            const alertInfoContainer = document.querySelector('.video-player + .bg-gray-800');
            alertInfoContainer.querySelector('.time-info').textContent = data.alertType;
            alertInfoContainer.querySelector('.timestamp').textContent = 
                new Date(data.timestamp).toLocaleString('zh-CN', {
                    year: 'numeric',
                    month: '2-digit',
                    day: '2-digit',
                    hour: '2-digit',
                    minute: '2-digit',
                    second: '2-digit'
                });
        });
          container.appendChild(clone);
        });
      } catch (error) {
        console.error('获取视频片段失败:', error);
        // 可添加UI错误提示
        alert('无法加载视频片段，请稍后重试');
      }
    }
  
    // 在DOM加载时添加日期选择器监听
    document.addEventListener('DOMContentLoaded', () => {
      // 设置日期选择器默认值为今天
      const today = new Date().toISOString().split('T')[0];
      document.getElementById('datePicker').value = today;

      document.getElementById('datePicker').addEventListener('change', loadVideoClips);

      // 页面加载时立即加载视频片段
      loadVideoClips();

      const player = document.getElementById('clipPlayer');
      // 播放器控制逻辑...
    });
  </script>
    <script>
     window.difyChatbotConfig = {
      token: 'UaIvYCE9BRHuDdhG',
      baseUrl: 'http://localhost',
      systemVariables: {
        // user_id: 'YOU CAN DEFINE USER ID HERE',
        // conversation_id: 'YOU CAN DEFINE CONVERSATION ID HERE, IT MUST BE A VALID UUID',
      },
     }
    </script>
    <script
     src="http://localhost/embed.min.js"
     id="UaIvYCE9BRHuDdhG"
     defer>
    </script>
    <style>
      #dify-chatbot-bubble-button {
        background-color: #16213e !important;
      }
      #dify-chatbot-bubble-window {
        width: 24rem !important;
        height: 40rem !important;
      }
    </style>
</body>
</html>
