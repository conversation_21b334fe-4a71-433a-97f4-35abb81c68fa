// 视频源配置功能
function initVideoSourceConfig() {
    // 视频源类型切换
    document.querySelectorAll('input[name="videoSourceType"]').forEach(radio => {
        radio.addEventListener('change', toggleVideoSourceType);
    });

    // 文件选择
    document.getElementById('selectFileBtn').addEventListener('click', () => {
        document.getElementById('videoFileInput').click();
    });

    document.getElementById('videoFileInput').addEventListener('change', handleFileSelect);

    // 应用视频源 - RTSP
    document.getElementById('applyVideoSource').addEventListener('click', applyRtspSource);

    // 应用视频源 - 文件
    document.getElementById('applyFileSource').addEventListener('click', applyFileSource);

    // 加载当前视频源信息
    loadCurrentVideoSource();
}

function toggleVideoSourceType() {
    const selectedType = document.querySelector('input[name="videoSourceType"]:checked').value;
    const fileSection = document.getElementById('fileUploadSection');
    const rtspSection = document.getElementById('rtspSection');
    const fileApplyBtn = document.getElementById('applyFileSource');
    const rtspApplyBtn = document.getElementById('applyVideoSource');

    if (selectedType === 'rtsp') {
        rtspSection.classList.remove('hidden');
        fileSection.classList.add('hidden');
        fileApplyBtn.classList.add('hidden');
        rtspApplyBtn.classList.remove('hidden');
    } else {
        fileSection.classList.remove('hidden');
        rtspSection.classList.add('hidden');
        rtspApplyBtn.classList.add('hidden');

        // 如果已选择文件，显示文件应用按钮
        const fileInput = document.getElementById('videoFileInput');
        if (fileInput.files.length > 0) {
            fileApplyBtn.classList.remove('hidden');
        } else {
            fileApplyBtn.classList.add('hidden');
        }
    }
}

function handleFileSelect(event) {
    const file = event.target.files[0];
    const fileNameSpan = document.getElementById('selectedFileName');
    const fileApplyBtn = document.getElementById('applyFileSource');

    if (file) {
        fileNameSpan.textContent = file.name;
        fileNameSpan.classList.remove('text-gray-400');
        fileNameSpan.classList.add('text-green-400');
        fileApplyBtn.classList.remove('hidden');
    } else {
        fileNameSpan.textContent = '未选择文件';
        fileNameSpan.classList.remove('text-green-400');
        fileNameSpan.classList.add('text-gray-400');
        fileApplyBtn.classList.add('hidden');
    }
}

async function applyRtspSource() {
    const rtspUrl = document.getElementById('rtspUrl').value.trim();
    const applyBtn = document.getElementById('applyVideoSource');

    if (!rtspUrl) {
        showNotification('请输入RTSP地址', 'error');
        return;
    }

    const originalText = applyBtn.innerHTML;
    applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>应用中';
    applyBtn.disabled = true;

    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/set-video-source`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            headers: { 'Content-Type': 'application/json' },
            body: JSON.stringify({ video_source: rtspUrl })
        });

        if (response.ok) {
            showNotification('RTSP视频源设置成功', 'success');
            loadCurrentVideoSource();
        } else {
            const error = await response.json();
            throw new Error(error.detail || '设置失败');
        }
    } catch (error) {
        showNotification(`设置失败: ${error.message}`, 'error');
    } finally {
        applyBtn.innerHTML = originalText;
        applyBtn.disabled = false;
    }
}

async function applyFileSource() {
    const fileInput = document.getElementById('videoFileInput');
    const applyBtn = document.getElementById('applyFileSource');

    if (!fileInput.files[0]) {
        showNotification('请先选择视频文件', 'error');
        return;
    }

    const originalText = applyBtn.innerHTML;
    applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin mr-2"></i>上传中';
    applyBtn.disabled = true;

    try {
        const formData = new FormData();
        formData.append('file', fileInput.files[0]);

        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/upload-video`;

        const response = await fetch(apiUrl, {
            method: 'POST',
            body: formData
        });

        if (response.ok) {
            const result = await response.json();
            showNotification('视频文件上传成功', 'success');
            loadCurrentVideoSource();
            // 隐藏应用按钮
            document.getElementById('applyFileSource').classList.add('hidden');
        } else {
            const error = await response.json();
            throw new Error(error.detail || '上传失败');
        }
    } catch (error) {
        showNotification(`上传失败: ${error.message}`, 'error');
    } finally {
        applyBtn.innerHTML = originalText;
        applyBtn.disabled = false;
    }
}

async function loadCurrentVideoSource() {
    try {
        // 使用正确的后端服务地址和端口
        const host = window.location.hostname;
        const port = '16532'; // 后端服务端口
        const apiUrl = `http://${host}:${port}/api/config`;

        const response = await fetch(apiUrl);
        if (response.ok) {
            const data = await response.json();
            const videoSource = data.config.video_source || '未设置';

            // 更新视频源显示
            const sourceDisplay = document.getElementById('currentVideoSource');
            if (videoSource !== '未设置') {
                // 简化显示，只显示文件名或RTSP地址的关键部分
                let displayText = videoSource;
                if (videoSource.includes('/')) {
                    displayText = videoSource.split('/').pop(); // 获取文件名
                }
                if (displayText.length > 20) {
                    displayText = displayText.substring(0, 17) + '...';
                }
                sourceDisplay.textContent = displayText;
            } else {
                sourceDisplay.textContent = '未设置';
            }

            // 检查视频连接状态
            updateVideoStatus();
        }
    } catch (error) {
        console.error('加载视频源信息失败:', error);
    }
}

function updateVideoStatus() {
    const videoElement = document.getElementById('videoFeed');
    const statusIndicator = document.getElementById('videoStatus');
    const statusText = document.getElementById('videoSourceDisplay');

    // 检查WebSocket连接状态和图片是否有内容
    if (videoSocket && videoSocket.readyState === WebSocket.OPEN && videoElement && videoElement.src && videoElement.src !== window.location.href) {
        statusIndicator.className = 'inline-block w-3 h-3 rounded-full bg-green-500 mr-2';
        statusText.textContent = '已连接';
        statusText.className = 'text-sm text-green-400';
    } else if (videoSocket && videoSocket.readyState === WebSocket.CONNECTING) {
        statusIndicator.className = 'inline-block w-3 h-3 rounded-full bg-yellow-500 mr-2';
        statusText.textContent = '连接中';
        statusText.className = 'text-sm text-yellow-400';
    } else {
        statusIndicator.className = 'inline-block w-3 h-3 rounded-full bg-red-500 mr-2';
        statusText.textContent = '未连接';
        statusText.className = 'text-sm text-gray-400';
    }
}
