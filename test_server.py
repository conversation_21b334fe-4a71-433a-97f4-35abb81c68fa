#!/usr/bin/env python3
"""
简单的HTTP测试服务器
用于测试语音助手状态更新功能
"""

import http.server
import socketserver
import os
import json
import datetime
import random
from urllib.parse import urlparse, parse_qs

# 模拟语音助手状态
voice_assistant_state = {
    "active": False,
    "listening": False,
    "conversation_active": False,
    "sleeping": False,
    "last_interaction": None
}

class TestHTTPRequestHandler(http.server.SimpleHTTPRequestHandler):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, directory=".", **kwargs)
    
    def do_GET(self):
        parsed_path = urlparse(self.path)
        
        # 处理根路径
        if parsed_path.path == '/':
            self.serve_index()
        # 处理静态文件
        elif parsed_path.path.startswith('/static/'):
            self.serve_static_file(parsed_path.path)
        # 处理API请求
        elif parsed_path.path.startswith('/api/'):
            self.handle_api_get(parsed_path.path)
        else:
            super().do_GET()
    
    def do_POST(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path.startswith('/api/'):
            self.handle_api_post(parsed_path.path)
        else:
            self.send_error(404)
    
    def do_DELETE(self):
        parsed_path = urlparse(self.path)
        
        if parsed_path.path.startswith('/api/'):
            self.handle_api_delete(parsed_path.path)
        else:
            self.send_error(404)
    
    def serve_index(self):
        """提供index.html页面"""
        try:
            with open('web/templates/index.html', 'r', encoding='utf-8') as f:
                content = f.read()
            
            self.send_response(200)
            self.send_header('Content-type', 'text/html; charset=utf-8')
            self.end_headers()
            self.wfile.write(content.encode('utf-8'))
        except FileNotFoundError:
            self.send_error(404, "Index file not found")
    
    def serve_static_file(self, path):
        """提供静态文件"""
        file_path = path[1:]  # 移除开头的 '/'
        
        if os.path.exists(file_path):
            try:
                with open(file_path, 'rb') as f:
                    content = f.read()
                
                # 根据文件扩展名设置Content-Type
                if file_path.endswith('.js'):
                    content_type = 'application/javascript'
                elif file_path.endswith('.css'):
                    content_type = 'text/css'
                elif file_path.endswith('.html'):
                    content_type = 'text/html'
                else:
                    content_type = 'application/octet-stream'
                
                self.send_response(200)
                self.send_header('Content-type', f'{content_type}; charset=utf-8')
                self.end_headers()
                self.wfile.write(content)
            except Exception as e:
                self.send_error(500, f"Error reading file: {e}")
        else:
            self.send_error(404, f"File not found: {file_path}")
    
    def handle_api_get(self, path):
        """处理API GET请求"""
        if path == '/api/voice/status':
            # 模拟更真实的语音状态逻辑
            global voice_assistant_state

            # 如果语音助手未启动，所有状态都应该是False
            if not voice_assistant_state["active"]:
                voice_assistant_state["listening"] = False
                voice_assistant_state["conversation_active"] = False
                voice_assistant_state["sleeping"] = False
            else:
                # 语音助手启动时，模拟更真实的状态变化
                # 监听状态：启动后大部分时间在监听
                voice_assistant_state["listening"] = random.choice([True, True, True, False])

                # 对话状态：偶尔进入对话状态
                voice_assistant_state["conversation_active"] = random.choice([True, False, False, False])

                # 休眠状态：很少休眠
                voice_assistant_state["sleeping"] = random.choice([True, False, False, False, False])

            response = {
                "status": "success",
                "message": "获取语音状态成功",
                "data": voice_assistant_state
            }
        elif path == '/api/voice/conversation-history':
            # 模拟对话历史数据
            now = datetime.datetime.now()
            response = {
                "status": "success",
                "message": "获取对话历史成功",
                "data": {
                    "history": [
                        {
                            "role": "user",
                            "content": "今天天气怎么样？",
                            "timestamp": (now - datetime.timedelta(minutes=5)).isoformat()
                        },
                        {
                            "role": "assistant",
                            "content": "今天天气晴朗，温度适宜，是个不错的天气。",
                            "timestamp": (now - datetime.timedelta(minutes=4, seconds=30)).isoformat()
                        }
                    ],
                    "count": 2,
                    "timestamp": now.isoformat()
                }
            }
        elif path == '/api/config':
            response = {
                "status": "success",
                "config": {
                    "video_source": "test/test.mp4",
                    "current_model": {"name": "helmet"}
                }
            }
        else:
            response = {"status": "error", "message": "API endpoint not found"}
        
        self.send_json_response(response)
    
    def handle_api_post(self, path):
        """处理API POST请求"""
        try:
            content_length = int(self.headers['Content-Length'])
            post_data = self.rfile.read(content_length)
            data = json.loads(post_data.decode('utf-8'))
        except:
            data = {}
        
        global voice_assistant_state
        
        if path == '/api/voice/control':
            action = data.get('action', '')
            if action == 'start':
                voice_assistant_state["active"] = True
                voice_assistant_state["last_interaction"] = datetime.datetime.now().isoformat()
                response = {
                    "status": "success",
                    "message": "语音助手启动成功",
                    "data": voice_assistant_state
                }
            elif action == 'stop':
                voice_assistant_state["active"] = False
                voice_assistant_state["listening"] = False
                voice_assistant_state["conversation_active"] = False
                voice_assistant_state["sleeping"] = False
                response = {
                    "status": "success",
                    "message": "语音助手停止成功",
                    "data": voice_assistant_state
                }
            else:
                response = {"status": "error", "message": "Invalid action"}
        elif path == '/api/analysis/start':
            response = {
                "status": "success",
                "message": "Analysis started",
                "algorithms": data.get('algorithms', [])
            }
        elif path == '/api/analysis/stop':
            response = {
                "status": "success",
                "message": "Analysis stopped"
            }
        elif path == '/api/ask':
            response = {
                "status": "success",
                "answer": f"这是对问题 '{data.get('question', '')}' 的模拟回答"
            }
        else:
            response = {"status": "error", "message": "API endpoint not found"}
        
        self.send_json_response(response)
    
    def handle_api_delete(self, path):
        """处理API DELETE请求"""
        if path == '/api/voice/conversation-history':
            response = {
                "status": "success",
                "message": "对话历史已清空",
                "data": {
                    "timestamp": datetime.datetime.now().isoformat()
                }
            }
        else:
            response = {"status": "error", "message": "API endpoint not found"}
        
        self.send_json_response(response)
    
    def send_json_response(self, data):
        """发送JSON响应"""
        json_data = json.dumps(data, ensure_ascii=False, indent=2)
        
        self.send_response(200)
        self.send_header('Content-type', 'application/json; charset=utf-8')
        self.send_header('Access-Control-Allow-Origin', '*')
        self.send_header('Access-Control-Allow-Methods', 'GET, POST, DELETE, OPTIONS')
        self.send_header('Access-Control-Allow-Headers', 'Content-Type')
        self.end_headers()
        self.wfile.write(json_data.encode('utf-8'))

def main():
    PORT = 16532
    
    with socketserver.TCPServer(("", PORT), TestHTTPRequestHandler) as httpd:
        print(f"🚀 测试服务器启动在端口 {PORT}")
        print(f"📱 访问地址: http://localhost:{PORT}")
        print("🔧 支持的API:")
        print("   - GET  /api/voice/status")
        print("   - POST /api/voice/control")
        print("   - GET  /api/voice/conversation-history")
        print("   - DELETE /api/voice/conversation-history")
        print("   - POST /api/analysis/start")
        print("   - POST /api/analysis/stop")
        print("   - POST /api/ask")
        print("   - GET  /api/config")
        print("\n按 Ctrl+C 停止服务器")
        
        try:
            httpd.serve_forever()
        except KeyboardInterrupt:
            print("\n🛑 服务器已停止")

if __name__ == "__main__":
    main()
