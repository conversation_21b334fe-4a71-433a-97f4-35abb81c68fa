
/* ai_security_monitor/frontend/css/style.css */
@import url('https://fonts.loli.net/css2?family=Noto+Sans+SC:wght@400;500;700&display=swap');

:root {
  --primary-gradient: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%);
  --secondary-gradient: linear-gradient(to right, #3b82f6, #8b5cf6);
  --alert-red: #ef4444;
  --alert-yellow: #f59e0b;
  --alert-blue: #3b82f6;
  --bg-dark: #0f172a;
  --bg-light: #1e293b;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Noto Sans SC', sans-serif;
  background: var(--primary-gradient);
  color: #e2e8f0;
  min-height: 100vh;
  line-height: 1.6;
}

.container {
  width: 100%;
  max-width: 1400px;
  margin: 0 auto;
  padding: 0 1rem;
}

/* 视频区域样式 */
.video-container {
  position: relative;
  border-radius: 0.75rem;
  overflow: hidden;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.3);
  transition: all 0.3s ease;
  background: var(--bg-light);
}

.video-container:hover {
  transform: translateY(-2px);
  box-shadow: 0 15px 30px rgba(0, 0, 0, 0.4);
}

#videoFeed {
  width: 100%;
  height: auto;
  max-height: 500px;
  object-fit: cover;
}

.video-controls {
  padding: 1rem;
  background: var(--bg-dark);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* 预警信息区域样式 */
.alert-panel {
  background: var(--bg-light);
  border-radius: 0.75rem;
  padding: 1.5rem;
  height: 100%;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.alert-count {
  background: var(--alert-red);
  color: white;
  font-size: 0.75rem;
  font-weight: bold;
  padding: 0.25rem 0.5rem;
  border-radius: 9999px;
}

.alert-list {
  max-height: 600px;
  overflow-y: auto;
  padding-right: 0.5rem;
}

.alert-item {
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: 0.5rem;
  padding: 1rem;
  margin-bottom: 0.75rem;
  border-left: 4px solid var(--alert-red);
  transition: all 0.3s ease;
}

.alert-item:hover {
  transform: translateY(-3px);
  box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2);
}

.alert-item.warning {
  border-left-color: var(--alert-yellow);
}

.alert-item.info {
  border-left-color: var(--alert-blue);
}

/* 控制面板样式 */
.control-panel {
  background: var(--bg-light);
  border-radius: 0.75rem;
  padding: 1.5rem;
  margin-top: 1.5rem;
}

.control-group {
  margin-bottom: 1rem;
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-weight: 500;
  transition: all 0.2s ease;
  cursor: pointer;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn-secondary {
  background: #8b5cf6;
  color: white;
}

.btn-secondary:hover {
  background: #7c3aed;
}

.btn-danger {
  background: #ef4444;
  color: white;
}

.btn-danger:hover {
  background: #dc2626;
}

/* 响应式调整 */
@media (max-width: 1024px) {
  .container {
    padding: 0 0.5rem;
  }
  
  .video-container {
    margin-bottom: 1rem;
  }
}

@media (max-width: 768px) {
  .video-controls {
    flex-direction: column;
    gap: 0.5rem;
  }
  
  .alert-item {
    padding: 0.75rem;
  }
}

/* 动画效果 */
@keyframes pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

.pulse {
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.chat-message.user {
    background: #4f46e5;
    margin-left: auto;
}

.chat-message.system {
    background: #374151;
    margin-right: auto;
}

#chatContainer::-webkit-scrollbar {
    width: 6px;
}

#chatContainer::-webkit-scrollbar-track {
    background: #1f2937;
}

#chatContainer::-webkit-scrollbar-thumb {
    background: #4f46e5;
    border-radius: 3px;
}
