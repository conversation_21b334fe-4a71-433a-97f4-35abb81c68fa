#!/usr/bin/env python3
"""
测试语音API是否正常工作
"""

import requests
import json
import sys
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_voice_api():
    """测试语音API"""
    base_url = "http://127.0.0.1:16532"
    
    print("测试语音API连接...")
    print("=" * 50)
    
    # 测试1: 检查对话历史API
    try:
        print("1. 测试对话历史API...")
        response = requests.get(f"{base_url}/api/voice/conversation-history", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if data.get('status') == 'success':
                history = data.get('data', {}).get('history', [])
                print(f"   对话记录数量: {len(history)}")
                
                if len(history) > 0:
                    print("   ✅ 找到对话记录!")
                    for i, msg in enumerate(history[-3:]):  # 显示最后3条
                        role = msg.get('role', 'unknown')
                        content = msg.get('content', '')
                        timestamp = msg.get('timestamp', '')
                        print(f"      {i+1}. [{role}] {content[:50]}... ({timestamp})")
                else:
                    print("   ⚠️ 没有对话记录")
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            print(f"   响应内容: {response.text}")
            
    except requests.exceptions.ConnectionError:
        print("   ❌ 连接失败 - 服务器可能未启动")
        return False
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
        return False
    
    # 测试2: 检查语音助手状态
    try:
        print("\n2. 测试语音助手状态API...")
        response = requests.post(f"{base_url}/api/voice/control", 
                               json={"action": "status"}, 
                               timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
            
            if data.get('status') == 'success':
                voice_data = data.get('data', {})
                active = voice_data.get('active', False)
                listening = voice_data.get('listening', False)
                conversation_active = voice_data.get('conversation_active', False)
                
                print(f"   语音助手状态:")
                print(f"     - 激活: {active}")
                print(f"     - 监听: {listening}")
                print(f"     - 对话中: {conversation_active}")
                
                if active:
                    print("   ✅ 语音助手已启动")
                else:
                    print("   ⚠️ 语音助手未启动")
            else:
                print(f"   ❌ API返回错误: {data}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    # 测试3: 检查健康状态
    try:
        print("\n3. 测试健康检查API...")
        response = requests.get(f"{base_url}/api/voice/health", timeout=5)
        print(f"   状态码: {response.status_code}")
        
        if response.status_code == 200:
            data = response.json()
            print(f"   响应: {json.dumps(data, ensure_ascii=False, indent=2)}")
        else:
            print(f"   ❌ HTTP错误: {response.status_code}")
            
    except Exception as e:
        print(f"   ❌ 请求失败: {e}")
    
    print("\n" + "=" * 50)
    return True

def test_manual_conversation():
    """手动添加测试对话"""
    print("\n手动测试对话记录...")
    print("=" * 50)
    
    try:
        # 导入语音控制器
        from src.voice.voice_controller import voice_assistant
        
        if voice_assistant is None:
            print("❌ 语音助手实例不存在")
            print("请先通过API启动语音助手:")
            print("POST /api/voice/control")
            print('{"action": "start"}')
            return False
        
        # 手动添加测试对话
        print("添加测试对话...")
        voice_assistant.dialogue_manager.add_message("user", "你好，我想查询今天的警报")
        voice_assistant.dialogue_manager.add_message("assistant", "您好！今天共有3个警报事件，都已处理完毕")
        voice_assistant.dialogue_manager.add_message("user", "能详细说明一下吗？")
        voice_assistant.dialogue_manager.add_message("assistant", "当然可以。第一个是入侵检测警报...")
        
        print("✅ 测试对话已添加")
        
        # 检查对话历史
        history = voice_assistant.dialogue_manager.conversation_history
        print(f"对话记录数量: {len(history)}")
        
        for i, msg in enumerate(history):
            role = msg.get('role', 'unknown')
            content = msg.get('content', '')
            print(f"  {i+1}. [{role}] {content}")
        
        return True
        
    except Exception as e:
        print(f"❌ 手动测试失败: {e}")
        return False

def main():
    print("语音API测试工具")
    print("=" * 80)
    
    # 测试API连接
    api_ok = test_voice_api()
    
    if api_ok:
        # 测试手动对话
        manual_ok = test_manual_conversation()
        
        if manual_ok:
            print("\n🎯 建议:")
            print("1. 刷新浏览器页面")
            print("2. 打开开发者工具查看控制台日志")
            print("3. 检查对话记录区域是否显示测试对话")
        else:
            print("\n🎯 建议:")
            print("1. 通过前端界面启动语音助手")
            print("2. 或通过API启动: POST /api/voice/control {\"action\": \"start\"}")
    else:
        print("\n❌ API测试失败")
        print("请确保:")
        print("1. 主服务器正在运行 (python main.py)")
        print("2. 服务器端口是30367")
        print("3. 语音API已正确集成")

if __name__ == "__main__":
    main()
