#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版RAG查询模块 - 专用于语音系统测试
功能：提供基本的查询响应，不依赖复杂的视频分析模块
"""

import asyncio
import json
import datetime
import re
from typing import Optional

# 尝试导入必需的依赖
try:
    import requests
    REQUESTS_AVAILABLE = True
except ImportError:
    REQUESTS_AVAILABLE = False
    print("Requests不可用，将使用模拟LLM响应")

try:
    from pymilvus import connections, Collection
    MILVUS_AVAILABLE = True
except ImportError:
    MILVUS_AVAILABLE = False
    print("Milvus不可用，将使用模拟向量检索")

# 尝试导入配置
try:
    from src.core.config_adapter import RAGConfig, APIConfig
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    print("配置文件不可用，使用默认配置")
    
    # 默认配置
    class RAGConfig:
        ENABLE_RAG = False
        MILVUS_HOST = "localhost"
        MILVUS_PORT = "19530"
        VECTOR_API_URL = "http://localhost:11434/api/embeddings"
    
    class APIConfig:
        MOONSHOT_API_URL = "http://localhost:11434/v1/chat/completions"
        MOONSHOT_MODEL = "qwen3:4b"
        MOONSHOT_API_KEY = ""
        REQUEST_TIMEOUT = 60.0

def get_embeddings(text: str) -> list:
    """获取文本嵌入向量 - 简化版"""
    if not REQUESTS_AVAILABLE:
        # 返回模拟向量
        return [0.1] * 768
    
    try:
        response = requests.post(
            RAGConfig.VECTOR_API_URL,
            json={"model": "nomic-embed-text:latest", "prompt": text},
            timeout=10
        )
        if response.status_code == 200:
            return response.json().get("embedding", [0.1] * 768)
        else:
            return [0.1] * 768
    except Exception as e:
        print(f"向量化失败: {e}")
        return [0.1] * 768

async def chat_request(message: str) -> str:
    """LLM聊天请求 - 简化版"""
    if not REQUESTS_AVAILABLE:
        return get_mock_response(message)
    
    try:
        import httpx
        
        url = APIConfig.MOONSHOT_API_URL
        model = APIConfig.MOONSHOT_MODEL
        
        messages = [{"role": "user", "content": message}]
        headers = {
            "Content-Type": "application/json",
            "Authorization": f"Bearer {APIConfig.MOONSHOT_API_KEY}"
        }
        
        data = {
            "model": model,
            "messages": messages,
            "temperature": 0.5,
            "max_tokens": 1000
        }
        
        async with httpx.AsyncClient(timeout=httpx.Timeout(APIConfig.REQUEST_TIMEOUT)) as client:
            response = await client.post(url, headers=headers, json=data)
            response_data = response.json()
            return response_data['choices'][0]['message']['content']
            
    except Exception as e:
        print(f"LLM请求失败: {e}")
        return get_mock_response(message)

def get_mock_response(query: str) -> str:
    """获取模拟响应"""
    query_lower = query.lower()
    
    # 根据查询内容返回相应的模拟回复
    if "警报" in query_lower or "alert" in query_lower:
        return "根据系统记录，最近24小时内共检测到3个警报事件，主要类型包括入侵检测和异常行为监控。"
    
    elif "统计" in query_lower or "数据" in query_lower:
        return "系统统计信息：今日检测事件15次，误报率2.3%，系统运行正常。"
    
    elif "状态" in query_lower or "系统" in query_lower:
        return "AI安全监控系统运行状态良好，所有模块正常工作，当前监控模式为实时检测。"
    
    elif "帮助" in query_lower or "功能" in query_lower:
        return "我是AI安全监控助手，可以帮您查询警报信息、系统状态、统计数据等。您可以问我关于监控系统的任何问题。"
    
    elif "你好" in query_lower or "hello" in query_lower:
        return "您好！我是AI安全监控智能助手，很高兴为您服务。请问有什么可以帮助您的吗？"
    
    elif "时间" in query_lower:
        current_time = datetime.datetime.now().strftime("%Y年%m月%d日 %H:%M:%S")
        return f"当前时间是{current_time}。"
    
    else:
        return f"我理解您询问的是：{query}。作为AI安全监控助手，我建议您查看系统日志或联系技术支持获取更详细的信息。"

async def vector_search_mock(query_vector: list, top_k: int = 3) -> list:
    """模拟向量检索"""
    # 返回模拟的检索结果
    mock_results = [
        "系统检测到异常行为，已记录相关信息",
        "监控摄像头运行正常，图像质量良好",
        "AI模型分析完成，未发现安全威胁"
    ]
    return mock_results[:top_k]

async def rag_query(user_query: str, top_k: int = 3) -> str:
    """RAG查询主函数 - 简化版"""
    try:
        # 如果RAG功能不可用，直接使用LLM
        if not RAGConfig.ENABLE_RAG or not MILVUS_AVAILABLE:
            print("使用简化模式进行查询...")
            
            # 构建增强提示词
            current_time = datetime.datetime.now().strftime('%Y年%m月%d日 %H:%M')
            prompt = f"""
[系统角色] 你是AI安全监控智能助手小凯。

当前时间：{current_time}
用户查询：{user_query}

请根据以下上下文信息回答用户问题：
- 系统运行状态：正常
- 监控模式：实时检测
- 最近警报：3个事件（入侵检测、异常行为）
- 系统性能：CPU使用率45%，内存使用率60%

请简洁、准确地回答用户问题。
"""
            
            response = await chat_request(prompt)
            return response
        
        # 完整RAG流程
        else:
            print("使用完整RAG模式进行查询...")
            
            # 获取查询向量
            query_vector = get_embeddings(user_query)
            
            # 向量检索
            if MILVUS_AVAILABLE:
                try:
                    connections.connect(
                        alias="default",
                        host=RAGConfig.MILVUS_HOST,
                        port=RAGConfig.MILVUS_PORT
                    )
                    
                    collection = Collection("knowledge_base")
                    collection.load()
                    
                    search_params = {"metric_type": "L2", "params": {"nprobe": 10}}
                    results = collection.search(
                        data=[query_vector],
                        anns_field="embedding",
                        param=search_params,
                        limit=top_k,
                        output_fields=["text"]
                    )
                    
                    retrieved_texts = [hit.entity.get("text") for hit in results[0]]
                    
                except Exception as e:
                    print(f"向量检索失败: {e}")
                    retrieved_texts = await vector_search_mock(query_vector, top_k)
            else:
                retrieved_texts = await vector_search_mock(query_vector, top_k)
            
            # 构建增强提示词
            current_time = datetime.datetime.now().strftime('%Y年%m月%d日')
            augmented_prompt = f"""
[系统角色] 你是AI安全监控智能助手小凯。

当前日期：{current_time}
用户查询：{user_query}

相关上下文信息：
{chr(10).join([f"- {text}" for text in retrieved_texts])}

请根据上下文信息简洁地回答用户问题。
"""
            
            # 调用LLM
            response = await chat_request(augmented_prompt)
            
            # 清理响应
            cleaned_response = re.sub(r'<think[^>]*>.*?</think>', '', response, flags=re.DOTALL)
            return cleaned_response.strip()
    
    except Exception as e:
        print(f"RAG查询异常: {e}")
        return get_mock_response(user_query)

# 测试函数
async def test_rag_query():
    """测试RAG查询功能"""
    test_queries = [
        "你好，请介绍一下系统功能",
        "查询最近的警报信息",
        "系统运行状态如何",
        "帮助我了解监控功能"
    ]
    
    print("🧪 测试RAG查询功能...")
    
    for query in test_queries:
        try:
            print(f"\n查询: {query}")
            response = await rag_query(query)
            print(f"回复: {response[:100]}...")
        except Exception as e:
            print(f"查询失败: {e}")

if __name__ == "__main__":
    # 运行测试
    try:
        if hasattr(asyncio, 'run'):
            asyncio.run(test_rag_query())
        else:
            loop = asyncio.get_event_loop()
            loop.run_until_complete(test_rag_query())
    except Exception as e:
        print(f"测试异常: {e}")
        import traceback
        traceback.print_exc()
