#!/usr/bin/env python3
"""
专门测试配置导入是否修复
"""

import sys
import os
from pathlib import Path

# 设置项目根目录
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))
os.chdir(project_root)

def test_config_import_only():
    """只测试配置导入，不涉及其他依赖"""
    print("测试配置导入修复...")
    print("=" * 40)
    
    try:
        # 测试直接导入配置
        from src.core.config_adapter import RAGConfig, APIConfig, VideoConfig
        print("✅ 从 src.core.config_adapter 导入成功")
        
        # 测试配置值
        print(f"   RAG启用: {RAGConfig.ENABLE_RAG}")
        print(f"   Milvus主机: {RAGConfig.MILVUS_HOST}")
        print(f"   历史文件: {RAGConfig.HISTORY_FILE}")
        
        return True
        
    except ImportError as e:
        print(f"❌ 配置导入失败: {e}")
        return False
    except Exception as e:
        print(f"❌ 其他错误: {e}")
        return False

def test_utility_config_import():
    """测试utility.py中的配置导入"""
    print("\n测试utility.py配置导入...")
    print("-" * 30)
    
    try:
        # 检查utility.py文件内容
        with open("utils/utility.py", 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含正确的导入语句
        if "from src.core.config_adapter import" in content:
            print("✅ utility.py 包含正确的导入语句")
        else:
            print("❌ utility.py 缺少正确的导入语句")
            return False
            
        # 检查是否还有旧的导入语句
        if "from config import" in content:
            print("❌ utility.py 仍包含旧的导入语句")
            return False
        else:
            print("✅ utility.py 不包含旧的导入语句")
            
        return True
        
    except Exception as e:
        print(f"❌ 检查utility.py失败: {e}")
        return False

def main():
    print("配置导入修复验证")
    print("=" * 50)
    
    success = True
    
    # 测试配置导入
    if not test_config_import_only():
        success = False
    
    # 测试utility文件
    if not test_utility_config_import():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("🎉 配置导入修复成功!")
        print("'No module named config' 问题已解决")
        print("\n注意: 如果仍有RAG系统异常，可能是由于:")
        print("1. 缺少依赖包 (如 cv2, pymilvus 等)")
        print("2. Milvus服务未运行")
        print("3. 网络连接问题")
    else:
        print("❌ 配置导入仍有问题")
    
    return success

if __name__ == "__main__":
    main()
