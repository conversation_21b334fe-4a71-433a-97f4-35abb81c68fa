"""
配置适配器
将YAML配置转换为原有的配置类格式，保持向后兼容
"""

import logging
from typing import Dict, Any
from datetime import datetime
from src.core.config_loader import config_loader


class VideoConfigClass:
    """视频配置类"""
    @property
    def VIDEO_INTERVAL(self):
        return config_loader.get_video_processing_config()['video_interval']

    @property
    def ANALYSIS_INTERVAL(self):
        return config_loader.get_video_processing_config()['analysis_interval']

    @property
    def BUFFER_DURATION(self):
        return config_loader.get_video_processing_config()['buffer_duration']

    @property
    def WS_RETRY_INTERVAL(self):
        return config_loader.get_video_processing_config()['ws_retry_interval']

    @property
    def MAX_WS_QUEUE(self):
        return config_loader.get_video_processing_config()['max_ws_queue']

    @property
    def JPEG_QUALITY(self):
        return config_loader.get_video_processing_config()['jpeg_quality']


class APIConfigClass:
    """API配置类"""
    @property
    def QWEN_API_KEY(self):
        return config_loader.get_api_config()['qwen']['api_key']
    
    @property
    def QWEN_API_URL(self):
        return config_loader.get_api_config()['qwen']['api_url']
    
    @property
    def QWEN_MODEL(self):
        return config_loader.get_api_config()['qwen']['model']
    
    @property
    def MOONSHOT_API_KEY(self):
        return config_loader.get_api_config()['moonshot']['api_key']
    
    @property
    def MOONSHOT_API_URL(self):
        return config_loader.get_api_config()['moonshot']['api_url']
    
    @property
    def MOONSHOT_MODEL(self):
        return config_loader.get_api_config()['moonshot']['model']
    
    @property
    def REQUEST_TIMEOUT(self):
        return config_loader.get_api_config()['request']['timeout']
    
    @property
    def TEMPERATURE(self):
        return config_loader.get_api_config()['request']['temperature']
    
    @property
    def TOP_P(self):
        return config_loader.get_api_config()['request']['top_p']
    
    @property
    def TOP_K(self):
        return config_loader.get_api_config()['request']['top_k']
    
    @property
    def REPETITION_PENALTY(self):
        return config_loader.get_api_config()['request']['repetition_penalty']


class RAGConfigClass:
    """RAG配置类"""
    @property
    def ENABLE_RAG(self):
        return config_loader.get_rag_config()['enable_rag']
    
    @property
    def MILVUS_HOST(self):
        return config_loader.get_rag_config()['milvus']['host']
    
    @property
    def MILVUS_PORT(self):
        return config_loader.get_rag_config()['milvus']['port']
    
    @property
    def MILVUS_USER(self):
        return config_loader.get_rag_config()['milvus']['user']
    
    @property
    def MILVUS_PASSWORD(self):
        return config_loader.get_rag_config()['milvus']['password']
    
    @property
    def COLLECTION_NAME(self):
        return config_loader.get_rag_config()['milvus']['collection_name']
    
    @property
    def EMBEDDING_DIM(self):
        return config_loader.get_rag_config()['milvus']['embedding_dim']
    
    @property
    def VECTOR_API_URL(self):
        return config_loader.get_rag_config()['vector_api_url']
    
    @property
    def HISTORY_FILE(self):
        return config_loader.get_rag_config()['history_file']


class ServerConfigClass:
    """服务器配置类"""
    @property
    def HOST(self):
        return config_loader.get_server_config()['host']
    
    @property
    def PORT(self):
        return config_loader.get_server_config()['port']
    
    @property
    def RELOAD(self):
        return config_loader.get_server_config()['reload']
    
    @property
    def WORKERS(self):
        return config_loader.get_server_config()['workers']


# 创建实例
VideoConfig = VideoConfigClass()
APIConfig = APIConfigClass()
RAGConfig = RAGConfigClass()
ServerConfig = ServerConfigClass()

# 全局变量
def get_video_source():
    return config_loader.get_video_source()

def get_current_model():
    return config_loader.get_current_model()

def get_model_names():
    return config_loader.get_model_names()

def get_storage_config():
    return config_loader.get_storage_config()

def get_logging_config():
    return config_loader.get_logging_config()

# 向后兼容的变量
VIDEO_SOURCE = get_video_source()
current_model = get_current_model()
MODEL_NAMES = get_model_names()
ARCHIVE_DIR = get_storage_config()['archive_dir']
VIDEO_WARNING_DIR = get_storage_config()['video_warning_dir']

# 日志配置
LOG_CONFIG = {
    'level': getattr(logging, get_logging_config()['level']),
    'format': get_logging_config()['format'],
    'handlers': [
        {'type': 'file', 'filename': get_logging_config()['handlers'][0]['filename']},
        {'type': 'stream'}
    ]
}

# 异常监测参数
ANOMALY_SURVEILLANCE = [MODEL_NAMES[current_model["name"]]]
ALERT_NAMES = current_model["name"]

def update_config(args: Dict[str, Any]):
    """动态更新配置参数"""
    global VIDEO_SOURCE, ANOMALY_SURVEILLANCE, ALERT_NAMES
    
    # 更新视频源
    if 'video_source' in args:
        VIDEO_SOURCE = args['video_source']
        
    # 更新异常监测参数
    if 'anomaly_surveillance' in args:
        ANOMALY_SURVEILLANCE = args['anomaly_surveillance']
        
    # 更新警报名称
    if 'alert_names' in args:
        ALERT_NAMES = args['alert_names']
    
    # 注意：其他配置更新需要通过config_loader进行
