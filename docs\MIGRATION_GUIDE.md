# 项目结构优化迁移指南

## 概述

本文档说明了AI安全监控系统项目结构优化的详细变更，以及如何适应新的目录结构。

## 主要变更

### 1. 目录结构重组

#### 新增目录
- `configs/` - 配置文件目录
- `src/` - 源代码根目录
- `web/` - 前端代码目录
- `utils/` - 工具模块目录
- `data/` - 数据存储目录
- `logs/` - 日志文件目录
- `docs/` - 文档目录
- `test/` - 测试文件目录

#### 文件迁移映射

| 原路径 | 新路径 | 说明 |
|--------|--------|------|
| `config.py` | `src/core/config.py` | 核心配置文件 |
| `video_server.py` | `src/video/video_server.py` | 视频服务器 |
| `voice_*.py` | `src/voice/` | 语音相关模块 |
| `multi_modal_analyzer.py` | `src/models/multi_modal_analyzer.py` | 多模态分析器 |
| `custom_model_manager.py` | `src/models/custom_model_manager.py` | 自定义模型管理 |
| `rag_*.py` | `src/api/` | RAG查询模块 |
| `prompt.py` | `src/api/prompt.py` | 提示词模板 |
| `alert_statistics.py` | `utils/alert_statistics.py` | 警报统计 |
| `utility.py` | `utils/utility.py` | 通用工具 |
| `templates/` | `web/templates/` | HTML模板 |
| `static/` | `web/static/` | 静态资源 |
| `*.md` | `docs/` | 文档文件 |
| `test*.py`, `test*.mp4` | `test/` | 测试文件 |
| `uploads/` | `data/uploads/` | 上传文件 |
| `video_warning/` | `data/video_warning/` | 警告视频 |
| `voice/` | `data/voice/` | 语音数据 |
| `archive/` | `data/archive/` | 归档文件 |
| `*.log` | `logs/` | 日志文件 |

### 2. 配置文件优化

#### 新增YAML配置文件
- `configs/app_config.yaml` - 应用主配置
- `configs/voice_config.yaml` - 语音助手配置

#### 配置加载器
- `src/core/config_loader.py` - 统一的配置加载器

### 3. 导入路径更新

所有Python文件的导入路径已更新以适应新的目录结构：

```python
# 旧的导入方式
from config import VideoConfig
from utility import get_embeddings
from voice_assistant import VoiceConfig

# 新的导入方式
from src.core.config import VideoConfig
from utils.utility import get_embeddings
from src.voice.voice_assistant import VoiceConfig
```

### 4. 启动方式变更

#### 新的主启动文件
```bash
python main.py
```

#### 语音助手启动
```bash
python src/voice/start_voice_assistant.py
python src/voice/start_enhanced_voice_assistant.py
```

## 兼容性说明

### 向后兼容
- 保留了原始的`src/core/config.py`文件
- 所有原有功能保持不变
- API接口保持兼容

### 配置迁移
- 原有的Python配置仍然有效
- 新增的YAML配置提供更好的可维护性
- 可以逐步迁移到YAML配置

## 开发指南

### 新项目开发
1. 使用YAML配置文件进行配置管理
2. 按模块组织代码到相应目录
3. 使用新的导入路径

### 现有代码修改
1. 更新导入语句以使用新的路径
2. 考虑使用YAML配置替代硬编码配置
3. 将新功能按模块放置到合适目录

### 测试
1. 所有测试文件移动到`test/`目录
2. 使用`python -m pytest test/`运行测试
3. 测试数据统一存放在`test/`目录

## 部署说明

### 容器化部署
新的目录结构更适合容器化部署：

```dockerfile
# 示例Dockerfile结构
COPY configs/ /app/configs/
COPY src/ /app/src/
COPY web/ /app/web/
COPY utils/ /app/utils/
COPY main.py requirements.txt /app/
```

### 数据持久化
- `data/` 目录应该挂载为持久化存储
- `logs/` 目录可以挂载用于日志收集
- `configs/` 目录可以通过ConfigMap管理

## 故障排除

### 常见问题

1. **导入错误**
   - 检查Python路径设置
   - 确认所有`__init__.py`文件存在
   - 使用绝对导入路径

2. **配置文件找不到**
   - 确认配置文件在`configs/`目录
   - 检查工作目录是否正确
   - 使用配置加载器而不是直接读取文件

3. **静态文件404**
   - 确认静态文件在`web/static/`目录
   - 检查Web服务器配置
   - 更新模板中的静态文件路径

### 调试建议
1. 使用`python main.py`启动系统
2. 检查日志文件在`logs/`目录
3. 确认所有依赖模块正确导入

## 总结

这次项目结构优化带来了以下好处：
1. **更清晰的代码组织**：按功能模块分类
2. **更好的配置管理**：YAML配置文件
3. **更易于维护**：标准化的目录结构
4. **更适合部署**：容器化友好的结构
5. **更好的开发体验**：清晰的模块边界

建议开发者逐步适应新的目录结构，并在新功能开发中使用新的组织方式。
