#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FunASR参数测试脚本
功能：测试不同的FunASR参数组合，找到正确的配置
"""

import asyncio
import json
import struct
import wave
import sounddevice as sd

try:
    import websockets
    WEBSOCKETS_AVAILABLE = True
except ImportError:
    WEBSOCKETS_AVAILABLE = False
    print("❌ websockets库未安装")

# FunASR配置
FUNASR_WS_URL = "ws://192.168.3.95:10096/"
SAMPLE_RATE = 16000

class FunASRParameterTester:
    """FunASR参数测试器"""
    
    def __init__(self):
        self.ws_url = FUNASR_WS_URL
    
    def create_test_audio(self):
        """创建测试音频"""
        print("🎤 创建测试音频...")
        
        # 录制真实语音
        print("请说：'你好，测试语音识别'")
        try:
            audio_data = sd.rec(
                int(2 * SAMPLE_RATE),  # 2秒
                samplerate=SAMPLE_RATE,
                channels=1,
                dtype='int16'
            )
            sd.wait()
            
            filename = "param_test.wav"
            with wave.open(filename, 'wb') as wf:
                wf.setnchannels(1)
                wf.setsampwidth(2)
                wf.setframerate(SAMPLE_RATE)
                wf.writeframes(audio_data.tobytes())
            
            print(f"✅ 音频已保存: {filename}")
            return filename
            
        except Exception as e:
            print(f"❌ 录制失败: {e}")
            return None
    
    async def test_parameter_combination(self, audio_file, config_name, config):
        """测试特定参数组合"""
        print(f"\n🧪 测试配置: {config_name}")
        print(f"📋 参数: {config}")
        
        try:
            # 读取音频文件
            with wave.open(audio_file, 'rb') as wf:
                pcm_data = wf.readframes(wf.getnframes())
            
            pcm_int16 = struct.unpack(f'<{len(pcm_data)//2}h', pcm_data)
            
            async with websockets.connect(self.ws_url, timeout=10) as websocket:
                # 发送配置
                await websocket.send(json.dumps(config))
                print(f"📤 发送配置完成")
                
                # 发送音频数据
                chunk_size = config.get('chunk_size_samples', 960)
                chunk_count = 0
                
                for i in range(0, len(pcm_int16), chunk_size):
                    chunk = pcm_int16[i:i+chunk_size]
                    if len(chunk) > 0:
                        chunk_bytes = struct.pack(f'<{len(chunk)}h', *chunk)
                        await websocket.send(chunk_bytes)
                        chunk_count += 1
                
                print(f"📤 发送了 {chunk_count} 个音频块")
                
                # 发送结束信号
                end_config = config.copy()
                end_config['is_speaking'] = False
                await websocket.send(json.dumps(end_config))
                print("📤 发送结束信号")
                
                # 接收结果
                try:
                    response = await asyncio.wait_for(websocket.recv(), timeout=5)
                    print(f"📥 响应: {response}")
                    
                    if isinstance(response, str):
                        try:
                            data = json.loads(response)
                            text = data.get('text', '').strip()
                            if text:
                                print(f"✅ {config_name} 成功: '{text}'")
                                return text
                            else:
                                print(f"❌ {config_name} 失败: 无文本")
                        except:
                            print(f"❌ {config_name} 失败: JSON解析错误")
                    
                except asyncio.TimeoutError:
                    print(f"❌ {config_name} 失败: 响应超时")
                
        except Exception as e:
            print(f"❌ {config_name} 异常: {e}")
        
        return None
    
    async def run_parameter_tests(self):
        """运行参数测试"""
        print("\n" + "=" * 60)
        print("🔬 FunASR参数组合测试")
        print("=" * 60)
        
        if not WEBSOCKETS_AVAILABLE:
            print("❌ websockets库不可用")
            return
        
        # 创建测试音频
        audio_file = self.create_test_audio()
        if not audio_file:
            return
        
        # 定义不同的参数组合
        test_configs = [
            # 配置1: 基本2pass模式
            ("基本2pass模式", {
                "chunk_size": [5, 10, 5],
                "wav_name": "test1",
                "is_speaking": True,
                "chunk_interval": 10,
                "mode": "2pass",
                "chunk_size_samples": 960
            }),
            
            # 配置2: offline模式
            ("offline模式", {
                "chunk_size": [5, 10, 5],
                "wav_name": "test2",
                "is_speaking": True,
                "chunk_interval": 10,
                "mode": "offline",
                "chunk_size_samples": 960
            }),
            
            # 配置3: online模式
            ("online模式", {
                "chunk_size": [5, 10, 5],
                "wav_name": "test3",
                "is_speaking": True,
                "chunk_interval": 10,
                "mode": "online",
                "chunk_size_samples": 960
            }),
            
            # 配置4: 启用ITN
            ("启用ITN", {
                "chunk_size": [5, 10, 5],
                "wav_name": "test4",
                "is_speaking": True,
                "chunk_interval": 10,
                "mode": "2pass",
                "itn": True,
                "chunk_size_samples": 960
            }),
            
            # 配置5: 不同chunk_size
            ("不同chunk_size", {
                "chunk_size": [10, 5, 5],
                "wav_name": "test5",
                "is_speaking": True,
                "chunk_interval": 10,
                "mode": "2pass",
                "chunk_size_samples": 1920
            }),
            
            # 配置6: 添加语言参数
            ("添加语言参数", {
                "chunk_size": [5, 10, 5],
                "wav_name": "test6",
                "is_speaking": True,
                "chunk_interval": 10,
                "mode": "2pass",
                "language": "zh-cn",
                "chunk_size_samples": 960
            }),
            
            # 配置7: 添加热词
            ("添加热词", {
                "chunk_size": [5, 10, 5],
                "wav_name": "test7",
                "is_speaking": True,
                "chunk_interval": 10,
                "mode": "2pass",
                "hotwords": "你好 20 测试 15",
                "chunk_size_samples": 960
            }),
            
            # 配置8: 最小配置
            ("最小配置", {
                "wav_name": "test8",
                "is_speaking": True,
                "mode": "2pass",
                "chunk_size_samples": 960
            }),
        ]
        
        # 测试每个配置
        successful_configs = []
        
        for config_name, config in test_configs:
            result = await self.test_parameter_combination(audio_file, config_name, config)
            if result:
                successful_configs.append((config_name, config, result))
        
        # 显示结果
        print("\n" + "=" * 60)
        print("📊 测试结果总结")
        print("=" * 60)
        
        if successful_configs:
            print(f"✅ 成功的配置数量: {len(successful_configs)}")
            for config_name, config, result in successful_configs:
                print(f"\n🎯 {config_name}:")
                print(f"   识别结果: '{result}'")
                print(f"   配置参数: {config}")
            
            # 推荐最佳配置
            best_config = successful_configs[0]
            print(f"\n💡 推荐使用配置: {best_config[0]}")
            print(f"   参数: {best_config[1]}")
            
        else:
            print("❌ 所有配置都失败了")
            print("\n💡 建议检查:")
            print("1. FunASR服务器是否正确启动")
            print("2. 服务器版本和协议兼容性")
            print("3. 音频格式是否正确")
            print("4. 网络连接是否稳定")
        
        print("=" * 60)
        
        # 清理临时文件
        try:
            import os
            os.remove(audio_file)
        except:
            pass
        
        return len(successful_configs) > 0

async def main():
    """主函数"""
    tester = FunASRParameterTester()
    
    try:
        success = await tester.run_parameter_tests()
        if success:
            print("\n🎉 找到了可用的FunASR配置！")
        else:
            print("\n😞 未找到可用的FunASR配置。")
    except KeyboardInterrupt:
        print("\n测试被中断")
    except Exception as e:
        print(f"\n测试异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    # 兼容Python 3.6
    try:
        asyncio.run(main())
    except AttributeError:
        loop = asyncio.get_event_loop()
        loop.run_until_complete(main())
