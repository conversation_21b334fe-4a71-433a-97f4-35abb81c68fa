"""
YAML配置文件加载器
用于加载和管理系统配置
"""

import yaml
import os
from typing import Dict, Any
from datetime import datetime


class ConfigLoader:
    """配置加载器类"""
    
    def __init__(self, config_dir: str = "configs"):
        self.config_dir = config_dir
        self._app_config = None
        self._voice_config = None
    
    def load_app_config(self) -> Dict[str, Any]:
        """加载应用配置"""
        if self._app_config is None:
            config_path = os.path.join(self.config_dir, "app_config.yaml")
            with open(config_path, 'r', encoding='utf-8') as f:
                self._app_config = yaml.safe_load(f)
            
            # 设置当前时间戳
            if self._app_config['models']['current_model']['timestamp'] is None:
                self._app_config['models']['current_model']['timestamp'] = datetime.now().isoformat()
        
        return self._app_config
    
    def load_voice_config(self) -> Dict[str, Any]:
        """加载语音配置"""
        if self._voice_config is None:
            config_path = os.path.join(self.config_dir, "voice_config.yaml")
            with open(config_path, 'r', encoding='utf-8') as f:
                self._voice_config = yaml.safe_load(f)
        
        return self._voice_config
    
    def get_video_source(self) -> str:
        """获取视频源配置"""
        config = self.load_app_config()
        return config['video']['source']
    
    def get_current_model(self) -> Dict[str, Any]:
        """获取当前模型配置"""
        config = self.load_app_config()
        return config['models']['current_model']
    
    def get_model_names(self) -> Dict[str, str]:
        """获取模型名称映射"""
        config = self.load_app_config()
        return config['models']['model_names']
    
    def get_api_config(self) -> Dict[str, Any]:
        """获取API配置"""
        config = self.load_app_config()
        return config['api']
    
    def get_rag_config(self) -> Dict[str, Any]:
        """获取RAG配置"""
        config = self.load_app_config()
        return config['rag']
    
    def get_server_config(self) -> Dict[str, Any]:
        """获取服务器配置"""
        config = self.load_app_config()
        return config['server']
    
    def get_storage_config(self) -> Dict[str, Any]:
        """获取存储配置"""
        config = self.load_app_config()
        return config['storage']
    
    def get_logging_config(self) -> Dict[str, Any]:
        """获取日志配置"""
        config = self.load_app_config()
        return config['logging']
    
    def get_video_processing_config(self) -> Dict[str, Any]:
        """获取视频处理配置"""
        config = self.load_app_config()
        return config['video']['processing']
    
    def get_wake_words(self) -> list:
        """获取唤醒词列表"""
        config = self.load_voice_config()
        return config['wake_words']
    
    def get_funasr_config(self) -> Dict[str, Any]:
        """获取FunASR配置"""
        config = self.load_voice_config()
        return config['funasr']
    
    def get_tts_config(self) -> Dict[str, Any]:
        """获取TTS配置"""
        config = self.load_voice_config()
        return config['tts']
    
    def update_config(self, config_type: str, updates: Dict[str, Any]):
        """动态更新配置"""
        if config_type == "app":
            config = self.load_app_config()
            self._update_nested_dict(config, updates)
        elif config_type == "voice":
            config = self.load_voice_config()
            self._update_nested_dict(config, updates)
    
    def _update_nested_dict(self, d: Dict[str, Any], updates: Dict[str, Any]):
        """递归更新嵌套字典"""
        for key, value in updates.items():
            if isinstance(value, dict) and key in d and isinstance(d[key], dict):
                self._update_nested_dict(d[key], value)
            else:
                d[key] = value

    def reload_config(self):
        """重新加载配置，清除缓存"""
        self._app_config = None
        self._voice_config = None


# 全局配置加载器实例
config_loader = ConfigLoader()
