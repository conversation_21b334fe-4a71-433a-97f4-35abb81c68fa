# FunASR语音识别配置指南

## 概述

本指南介绍如何将项目中的语音识别从Whisper切换到FunASR，使用您部署的FunASR WebSocket服务。

## 配置说明

### 1. FunASR服务器配置

您的FunASR服务器地址：`ws://************:10096/`

### 2. 项目配置更新

已更新以下配置文件：

#### voice_config.py
```python
# FunASR配置
FUNASR_WS_URL = "ws://************:10096/"  # FunASR WebSocket地址
FUNASR_TIMEOUT = 10  # WebSocket连接超时时间(秒)
```

#### voice_assistant.py
- 添加了FunASR WebSocket支持
- 实现了FunASR优先，Whisper备用的识别策略
- 支持自动降级机制

## 依赖安装

确保安装了WebSocket支持：

```bash
pip install websockets
```

## 测试FunASR连接

运行测试脚本验证FunASR连接：

```bash
# 基础连接测试
python test_funasr.py

# 协议探索测试
python test_funasr_protocol.py

# 完整功能测试
python test_funasr_final.py
```

测试内容包括：
1. WebSocket连接测试
2. 协议格式验证
3. 音频录制测试
4. PCM格式转换
5. 语音识别测试

## 使用方法

### 1. 启动语音助手

```bash
# 推荐方式: 使用启动脚本
python start_voice_assistant.py

# 方式2: 直接启动语音控制器
python voice_controller.py

# 方式3: 通过主服务器启动（如果存在）
python video_server.py
```

### 2. 语音识别流程

1. **FunASR优先**: 系统首先尝试使用FunASR进行语音识别
2. **Whisper备用**: 如果FunASR不可用或识别失败，自动切换到Whisper
3. **模拟模式**: 如果两者都不可用，使用模拟识别

### 3. 唤醒词

支持的唤醒词：
- "小凯小凯" (主要唤醒词)
- "你好小凯" (礼貌唤醒)
- "小凯同学" (亲切称呼)
- "小凯助手" (正式称呼)
- "嘿小凯" (随意唤醒)

## 技术实现

### FunASR集成特性

1. **WebSocket连接**: 使用websockets库连接FunASR服务
2. **异步处理**: 支持异步语音识别，不阻塞主线程
3. **错误处理**: 完善的超时和异常处理机制
4. **自动降级**: FunASR失败时自动切换到Whisper

### 识别流程

```python
async def recognize(self, audio_file: str) -> Optional[str]:
    # 1. 优先使用FunASR
    if self.use_funasr:
        result = await self._funasr_recognize(audio_file)
        if result:
            return result
    
    # 2. 使用Whisper作为备用
    if self.whisper_model is not None:
        result = await self._whisper_recognize(audio_file)
        if result:
            return result
    
    # 3. 最后使用模拟识别
    return await self._mock_recognize(audio_file)
```

## 故障排除

### 1. 连接问题

**问题**: 无法连接到FunASR服务器
**解决方案**:
1. 检查服务器地址是否正确
2. 确认FunASR服务是否正在运行
3. 检查网络连接和防火墙设置
4. 验证端口10096是否开放

### 2. 识别问题

**问题**: 语音识别结果为空
**解决方案**:
1. 检查音频质量和音量
2. 确认麦克风权限设置
3. 查看FunASR服务器日志
4. 尝试使用测试脚本验证

### 3. 性能问题

**问题**: 识别速度慢
**解决方案**:
1. 检查网络延迟
2. 调整FUNASR_TIMEOUT参数
3. 优化音频数据传输格式

## 配置参数

### 音频参数
```python
SAMPLE_RATE = 16000      # 采样率
CHUNK_SIZE = 1024        # 音频块大小
CHANNELS = 1             # 声道数
FORMAT = 'int16'         # 音频格式
```

### FunASR参数
```python
FUNASR_WS_URL = "ws://************:10096/"  # WebSocket地址
FUNASR_TIMEOUT = 10      # 连接超时时间
```

### 录音参数
```python
RECORD_TIMEOUT = 5       # 录音超时时间(秒)
SILENCE_THRESHOLD = 500  # 静音阈值
SILENCE_DURATION = 2     # 静音持续时间(秒)
```

## 日志和调试

系统会输出详细的日志信息：

```
INFO - 使用FunASR进行语音识别
INFO - FunASR识别结果: 查询今天的警报
WARNING - FunASR识别失败，尝试Whisper
INFO - Whisper识别结果: 显示统计信息
```

## 注意事项

1. **网络依赖**: FunASR需要网络连接到服务器
2. **音频格式**: 确保音频格式与FunASR服务器兼容
3. **并发限制**: 注意FunASR服务器的并发连接限制
4. **数据安全**: 语音数据通过网络传输，注意安全性

## 更新日志

### v1.0.0
- 集成FunASR WebSocket支持
- 实现FunASR优先的识别策略
- 添加自动降级机制
- 完善错误处理和日志记录

## 技术支持

如果遇到问题，请：
1. 运行测试脚本: `python test_funasr.py`
2. 检查日志输出
3. 确认FunASR服务器状态
4. 验证网络连接
