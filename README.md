# AI安全监控系统 (AI Security Monitoring System)

一个基于AI的智能安全监控系统，集成了视频分析、语音助手、异常检测等功能。

## 项目结构

```
aiWatchdog0703/
├── main.py                 # 主启动文件
├── requirements.txt        # Python依赖
├── README.md              # 项目说明
│
├── configs/               # 配置文件目录
│   ├── app_config.yaml    # 应用主配置
│   └── voice_config.yaml  # 语音助手配置
│
├── src/                   # 源代码目录
│   ├── __init__.py
│   ├── core/              # 核心模块
│   │   ├── __init__.py
│   │   ├── config.py      # 原配置文件（兼容性）
│   │   ├── config_loader.py # YAML配置加载器
│   │   └── custom_models.json
│   ├── api/               # API和RAG模块
│   │   ├── __init__.py
│   │   ├── rag_query.py
│   │   ├── rag_query_simple.py
│   │   └── prompt.py
│   ├── voice/             # 语音助手模块
│   │   ├── __init__.py
│   │   ├── voice_api.py
│   │   ├── voice_assistant.py
│   │   ├── voice_controller.py
│   │   ├── start_voice_assistant.py
│   │   └── start_enhanced_voice_assistant.py
│   ├── video/             # 视频处理模块
│   │   ├── __init__.py
│   │   └── video_server.py
│   └── models/            # 模型管理模块
│       ├── __init__.py
│       ├── multi_modal_analyzer.py
│       └── custom_model_manager.py
│
├── utils/                 # 工具模块
│   ├── __init__.py
│   ├── alert_statistics.py
│   └── utility.py
│
├── web/                   # 前端代码
│   ├── templates/         # HTML模板
│   │   ├── index.html
│   │   ├── config.html
│   │   ├── login.html
│   │   ├── voice.html
│   │   ├── voice_assistant.html
│   │   ├── assistant.html
│   │   └── auto_play_rules.html
│   └── static/            # 静态资源
│       ├── js/            # JavaScript文件
│       │   ├── main.js
│       │   ├── websocket.js
│       │   ├── auth.js
│       │   ├── storage.js
│       │   └── config.js
│       ├── css/           # CSS样式文件
│       │   ├── style.css
│       │   └── login.css
│       └── assets/        # 其他静态资源
│           └── alert.mp3
│
├── test/                  # 测试文件
│   ├── test.mp4
│   ├── test1.mp4
│   ├── test2.mp4
│   ├── test3.mp4
│   ├── test_funasr.py
│   ├── test_funasr_correct_protocol.py
│   ├── test_funasr_enhanced.py
│   ├── test_funasr_final.py
│   ├── test_funasr_parameters.py
│   ├── test_funasr_protocol.py
│   ├── debug_funasr.py
│   ├── funasr_compatibility_test.py
│   └── validate_html.py
│
├── data/                  # 数据存储目录
│   ├── uploads/           # 上传文件
│   ├── video_warning/     # 警告视频
│   ├── voice/             # 语音数据
│   │   ├── conversations/ # 对话记录
│   │   ├── temp/          # 临时文件
│   │   └── tts_cache/     # TTS缓存
│   └── archive/           # 归档文件
│
├── logs/                  # 日志文件
│   ├── app.log           # 应用日志
│   ├── code.log          # 代码日志
│   └── voice_assistant.log # 语音助手日志
│
└── docs/                  # 文档目录
    ├── readme.md
    ├── README_EN.md
    ├── CONFIG_USAGE.md
    ├── AI安全监控软件APP开发方案.md
    ├── FUNASR_FINAL_GUIDE.md
    ├── FUNASR_INTEGRATION_SUMMARY.md
    ├── FUNASR_SETUP_GUIDE.md
    ├── FUNASR_TROUBLESHOOTING.md
    ├── VOICE_ASSISTANT_ENHANCEMENT_SUMMARY.md
    └── VOICE_ASSISTANT_README.md
```

## 功能特性

- **视频监控**: 实时视频流分析和异常检测
- **多模型支持**: 支持多种AI检测模型（入侵检测、安全帽检测、口罩检测等）
- **语音助手**: 基于FunASR的语音识别和对话功能
- **Web界面**: 直观的Web管理界面
- **RAG系统**: 知识库问答系统
- **实时警报**: 异常情况实时通知

## 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置系统

编辑 `configs/app_config.yaml` 和 `configs/voice_config.yaml` 文件，根据您的环境调整配置。

### 3. 启动系统

```bash
python main.py
```

### 4. 访问Web界面

打开浏览器访问: `http://localhost:16532`

## 配置说明

### 应用配置 (configs/app_config.yaml)

- `video`: 视频源和处理配置
- `models`: AI模型配置
- `api`: API服务配置
- `rag`: RAG系统配置
- `server`: 服务器配置
- `storage`: 存储路径配置
- `logging`: 日志配置

### 语音配置 (configs/voice_config.yaml)

- `funasr`: FunASR语音识别配置
- `wake_words`: 唤醒词设置
- `tts`: 文本转语音配置
- `conversation`: 对话管理配置
- `hardware`: 硬件设备配置

## 开发指南

### 添加新的检测模型

1. 在 `configs/app_config.yaml` 中添加模型定义
2. 在 `src/models/` 中实现模型逻辑
3. 更新Web界面配置

### 扩展语音功能

1. 修改 `configs/voice_config.yaml` 配置
2. 在 `src/voice/` 中添加新功能
3. 更新语音助手界面

## 许可证

[添加许可证信息]

## 贡献

欢迎提交Issue和Pull Request来改进项目。
